{% extends '@!EasyAdmin/crud/detail.html.twig' %}

{% block head_stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('announcementObservations') }}
{% endblock %}


{% block content_title %}
    <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\AnnouncementCrudController').setAction('detail').setEntityId(announcementObservation.announcement.id).set('tab','observations') }}" class="card-link">
        {{ 'announcements.label_in_singular'|trans({}, 'messages') }}  : {{ announcementObservation.announcement.course.name }}
    </a>
{% endblock %}

{% block content %}
    <div id="app">{{ parent() }}</div>
{% endblock %}

{% block main %}
    <div class="col-12 card">
        <div class="card-body">
            <div class="col-12 d-flex flex-row flex-wrap">
                <div class="col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.provider_cost'|trans({}) }}</b>
                    <span>{{ announcementObservation.providerCost|number_format(2, '.', ',') }}</span>
                </div>
                <div class="col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.hedima_management_cost'|trans({}) }}</b>
                    <span>{{ announcementObservation.hedimaManagementCost|number_format(2, '.', ',') }}</span>
                </div>
                <div class="col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.travel_and_maintenance_cost'|trans({}) }}</b>
                    <span>{{ announcementObservation.travelAndMaintenanceCost|number_format(2, '.', ',') }}</span>
                </div>
                <div class="col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.total_cost'|trans({}) }}</b>
                    <span>{{ announcementObservation.totalCost|number_format(2, '.', ',') }}</span>
                </div>
                <div class="col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.final_pax'|trans({}) }}</b>
                    <span>{{ announcementObservation.finalPax|number_format(2, '.', ',') }}</span>
                </div>
                <div class="col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.maximum_bonus'|trans({}) }}</b>
                    <span>{{ announcementObservation.maximumBonus|number_format(2, '.', ',') }}</span>
                </div>
                <div class="col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.subsidized_amount'|trans({}) }}</b>
                    <span>{{ announcementObservation.subsidizedAmount|number_format(2, '.', ',') }}</span>
                </div>
                <div class="col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.private_amount'|trans({}) }}</b>
                    <span>{{ announcementObservation.privateAmount|number_format(2, '.', ',') }}</span>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 card mt-1">
        <div class="card-body">
            <div class="col-12 d-flex flex-row flex-wrap">
                <div class="col-2 d-flex flex-column">
                    <b>{{ 'announcements.observations.comunicado_fundae'|trans({}) }}</b>
                    <span>{{ announcementObservation.comunicadoFundae ? 'Yes'|trans({}) : 'No'|trans({}) }}</span>
                </div>
                <div class="col-2 d-flex flex-column">
                    <b>{{ 'announcements.observations.comunicado_abilitia'|trans({}) }}</b>
                    <span>{{ announcementObservation.comunicadoAbilitia ? 'Yes'|trans({}) : 'No'|trans({}) }}</span>
                </div>
                <div class="col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.economic_module'|trans({}) }}</b>
                    <span>{{ announcementObservation.economicModule }}</span>
                </div>
                <div class="col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.travel_and_maintenance'|trans({}) }}</b>
                    <span>{{ announcementObservation.travelAndMaintenance }}</span>
                </div>

                <div class="col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.provider_invoice_number'|trans({}) }}</b>
                    <span>{{ announcementObservation.providerInvoiceNumber }}</span>
                </div>
                <div class="col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.hedima_management_invoice_number'|trans({}) }}</b>
                    <span>{{ announcementObservation.hedimaManagementInvoiceNumber }}</span>
                </div>
                <div class="form-group col-4 d-flex flex-column">
                    <b>{{ 'announcements.observations.invoice_status'|trans({}) }}</b>
                    <span>{{ announcementObservation.invoiceStatus }}</span>
                </div>
                <div class="form-group col-12 d-flex flex-column">
                    <b>{{ 'announcements.observations.course_status'|trans({}) }}</b>
                    <span>{{ announcementObservation.courseStatus }}</span>
                </div>

                <div class="form-group col-12 d-flex flex-column">
                    <b>{{ 'announcements.observations.observations'|trans({}) }}</b>
                    {{ announcementObservation.observations|raw }}
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 mt-3">
        {% if 'ROLE_SUBSIDIZER' not in app.user.roles %}
        <div class="col-12">
            <button type="button" class="btn btn-primary" @click="showSelectionFileModal()">{{ 'observations.addfiles'|trans({}) }}</button>
        </div>
        {% endif %}

        <div class="d-flex align-items-center justify-content-center" v-if="isLoadingFiles">
            <loader :is-loaded="isLoadingFiles"></loader>
        </div>
        <table class="table mt-2" v-else>
            <thead>
            <tr>
                <th>{{ 'documentation.file'|trans({}) }}</th>
                <th>{{ 'material_course.configureFields.type'|trans({}) }}</th>
                <th>{{ 'common_areas.created_at'|trans({}) }}</th>
                <th class="text-right">{{ 'common_areas.actions'|trans({}) }}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="file in files" :key="file.id">
                <td>${ file.originalName }</td>
                <td>${ file.mimeType }</td>
                <td>${ file.createdAt }</td>
                <td class="text-right">
                    <button type="button" class="btn btn-secondary btn-sm"
                            data-bs-toggle="modal"
                            :data-bs-target="`#visor-material_${file.id}`"
                    ><i class="fa fa-eye"></i></button>
                    {% if 'ROLE_SUBSIDIZER' not in app.user.roles %}
                    <button type="button" class="btn btn-danger btn-sm" @click="deleteAnnouncementObservationDocument(file)"><i class="fa fa-trash"></i></button>
                    {% endif %}
                    <div
                            class="modal fade"
                            :id="`visor-material_${file.id}`"
                            tabindex="-1"
                            :aria-labelledby="`visor-material_${file.id}`"
                            aria-hidden="true"
                    >
                        <div class="modal-dialog modal-xl">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="exampleModalLabel">
                                        ${ file.originalName }
                                    </h5>
                                    <button
                                            type="button"
                                            class="btn-close btn-close-white"
                                            data-bs-dismiss="modal"
                                            aria-label="Close"
                                    ></button>
                                </div>
                                <div class="modal-body body-visor">
                                    <component
                                            :is="fileVisorComponent(file)"
                                            :name="file.filename"
                                            route-base="uploads/files/announcement_observation/"
                                            :key="file.id"
                                            identifierVideo="undefined"
                                            urlMaterial="undefined"
                                            codeVideo="undefined"
                                            :autoplay="false"
                                    ></component>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="observation-files" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ 'taskCourse.configureFields.addFile'|trans({}) }}</h5>
                    <button type="button" class="btn-close btn-close-white" data-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col-12">
                        <form action="" @submit.prevent id="form-upload-observation-file" v-show="!uploadingFiles">
                            <div class="col-12">
                                <label>{{ 'material_course.configureFields.type'|trans({}) }}</label>
                                <select class="custom-select" v-model="selectedFileType">
                                    <option v-for="type in allowedFileTypes" :key="type.type" :value="type">${ type.name }</option>
                                </select>
                            </div>
                            <div class="col-12 form-group">
                                <label>{{ 'material_course.placeholder.file'|trans({}) }}</label>
                                <input name="file" id="file" type="file"
                                       class="form-control"
                                       :accept="selectedFileType.accept"
                                       :multiple="selectedFileType.multiple">
                            </div>
                            <div class="col-12 d-flex align-items-center justify-content-end">
                                <button type="button" class="btn btn-primary" @click="submit()">{{ 'common_areas.save'|trans({}) }}</button>
                            </div>
                        </form>
                        <div class="col-12 d-flex align-items-center justify-content-center" v-if="uploadingFiles">
                            <spinner></spinner>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block body_javascript %}
    <script>
        let observationId = {{ announcementObservation.id | json_encode | raw }};
        let confirmFileUploadText = '{{ 'common_areas.confirm_file_upload'|trans({}) }}';
        let confirmFileDelete = '{{ 'common_areas.confirm_file_delete'|trans({}) }}';
    </script>
    {{ parent() }}
    {{ encore_entry_script_tags('announcementObservations') }}
{% endblock %}
