<div class="content-panel pb-3">
  <div class="page-actions pt-3 pr-3 text-right">
    {# <a class="action-new btn btn-primary" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\MaterialCourseCrudController').setAction('new').set('courseId', course.id).set('referrer', referrerAnnouncement) }}">{{ 'course.configureFields.add_material'|trans({}) }}</a> #}

    {% if (announcement.finishAt|date('Y-m-d H:i:s')) > ('now'|date('Y-m-d H:i:s')) %}
      <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#materialCourseModal">{{ 'course.configureFields.add_material'|trans({}) }}</button>
    {% endif %}
  </div>

  <div id="materialCourse">
    <list-material-course :id-announcement="{{ announcement.id }}"></list-material-course>   
    {% include 'admin/announcement/materials/add_material.html.twig' %}
  </div>  
</div>
