{#{% extends '@!EasyAdmin/crud/index.html.twig' %}#}
{#{% extends '@!EasyAdmin/page/content.html.twig' %}#}
{#{% block page_title %}#}
{#    Library#}
{#{% endblock %}#}
{% extends 'base_app.html.twig' %}

{% block head_stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('library_app') }}
{% endblock %}

{#{% block content %}#}
{#<div id="app"#}
{#     class="VueApp"#}
{#     locales="{{ locales | json_encode }}"#}
{#     default-locale="{{ defaultLocale }}"#}
{#     user-locale="{{ userLocale }}"#}
{#     activeRoute="{{ activeRoute }}"#}
{#     activeParams="{{ activeParams }}"#}
{#     routeStatus="{{ routeStatus }}"#}
{#     config="{{ config }}"#}
{#>#}
{#    {{ parent() }}#}
{#    <div id="roles" roles="{{ user.roles | json_encode }}"></div>#}
{#    <div id="froala_key" froala-key="{{ froalaKey }}"></div>#}
{#</div>#}
{#{% endblock content %}#}

{#{% block content_title %}#}
{#    <content-title></content-title>#}
{#{% endblock content_title %}#}

{#{% block page_actions %}#}
{#    <action-category-view v-if="currentRouteName === 'CategoryView'"></action-category-view>#}
{#    <page-actions></page-actions>#}
{#{% endblock page_actions %}#}
{#{% block main %}#}
{#    <div class="row p-0 m-0">#}
{#        <div class="col-12  p-0 m-0">#}
{#            <library-app></library-app>#}
{#        </div>#}
{#    </div>#}
{#{% endblock %}#}

{% block body_javascript %}
    {{ parent() }}
    {{ encore_entry_script_tags('library_app') }}
{% endblock %}
