{% extends '@!EasyAdmin/crud/edit.html.twig' %}

{% block head_stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('user') }}
{% endblock head_stylesheets %}


{% block content %}
    <div id="app"
         enable-filters="{{ enableUserFilters  }}"
         user-filters="{{ (user_use_filters ? userFilters : []) | json_encode }}">{{ parent() }}</div>
{% endblock content %}

{% block main %}
    <div class="row">
        <div class="parent-data">
            {{ parent() }}
        </div>
        {% if user_use_filters %}
            <div class="field-form_panel col-xs-12 col-md-12">
                <div class="form-panel-header">
                    <div class="form-panel-title">
                        <a href="#" class="not-collapsible">{{ 'filter.label_in_plural' |trans({}, 'messages',  app.user.locale)}}</a>
                    </div>
                </div>
                <div class="form-panel-body show">
                    <div class="row justify-content-center" v-show="filtersRequired" id="filtersRequiredAlert">
                        <div class="col-md-6">
                            <p class="alert alert-danger">
                                {{ 'user.filtersRequired' |trans({}, 'messages',  app.user.locale)}}
                            </p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <category-filter :save-in-realtime="false"
                                             :extern-selected-filters="filters"
                                             filters-add-all="{{ 'filters.add_all' |trans({}, 'messages', app.user.locale)|raw }}"
                                             filters-remove-all="{{ 'filters.remove_all' |trans({}, 'messages', app.user.locale)|raw }}"
                                             filters-search="{{ 'filters.placeholder' |trans({}, 'messages', app.user.locale)|raw }}"
                                             @on-local-filter-updated="onFilterUpdated"></category-filter>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
{% endblock main %}

{% block body_javascript %}
    {{ parent() }}
    {{ encore_entry_script_tags('user') }}
{% endblock body_javascript %}
