{% extends '@!EasyAdmin/page/content.html.twig' %}

{% block head_stylesheets %}
    {{ parent() }}
{% endblock %}

{% block page_title %}
{{ 'user.configureFields.courses'|trans({}, 'messages',  app.user.locale) }} {{ userCourse.course.name }}
    / {{ userCourse.user.fullName }}
{% endblock page_title %}

{% block page_actions %}
    <a class="action-detail btn btn-secondary"
       href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\UserCrudController').setAction('detail').setEntityId(userCourse.user.id) }}">{% trans %}Back to user{% endtrans %}</a>
{% endblock %}

{% block main %}
    <div id="subsidizer-user">
        <div class="content-panel mt-2 p-3">
            <h3>{{ userCourse.user.fullName }}</h3>
            <div class="p-4">
                <div class="row">
                    {% if announcementUser is not null %}
                        <div class="col-md-4">
                            <h5>Announcement</h5>
                            <div class="mb-3">
                                <strong>{% trans %}Notified at{% endtrans %}:</strong>
                                {{ announcementUser.notified | date('Y-m-d H:i') }}
                            </div>
                            <div class="mb-3">
                                <strong>{% trans %}Start at{% endtrans %}:</strong>
                                {{ announcementUser.announcement.startAt | date('Y-m-d H:i') }}
                            </div>
                            <div class="mb-3">
                                <strong>{% trans %}Finish at{% endtrans %}:</strong>
                                {{ announcementUser.announcement.finishAt | date('Y-m-d H:i') }}
                            </div>
                            <div class="mb-3">
                                <strong>{% trans %}Tutor{% endtrans %}:</strong>
                                {{ announcementUser.announcement.tutor.fullName }}
                            </div>
                        </div>
                    {% endif %}

                    <div class="col-md-4">
                        <div class="mb-3">
                            <strong>{{ 'user.configureFields.started_at'|trans({}, 'messages',  app.user.locale) }}:</strong>
                            {{ userCourse is not null and userCourse.startedAt ? userCourse.startedAt|date('Y-m-d H:i') : '-' }}
                        </div>

                        <div class="mb-3">
                            <strong>{{ 'user.configureFields.finished_at'|trans({}, 'messages',  app.user.locale) }}:</strong>
                            {{ userCourse is not null and userCourse.finishedAt ? userCourse.finishedAt|date('Y-m-d H:i') : '-' }}
                        </div>
                        <div class="mb-3">
                            <strong>{{ 'user.configureFields.time_spent'|trans({}, 'messages',  app.user.locale) }}:</strong>
                            {{ userChapters | propertySum('timeSpent') | niceTime }}

                        </div>

                        <div class="mb-3">
                            <strong>{{ 'user.configureFields.content_viewed'|trans({}, 'messages',  app.user.locale) }}:</strong>
                            {{ userCourse.course.chapters | length ? ((userChapters | length / userCourse.course.chapters | length * 100) | round) : 0 }}
                            %
                            ({{ userChapters | length }}/{{ userCourse.course.chapters | length }})
                        </div>

                        <div class="mb-3">
                            <strong>{{ 'user.configureFields.interaction_with_teacher'|trans({}, 'messages',  app.user.locale) }}</strong>
                            {{ messages | length }}
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="mb-3">
                            <strong>{% trans %}Apt{% endtrans %}:</strong>
                            {% if userCourse is not null and userCourse.finishedAt %}
                                <span class="fas fa-check-circle text-success"></span>
                            {% else %}
                                <span class="fas fa-times-circle text-danger"></span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="content-panel p-3">
            <h4>{{ 'user.configureFields.course_content'|trans({}, 'messages',  app.user.locale) }}</h4>
            <table class="table datagrid with-rounded-top ">
                <thead class="thead-light">
                <tr>
                    <th><span>{{ 'user.configureFields.chapter'|trans({}, 'messages',  app.user.locale) }}</span></th>
                    <th><span>{{ 'user.configureFields.content_type'|trans({}, 'messages',  app.user.locale) }}</span></th>
                    <th><span>{{ 'user.configureFields.started_at'|trans({}, 'messages',  app.user.locale) }}</span></th>
                    <th><span>{{ 'user.configureFields.finished_at'|trans({}, 'messages',  app.user.locale) }}</span></th>
                    <th><span>{{ 'user.configureFields.time_spent'|trans({}, 'messages',  app.user.locale) }}</span></th>
                    <th class="text-center"><span>{{ 'user.configureFields.finished'|trans({}, 'messages',  app.user.locale) }}</span></th>
                    <th class="text-center"><span></span></th>
                </tr>
                </thead>

                <tbody>
                {% for chapter in userCourse.course.chapters %}
                    <tr>
                        <td>
                            {{ chapter.position }}.
                            {{ chapter.title }}
                        </td>
                        <td>
                            {{ chapter.type }}
                        </td>
                        <td>
                            {% if userChapters[chapter.id] is defined %}
                                {{ userChapters[chapter.id].startedAt | date('Y-m-d H:i') }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if userChapters[chapter.id] is defined and userChapters[chapter.id].finishedAt is not null %}
                                {{ userChapters[chapter.id].finishedAt | date('Y-m-d H:i') }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if userChapters[chapter.id] is defined %}
                                {{ userChapters[chapter.id].timeSpent | niceTime }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if userChapters[chapter.id] is defined and userChapters[chapter.id].finishedAt is not null %}
                                <span class="fas fa-check-circle text-success"></span>
                            {% else %}
                                <span class="fas fa-times-circle text-danger"></span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if(chapter.type.id in resultsTypes and userChapters[chapter.id] is defined and userChapters[chapter.id].data is not null) %}
                                <a href="javascript:void(0)"
                                   v-on:click="showResults({{ userChapters[chapter.id].id }}, '{{ chapter.type | slug | lower }}')">
                                    <span class="fas fa-eye"></span>
                                </a>
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="content-panel p-3">
            <h4>{{ 'user.configureFields.teacher_interaction'|trans({}, 'messages',  app.user.locale) }}</h4>
            <table class="table datagrid with-rounded-top ">
                <thead class="thead-light">
                <tr>
                    <th><span>{{ 'user.configureFields.date'|trans({}, 'messages',  app.user.locale) }}</span></th>
                    <th><span>{{ 'user.configureFields.sender'|trans({}, 'messages',  app.user.locale) }}</span></th>
                    <th><span>{{ 'user.configureFields.recipient'|trans({}, 'messages',  app.user.locale) }}</span></th>
                    <th><span>{{ 'user.configureFields.subject'|trans({}, 'messages',  app.user.locale) }}</span></th>
                    <th></th>
                </tr>
                </thead>

                <tbody>
                {% for message in messages %}
                    <tr>
                        <td>
                            {{ message.sentAt | date('Y-m-d H:i') }}
                        </td>
                        <td>
                            {{ message.sender.fullName }}
                        </td>
                        <td>
                            {{ message.recipient.fullName }}
                        </td>
                        <td>{{ message.subject }}</td>
                        <td>
                            <a href="javascript:void(0)"
                               data-toggle="modal" data-target="#message-modal"
                               data-message-id="{{ message.id }}"><span class="fas fa-eye"></span></a>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="modal fade" id="message-modal" tabindex="-1" aria-labelledby="message-modal-label"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="message-modal-label">{% trans %}Message{% endtrans %}</h5>
                        <button type="button" class="close" data-dismiss="modal"
                                aria-label="{% trans %}Close{% endtrans %}">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        ...
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                                data-dismiss="modal">{% trans %}Close{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="results-modal" tabindex="-1" aria-labelledby="results-modal-label"
             aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="message-modal-label">{% trans %}Results{% endtrans %}</h5>
                        <button type="button" class="close" data-dismiss="modal"
                                aria-label="{% trans %}Close{% endtrans %}">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <quiz-results :quiz="quiz" :number="index+1" v-for="(quiz, index) in quizzes"
                                      :key="'quiz'+index"></quiz-results>
                        <roulette-results :questions="roulette" v-show="roulette.length"></roulette-results>
                        <roulette-words-results :questions="rouletteWords" v-show="rouletteWords.length"></roulette-words-results>
                        <puzzle-results :puzzle="puzzle" :number="index+1" v-for="(puzzle, index) in puzzles"
                                        :key="'puzzle'+index"></puzzle-results>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                                data-dismiss="modal">{% trans %}Close{% endtrans %}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% endblock main %}

{% block body_javascript %}
    {{ parent() }}
    {{ encore_entry_script_tags('subsidizerChapterInfo') }}
    {{ encore_entry_script_tags('subsidizerMessage') }}
{% endblock %}
