{% extends '@!EasyAdmin/crud/detail.html.twig' %}
{% set user = entity.instance %}
{% set roles = [] %}
{% for role in user.roles %}
	{% set roles = roles|merge([roleNames[role]]) %}
{% endfor %}

{% block content_title %}
	{{ user.fullName }}
{% endblock content_title %}

{% block head_stylesheets %}
	{{ parent() }}
	{{ encore_entry_link_tags('userDetail') }}
{% endblock %}

{% block main %}
	<div id="user-stats" v-cloak locale="{{ app.user.locale }}">
		<div class="card mb-4">
			<div class="row p-3 m-0">
				<div class="col-md-4 col-sm-12 user-details-card">
					<div class="user-image">
						{% if user.avatar %}
							<a href="#" class="ea-lightbox-thumbnail" data-featherlight="#ea-lightbox-{{ user.id }}" data-featherlight-close-on-click="anywhere">
								<img src="{{ vich_uploader_asset(user, 'avatarFile') }}" class="card-img" alt="{{ user.fullName }} image">
							</a>
							<div id="ea-lightbox-{{ user.id }}" class="ea-lightbox">
								<img src="{{ vich_uploader_asset(user, 'avatarFile') }}" alt="">
							</div>
						{% else %}
							<img src="{{ avatar_uploads_path }}/default.svg" class="card-img" alt="{{ user.fullName }} image">
						{% endif %}
					</div>
					<div>
						<p class="pt-1 font-weight-bold fs-5 my-0">{{ user.fullName }}</p>
						<p class="pt-1 mb-2">{{ roles|map(role => role|trans)|join(', ', ' y ') }}</p>
						<a class="text-primary" href="mailto:{{ user.email }}" target="_blank">
							{{ user.email }}
						</a>
						<div class="mt-2">
							{{ 'user.configureFields.open'|trans({}, 'messages',  app.user.locale) }}
							<i class="fa fa-check-circle campusIcon ml-1 {% if user.open and openCourse %}checked{% endif %}"></i>
						</div>
						<div style="margin-top: 10px">
							<button data-bs-toggle="modal" data-bs-target="#userDiplomasModal" class="btn btn-primary">{{ 'user.diploma.generate' | trans({}, 'messages', app.user.locale)}}</button>
						</div>
					</div>
				</div>
				<div class="col-md-8 col-sm-12 nav-tabs-blue">
					<ul class="nav nav-tabs ml-2">
						<li class="nav-item">
							<button class="nav-link" :class="activePane === 'filter1' ? 'active' : ''" id="filter1-tab" @click="activePane = 'filter1'">
								<i class="fa fa-sliders"></i>
								${ $t('USERS.FILTER_TAB1') }
							</button>
						</li>
						{% if excludedFilters %}
							<li class="nav-item">
								<button class="nav-link" :class="activePane === 'filter2' ? 'active' : ''" id="filter2-tab" @click="activePane = 'filter2'">
									<i class="fa fa-filter"></i>
									${ $t('USERS.FILTER_TAB2') }
								</button>
							</li>
						{% endif %}
					</ul>
					<div class="tab-content">
						<div class="tab-pane p-3 fade config" :class="activePane === 'filter1' ? 'active show' : ''">
							{% for filtercategory in filter_categories %}
								<p class="font-weight-bold mb-0 mt-3">{{ filtercategory.name }}</p>
								{% for subFilter in filtercategory.filters %}
									<span class="badge badge-light"> {{ subFilter.name }} </span>
								{% endfor %}
							{% endfor %}
						</div>
						<div class="tab-pane p-3 fade config" :class="activePane === 'filter2' ? 'active show' : ''">
							{% set lastFilter = '' %}
							{% if excludedFilters %}
								<div>
									{% for filter in excludedFilters %}
										<p class="font-weight-bold {% if lastFilter is same as '' %}my-0{% else %}mb-0 mt-3{% endif %}">{{ filter.filterCategory.name }}</p>
										<span class="badge badge-light">
											{{ filter.name }}
										</span>
									{% endfor %}
								</div>
							{% endif %}
						</div>
					</div>


					{% if user_use_extra_fields %}
						<div class="row">
							<div class="col-md-3 font-weight-bold">
								{{ 'user.configureFields.country'|trans({}, 'messages',  app.user.locale) }}:
							</div>
							<div class="col-md-9">
								{{ user.extra.country }}
							</div>
						</div>
						<div class="row">
							<div class="col-md-3 font-weight-bold">
								{{ 'user.configureFields.gender'|trans({}, 'messages',  app.user.locale) }}:
							</div>
							<div class="col-md-9">
								{{ user.extra.gender }}
							</div>
						</div>
						<div class="row">
							<div class="col-md-3 font-weight-bold">
								{{ 'user.configureFields.category'|trans({}, 'messages',  app.user.locale) }}:
							</div>
							<div class="col-md-9">
								{{ user.extra.category }}
							</div>
						</div>
						<div class="row">
							<div class="col-md-3 font-weight-bold">
								{{ 'user.configureFields.departament'|trans({}, 'messages',  app.user.locale) }}:
							</div>
							<div class="col-md-9">
								{{ user.extra.department }}
							</div>
						</div>
						<div class="row">
							<div class="col-md-3 font-weight-bold">
								{{ 'user.configureFields.center'|trans({}, 'messages',  app.user.locale) }}:
							</div>
							<div class="col-md-9">
								{{ user.extra.center }}
							</div>
						</div>
						<div class="row">
							<div class="col-md-3 font-weight-bold">
								{{ 'user.configureFields.birthdate'|trans({}, 'messages',  app.user.locale) }}
							</div>
							<div class="col-md-9">
								{{ user.extra.birthdate | date('Y/m/d') }}
							</div>
						</div>
					{% endif %}

				</div>
			</div>
		</div>

		<div class="row mb-4">
			<div class="d-flex col-12 align-items-center justify-content-between flex-wrap">
				<p class="font-weight-bold fs-4">
					{{ 'itinerary.total_courses'|trans({}, 'messages',  app.user.locale) }}
				</p>
				<excel-generator v-show="!loadingPage" :options="excelData" text="{{ 'stats.export_title'|trans({}, 'messages', app.user.locale) }}"></excel-generator>
			</div>
			<div class="cards-info-container">
				<div class="cards-info" v-if="courseSeries.length > 0">
					<div class="card-info-header bg-primary py-2 px-3">
						<i class="fa fa-graduation-cap"></i>
						<b class="number">${courseSeries[1].total}</b>
						{{ dynamicTitles["USERS.CARD_TITLE1"] }}
					</div>
					<div class="card-info-body px-3">
						<pie-chart class="chart" :series-data="courseSeries[0]" :legend="false" inner-size="70%" :colors="['#1E88E5', '#FFB74D', '#ECEFF1']" :height="100"></pie-chart>
						<div class="card-info-legend pr-3">
							<p class="my-0">
								<i class="manually" style="background-color: #1E88E5;"></i>
								<span class="text-gray">
									<span class="value">${courseSeries[0][0].y}</span>
									<span class="text">${courseSeries[0][0].name}</span>
								</span>
							</p>
							<hr class="my-0">
							<p class="my-0">
								<i class="manually" style="background-color: #FFB74D;"></i>
								<span>
									<span class="value">${courseSeries[0][1].y}</span>
									<span class="text">${courseSeries[0][1].name}</span>
								</span>
							</p>
							<hr class="my-0">
							<p class="my-0">
								<i class="manually" style="background-color: #ECEFF1;"></i>
								<span>
									<span class="value">${courseSeries[0][2].y}</span>
									<span class="text">${courseSeries[0][2].name}</span>
								</span>
							</p>
						</div>
					</div>
				</div>
				{#    <div class="cards-info" v-if="chapterSeries.length > 0">
																				                    <div class="card-info-header bg-success py-2 px-3">
																				                        <i class="fa fa-graduation-cap"></i>
																				                        <b class="number">${chapterSeries[0].y + chapterSeries[1].y}</b>
																				                        ${ $t('USERS.CARD_TITLE2') }
																				                    </div>
																				                    <div class="card-info-body px-3">
																				                        <pie-chart class="chart" :series-data="chapterSeries" :legend="false"
																				                                   inner-size="70%" :colors="['#1E88E5', '#FFB74D', '#ECEFF1']"
																				                                   :height="100"></pie-chart>
																				                        <div class="card-info-legend pr-3">
																				                            <p class="my-0">
																				                                <i class="manually" style="background-color: #1E88E5;"></i>
																				                                <span class="text-gray">
																				                                    <span class="value">${chapterSeries[0].y}</span>
																				                                    <span class="text">${chapterSeries[0].name}</span>
																				                                </span>
																				                            </p>
																				                            <hr class="my-0">
																				                            <p class="my-0">
																				                                <i class="manually" style="background-color: #FFB74D;"></i>
																				                                <span>
																				                                    <span class="value">${chapterSeries[1].y}</span>
																				                                    <span class="text">${chapterSeries[1].name}</span>
																				                                </span>
																				                            </p>
																				                        </div>
																				                    </div>
																				                </div> #}

				<div class="cards-info" v-if="chapterSeries.length > 0">
					<div class="card-info-header bg-success py-2 px-3">
						<i class="fa fa-graduation-cap"></i>
						<b class="number">{{ courseVoluntary[1].y }}</b>
						{{ dynamicTitles["USERS.CARD_TITLE3"] }}
					</div>
					<div class="card-info-body px-3">
						<pie-chart class="chart" :series-data="{{ courseVoluntary[0] | json_encode }}" :legend="false" inner-size="70%" :colors="['#1E88E5', '#FFB74D', '#ECEFF1']" :height="100"></pie-chart>
						<div class="card-info-legend pr-3">
							<p class="my-0">
								<i class="manually" style="background-color: #1E88E5;"></i>
								<span class="text-gray">
									<span class="value">{{ courseVoluntary[0][0].y }}</span>
									<span class="text">
										{{ 'user.configureFields.courses_stats.finished'|trans({}, 'messages',  app.user.locale) }}</span>
								</span>
							</p>
							<hr class="my-0">
							<p class="my-0">
								<i class="manually" style="background-color: #FFB74D;"></i>
								<span>
									<span class="value">{{ courseVoluntary[0][1].y }}</span>
									<span class="text">{{ 'user.configureFields.courses_stats.started'|trans({}, 'messages',  app.user.locale) }}</span>
								</span>
							</p>
							<hr class="my-0">
							<p class="my-0">
								<i class="manually" style="background-color: #ECEFF1;"></i>
								<span>
									<span class="value">{{ courseVoluntary[0][2].y }}</span>
									<span class="text">{{ 'user.configureFields.courses_stats.notstarted'|trans({}, 'messages',  app.user.locale) }}</span>
								</span>
							</p>
						</div>
					</div>
				</div>

				<div class="cards-info bg-white">
					<div class="card-info-header bg-primary py-2 px-3">
						<i class="fa fa-clock"></i>
						${ $t('TOTAL_TIME') }
					</div>
					<div class="p-3 d-flex align-items-center h-100">
						{{ timeSpentFormation }}
					</div>
				</div>

			</div>
		</div>

		<div class="row mb-4">
			<p class="col-12 font-weight-bold fs-4">
				${ $t('USERS.GRAPHIC_TITLE') }
			</p>
			<div class="col-12">
				<master-detail-chart :is-loading="loadingLogins" :options="logins" title="Días de acceso" icon="fa fa-calendar-o" :hide-header="true"></master-detail-chart>
			</div>
		</div>

		<p class="col-12 font-weight-bold fs-4 px-0">
			{{ dynamicTitles["USERS.TABLE_TITLE1"] }} 
		</p>
		<div class="row mb-4 px-3">
			<ul class="nav nav-tabs">
				<li class="nav-item">
					<button class="nav-link" :class="activePaneAssignedTraining === 'itineraries' ? 'active' : ''" id="filter3-tab" @click="activePaneAssignedTraining = 'itineraries'">
						<i class="fa fa-layer-group"></i>
						${ $t('ITINERARY.HOME.TITLE') }
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link" :class="activePaneAssignedTraining === 'announcement' ? 'active' : ''" id="filter4-tab" @click="activePaneAssignedTraining = 'announcement'">
						<i class="fa fa-calendar-check"></i>
						${ $t('ANNOUNCEMENTS') }
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link" :class="activePaneAssignedTraining === 'filters' ? 'active' : ''" id="filter5-tab" @click="activePaneAssignedTraining = 'filters'">
						<i class="fa fa-tags"></i>
						${ $t('FILTERS') }
					</button>
				</li>
				<li class="nav-item ml-auto mr-0">
					<div class="dropdown">
						<button class="btn btn-outline dropdown-toggle bg-white" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							<i class="fa" :class="iconsFilter[filterTable1]"></i>
							${ statusFilter[filterTable1] }
						</button>
						<div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
							<button class="dropdown-item" @click="filterTable1 = 'finished'">
								<i class="fa" :class="iconsFilter.finished"></i>
								${ statusFilter.finished }
							</button>
							<button class="dropdown-item" @click="filterTable1 = 'inProcess'">
								<i class="fa" :class="iconsFilter.inProcess"></i>
								${ statusFilter.inProcess }
							</button>
							<button class="dropdown-item" @click="filterTable1 = 'noStarted'">
								<i class="fa" :class="iconsFilter.noStarted"></i>
								${ statusFilter.noStarted }
							</button>
							<button class="dropdown-item" @click="filterTable1 = 'all'">
								<i class="fa" :class="iconsFilter.all"></i>
								${ statusFilter.all }
							</button>
						</div>
					</div>
				</li>
			</ul>
			<div class="tab-content bg-white">
				<div class="tab-pane p-3 fade active show">
					<div class="mb-2" v-show="activePaneAssignedTraining === 'announcement'">
						{% for typeCourseIntern in  typeCoursesIntern %}
							<button class="btn btn-sm btn-outline" :class="{active: filterAnnouncement === '{{ typeCourseIntern.type }}'}" @click="filterAnnouncement = '{{ typeCourseIntern.type }}'">

								<span class="icon-merge">
									<i class="{{ typeCourseIntern.icon }}"></i>
									<i class="fa fa-check"></i>
								</span>

								{{ typeCourseIntern.name }}
							</button>
						{%  endfor %}


						<button class="btn btn-sm btn-outline" :class="{active: filterAnnouncement === 'all'}" @click="filterAnnouncement = 'all'">
							<i class="fa fa-layer-group"></i>
							${ $t('ALL') }
						</button>
					</div>
					<training-table :tag="`active-pane-${activePaneAssignedTraining}-${filterAnnouncement}-${filterTable1}`" :items="tableFiltered" :show-itinerary-type="activePaneAssignedTraining === 'itineraries'" @show-details="showDetails"></training-table>
				</div>
			</div>
		</div>

		<p class="col-12 font-weight-bold fs-4 px-0">
			{{ dynamicTitles["USERS.TABLE_TITLE2"] }}
		</p>
		<div class="row mb-4 px-3">
			<ul class="nav nav-tabs">
				<li class="nav-item">
					<button class="nav-link" :class="filterTable2 === 'finished' ? 'active' : ''" id="filter6-tab" @click="filterTable2 = 'finished'">
						<i class="fa" :class="iconsFilter.finished"></i>
						${ statusFilter.finished }
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link" :class="filterTable2 === 'inProcess' ? 'active' : ''" id="filter7-tab" @click="filterTable2 = 'inProcess'">
						<i class="fa no-spin" :class="iconsFilter.inProcess"></i>
						${ statusFilter.inProcess }
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link" :class="filterTable2 === 'all' ? 'active' : ''" id="filter8-tab" @click="filterTable2 = 'all'">
						<i class="fa" :class="iconsFilter.all"></i>
						${ statusFilter.all }
					</button>
				</li>
			</ul>

			<div class="tab-content bg-white">
				<div class="tab-pane p-3 fade active show">
					<training-table :tag="filterTable2" :items="tableFiltered2" :show-category="true" @show-details="showDetails"/>
				</div>
			</div>
		</div>
		<modal-course-details :course="itemSelected"></modal-course-details>
		<user-diplomas-modal :title="$t('ANNOUNCEMENT.DIPLOMAS_STUDENT')" :user-id="id" :user-name="fullName"></user-diplomas-modal>
		<base-User-modal-delete @delete-element="deleteElement"></base-User-modal-delete>

{% endblock main %}

{% block body_javascript %}
	{{ parent() }}
	<script type="text/javascript">
		let userId = {{ user.id }};
		let userEmail = '{{ user.email }}';
		let userFullName = '{{ user.fullName }}';
		const course_data = {
			id: 0,
			image: '',
			name: '',
			type: '',
		};
		const courseTranslations = {
			course_started_in_period_title: "{{ 'stats.export.filter.course_started_in_period_title'|trans({}, 'messages',  app.user.locale) }}",
			course_finished_in_period_title: "{{ 'stats.export.filter.course_finished_in_period_title'|trans({}, 'messages',  app.user.locale) }}",
			export_success: "{{ 'stats.export.export_success'|trans({}, 'messages',  app.user.locale) }}",
			export_error: "{{ 'stats.export.export_error'|trans({}, 'messages',  app.user.locale) }}",
			export_dir: "{{ 'stats.export.export_dir'|trans({}, 'messages',  app.user.locale) }}"
		}
		const assetsDir = "{{ asset('assets/chapters/') }}"
	</script>

	{{ encore_entry_script_tags('userDetail') }}
	{{ encore_entry_script_tags('user-impersonate') }} 
{% endblock %}
