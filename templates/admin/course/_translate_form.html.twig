<div id="modal-translate" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <h4>{{ 'course.configureFields.question_modal_translate'|trans({}, 'messages',  app.user.locale) }}</h4>
                <p>{{ 'course.configureFields.content_modal_translate'|trans({}, 'messages',  app.user.locale) }}</p>
                <div class="alert alert-warning" style="display: none;"></div>
                {{ form_start(translateForm, {'name': 'translate-form', 'method': 'POST'}) }}
                    <div class="form-group">
                        {{ form_label(translateForm.language) }}
                        <div class="form-widget">
                            {{ form_widget(translateForm.language) }}
                        </div>
                    </div>
                {{ form_end(translateForm) }}
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-secondary">
                    <span class="btn-label">{{ 'action.cancel'|trans([], 'EasyAdminBundle') }}</span>
                </button>

                <button type="button" data-dismiss="modal" class="btn btn-primary" id="modal-translate-button" formtarget="delete-form" disabled>
                    <span class="btn-label">{{ 'Translate'|trans }}</span>
                </button>
            </div>
        </div>
    </div>
</div>
