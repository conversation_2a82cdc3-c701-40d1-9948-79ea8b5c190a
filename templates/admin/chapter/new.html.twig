{% extends '@!EasyAdmin/crud/new.html.twig' %}
{% set default = ea_url().unsetAll().setController('App\\Controller\\Admin\\CourseCrudController').setAction('detail').setEntityId(course.id) %}
{% set referer = app.request.query.get('referrer') %}
{% set urls = referer|split('courseUrlRedirectNew') %}
{% set targetUrl = urls|length > 1 ? urls[1] : urls[0] %}

{% block content_title %}
  {% if referer %}
    <a href="{{ targetUrl }}"  class="card-link">{{ course.name }}</a> > {{ 'chapter.configureFields.create_chapter'|trans({}, 'chapters') }}
  {% else %}
    <a href="{{ default }}"  class="card-link">{{ course.name }}</a> > {{ 'chapter.configureFields.create_chapter'|trans({}, 'chapters') }}
  {% endif %}
{% endblock %}

{% block head_stylesheets %}
  {{ parent() }} {{ encore_entry_link_tags('chapterType') }}
{% endblock %}
{% block main %}
  {{ parent() }}

  <div>{{ include('admin/chapter/options_chapter.html.twig') }}</div>

  <div class="text-center">
    <button class="action-saveAndReturn btn btn-primary action-save btn-sm" type="submit" name="ea[newForm][btn]" value="saveAndReturn" data-action-name="saveAndReturn" form="new-Chapter-form"><span class="btn-label">{{ 'common_areas.save'|trans({}, 'messages') }}</span></button>
  </div>
{% endblock %}

{% block body_javascript %}
  <script>
    let modalChapter = false;
  </script>

  {{ encore_entry_script_tags('chapterType') }}
{% endblock %}
