<div class="content-panel p-3">
    <h4>{{ 'user.configureFields.time_title'|trans({}, 'messages',  app.user.locale) }}</h4>

    <table class="table datagrid with-rounded-top ">
        <thead class="thead-light">
        <tr>
            <th>
                <span>{{'user.configureFields.date'|trans({}, 'messages',  app.user.locale) }}</span>
            </th>
            <th>
                <span>{{ 'user.configureFields.time_spent'|trans({}, 'messages',  app.user.locale) }}</span>
            </th>

            <th class="text-center">
                <span></span>
            </th>
        </tr>
        </thead>

        <tbody>
        {% for times in timeByDay %}
            <tr>
                <td> {{ times.date |format_datetime('full', 'none', locale= app.user.locale )}}
                </td>
                <td> {{ times.total | niceTime }}
                </td>
                <td>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
</div>