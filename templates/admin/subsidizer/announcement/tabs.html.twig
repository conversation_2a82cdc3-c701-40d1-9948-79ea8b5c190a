<div class="col-md-9">
  <ul class="nav nav-tabs" id="courseTab" role="tablist">
    <li class="nav-item" role="presentation">
      <a class="nav-link active" id="course-contents-tab" data-toggle="tab" href="#announcement-course-contents" role="tab" aria-controls="course-contents" aria-selected="true">{{ 'announcements.configureFields.content_course'|trans({}, 'messages',  app.user.locale) }}</a>
    </li>
    <li class="nav-item" role="presentation">
      <a class="nav-link" id="tutor-tab" data-toggle="tab" href="#tutor" role="tab" aria-controls="tutor" aria-selected="false">{{ 'announcements.configureFields.tutor'|trans({}, 'messages',  app.user.locale) }}</a>
    </li>
    <li class="nav-item" role="presentation">
      <a class="nav-link" id="announcement-users-tab" data-toggle="tab" href="#announcement-users" role="tab" aria-controls="users" aria-selected="false">{{ 'user.label_in_plural'|trans({}, 'messages',  app.user.locale) }}</a>
    </li>
    <li class="nav-item" role="presentation">
      <a class="nav-link" id="nav-materials-tab" data-bs-toggle="tab" data-bs-target="#nav-materials" role="tab" aria-controls="nav-materials" aria-selected="false">{{ 'chapter.chapter.materials'|trans({}, 'chapters', app.user.locale) }}</a>
    </li>
    <li class="nav-item" role="presentation">
      <a class="nav-link" id="nav-tasks-tab" data-bs-toggle="tab" data-bs-target="#nav-tasks" role="tab" aria-controls="nav-tasks" aria-selected="false">{{ 'taskCourse.labelInPlural'|trans({}, 'messages', app.user.locale) }}</a>
    </li>
    <li class="nav-item" role="presentation">
      <a class="nav-link" id="nav-opinions-tab" data-bs-toggle="tab" data-bs-target="#nav-opinions" role="tab" aria-controls="nav-opinions" aria-selected="false">{{ 'announcements.configureFields.opinions'|trans({}, 'messages') }}</a>
    </li>
    <li class="nav-item" role="presentation">
      <a class="nav-link" id="nav-opinions-tab" data-bs-toggle="tab" data-bs-target="#nav-observations" role="tab" aria-controls="nav-observations" aria-selected="false">{{ 'announcements.observations.observations'|trans({}, 'messages') }}</a>
    </li>
  </ul>

  <div class="tab-content" id="courseTabContent">
    {% include 'admin/subsidizer/announcement/content_course.html.twig' %}
    {% include 'admin/subsidizer/announcement/infoTutor.html.twig' %}
    {% include 'admin/subsidizer/announcement/users_announcement.html.twig' %}
    {% include 'admin/subsidizer/announcement/materials.html.twig' %}
    {% include 'admin/subsidizer/announcement/tasks.html.twig' %}
    {% include 'admin/subsidizer/announcement/opinions.html.twig' %}
    {% include 'admin/subsidizer/announcement/observations.html.twig' %}

  </div>
</div>
