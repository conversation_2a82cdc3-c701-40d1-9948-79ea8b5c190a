{% block head_stylesheets %}
    {{ encore_entry_link_tags('uploadPuzzle') }}
    {{ encore_entry_link_tags('list-question-games') }}
{% endblock %}

{% block main %}
    {% if chapter.type == 'Puzzle' %}
        <div id="upload-puzzle">
            <upload-puzzle :id="{{ chapter.id }}"></upload-puzzle>
        </div>
    {% endif %}

    <div class="d-flex flex-row justify-content-between align-content-center w-100 pb-3">
        <div class="content-header-title d-flex flex-row align-items-center">
            <!--                                                <h1 class="title">
                {% if  chapter.getPuzzle  and  chapter.type == 'Puzzle' %}
                    {{ 'question.configureFields.image_for'|trans({}, 'messages',  app.user.locale) }} {{ chapter.title }}
                {% endif %}
            </h1>                                             -->
            {% if chapter.type == 'Quiz' %}
                <div class="form-check form-switch mb-3"><!-- Si viene null, marcado, sino no marcado-->
                    <input class="form-check-input" name="doAllQuestions" type="checkbox" role="switch"
                           id="doAllQuestions" {{ chapter.maxQuestion is null ? 'checked' : '' }} >
                    <label class="form-check-label" for="doAllQuestions">{{ 'question.configureFields.do_all_questions'|trans({}, 'messages', app.user.locale) }}</label>
                </div>
                <div class="form-group p-0 ml-3">
                    {{ 'question.configureFields.quantity_max_question'|trans({}, 'messages', app.user.locale) }}
                    <input type="number" name="maxQuestion"
                           id="maxQuestion" {{ chapter.maxQuestion is null ? 'disabled' : '' }} min="1"
                           max="{{ chapter.questions|length <= 0 ? 1 : chapter.questions|length }}"
                           value="{{ chapter.maxQuestion is null ? '1' : chapter.maxQuestion }}">
                    <input type="hidden" id="urlMaxQuestion"
                           value="{{ ea_url().setRoute('api_update_max_question', {id: chapter.id }) }}">
                </div>

            {% endif %}

        </div>

        <div class="page-actions">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                    data-bs-target="#modal-chapter-{{ chapter.type.id }}">{{ 'question.configureFields.add_question'|trans({}, 'messages', app.user.locale) }}</button>
        </div>
    </div>

    <div class="content-panel">
        <div>
            <div id="list-question-games">
                <list-question-games/>
            </div>
        </div>
    </div>
{% endblock %}

{% block body_javascript %}
    <script>
        let questions = {{ questions | json_encode | raw }};
        let chapterId = {{ chapter.id | json_encode | raw }};
        let messageQuestion = {{ chapter.type.id == 7 ?
        'message_api.alert.question_hidden'|trans({}, 'message_api',  app.user.locale) | json_encode | raw
        : 'message_api.alert.question_correct'|trans({}, 'message_api',  app.user.locale) | json_encode | raw }};
        let chapteType = {{ chapter.type.id | json_encode | raw }};
        let messageMinimumQuestion = {{ 'message_api.alert.minimal_question'|trans({'%number%': chapter.getMinimumQuestions}, 'message_api', app.user.locale)| json_encode |raw }}
            let
        minimalQuestion = {{ chapter.getMinimumQuestions | json_encode | raw }};
        let messageImagePuzzle = {{ 'message_api.alert.image_puzzle'|trans({}, 'message_api', app.user.locale)| json_encode |raw }}
    </script>

    {{ encore_entry_script_tags('uploadPuzzle') }}
    {{ encore_entry_script_tags('list-question-games') }}
    {{ encore_entry_script_tags('admin-max-question') }}
{% endblock %}
