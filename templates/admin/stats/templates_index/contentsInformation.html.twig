<div class="contents-information">
	<h2 class="section-title">
		<i class="fas fa-boxes"></i>
		{{ 'stats.title_information_content'|trans({}, 'messages',  app.user.locale) }}
	</h2>

	<div class="active-contents">
		<div class="box-info type--2">
			<div v-if="usersWithAtLeastOneCourseFinished !== undefined" class="content">
				<div class="box-icon">
					<i class="fas fa-check"></i>
				</div>

				<div class="title">
					<div class="number">
						<b>${usersWithAtLeastOneCourseFinished}</b>
						<span></span>
					</div>
				</div>

				<div class="name">
					{{ 'stats.at_least_one_course_finished'|trans({}, 'messages',  app.user.locale) }}
				</div>
			</div>
		</div>

		<div class="box-info type--1">
			<div v-if="general.totalCourses !== undefined" class="content">
				<div class="box-icon">
					<i class="fas fa-graduation-cap"></i>
				</div>

				<div class="title">
					<div class="number">
						<b>${general.totalCourses | formatNumber}</b>
						<span>{{ 'stats.content_active_f'|trans({}, 'messages',  app.user.locale) }}</span>
					</div>

					<div class="box-subtitle">
						<b>${general.totalCoursesFinished | formatNumber}</b>
						<span>{{ 'stats.title_made'|trans({}, 'messages',  app.user.locale) }}</span>
					</div>
				</div>

				<div class="name">
					{{ 'user.configureFields.courses'|trans({}, 'messages',  app.user.locale) }}
				</div>
			</div>
		</div>

		<div class="box-info type--2">
			<div v-if="general.totalChapters !== undefined" class="content">
				<div class="box-icon">
					<i class="fas fa-book"></i>
				</div>

				<div class="title">
					<div class="number">
						<b>${general.totalChapters | formatNumber}</b>
						<span>{{ 'stats.content_active_f'|trans({}, 'messages',  app.user.locale) }}</span>
					</div>

					<div class="box-subtitle">
						<b>${general.totalChaptersFinished | formatNumber}</b>
						<span>{{ 'stats.title_made'|trans({}, 'messages',  app.user.locale) }}</span>
					</div>
				</div>

				<div class="name">
					{{ 'user.configureFields.chapter'|trans({}, 'messages',  app.user.locale) }}
				</div>
			</div>
		</div>

		<div class="box-info type--5">
			<div v-if="general.totalQuestions !== undefined" class="content">
				<div class="box-icon">
					<i class="fas fa-question"></i>
				</div>

				<div class="title">
					<div class="number">
						<b>${general.totalQuestions | formatNumber}</b>
						<span>
							{{ 'stats.content_active_f'|trans({}, 'messages',  app.user.locale) }}</span>
					</div>
				</div>

				<div class="name">
					{{ 'user.configureFields.questions'|trans({}, 'messages',  app.user.locale) }}
				</div>
			</div>
		</div>
	</div>

	{{ include("admin/stats/templates_index/contentsRealized.html.twig")}}
</div>
