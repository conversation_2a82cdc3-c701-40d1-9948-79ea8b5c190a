{% block valoration_class %}
	{% if chartCountry and chartAge and chartGender %}
		{% set class_css = 'users-information' %}
	{% endif %}
	{% if  (chartCountry == false  or chartAge  == false) and chartGender %}
		{% set class_css = 'users-information-hide-chart' %}
	{% endif %}
	{% if  chartGender == false and chartAge == false and chartCountry == false %}
		{% set class_css = 'users-information-hide-not-gender' %}
	{% endif %}
	{% if  chartGender == false and chartAge  and chartCountry == false %}
		{% set class_css = 'users-information-hide-not-age' %}
	{% endif %}
	{% if  chartGender == false and chartAge  and chartCountry == true %}
		{% set class_css = 'users-information-hide-not-gender-country-age' %}
	{% endif %}
	{% if  chartCountry  and chartGender  and chartAge == false %}
		{% set class_css = 'users-information-hide-not-country' %}
	{% endif %}

	{% if  chartCountry  and chartGender == false and chartAge == false %}
		{% set class_css = 'users-information-hide-chart' %}
	{% endif %}

	<div class="{{class_css}}">
		<h2 class="section-title">
			<i class="fas fa-users"></i>
			{{ 'stats.title_information_user'|trans({}, 'messages',  app.user.locale) }}
		</h2>

		{% if chartGender %}
			<div class="users-genre">
				<h2 class="subtitle">
					<i class="fas fa-venus-mars"></i>
					{{ 'menu.users_managment.users'|trans({}, 'messages',  app.user.locale) }}
					<span>
						( ${ general.totalUsers | formatNumber } )
					</span>
				</h2>

				<div class="dashboard-panel">
					<pie-chart v-if="usersGender" :series-data="usersGender" inner-size="80%" :colors="['var(--color-dashboard-1)', 'var(--color-dashboard-2)']"></pie-chart>
				</div>
			</div>
		{% endif %}

		{% if chartCountry %}
			<div class="users-countries">
				<h2 class="subtitle">
					<i class="fas fa-globe-americas"></i>
					{{ 'stats.distribution_country'|trans({}, 'messages',  app.user.locale) }}
				</h2>

				<div class="dashboard-panel">
					<pie-chart v-if="usersCountry" :series-data="usersCountry" inner-size="80%" color-to-monochrome="--color-dashboard-2"></pie-chart>
				</div>
			</div>
		{% endif %}

		<div class="users-activity">
			<h2 class="subtitle">
				<i class="fas fa-mouse"></i>
				{{ 'stats.users_activity'|trans({}, 'messages',  app.user.locale) }}
			</h2>

			<div class="dashboard-panel">
				<pie-chart v-if="status" :series-data="status" :inner-size="'80%'" :colors="['var(--color-dashboard-1)', 'var(--color-dashboard-5)', 'var(--color-dashboard-3)']"/>
			</div>
		</div>

		<div class="device-sesion">
			<h2 class="subtitle">
				<i class="fas fa-laptop"></i>
				{{ 'stats.devices_login'|trans({}, 'messages',  app.user.locale) }}
			</h2>
			<div class="chapters-chart dashboard-panel">
				<pie-chart v-if="divicesSesion" :series-data="divicesSesion" :colors="['var(--color-dashboard-1)', 'var(--color-dashboard-5)', 'var(--color-dashboard-3)','var(--color-dashboard-2)','var(--color-dashboard-4)']" :inner-size="'80%'"/>
			</div>
		</div>

		<div class="device-sesion2">
			<div class="box-info type--2">
				<div class="content" v-if="totalLogin">
					<div class="box-icon">
						<i class="fas fa-sign-in-alt"></i>
					</div>

					<div class="title">
						<div class="number">
							<b>${totalLogin}</b>
							<span>{{ 'stats.totalLogin'|trans({}, 'messages',  app.user.locale) }}</span>
						</div>

						<div class="box-subtitle">
							<b>${totalDistincLogin}</b>
							<span>{{ 'stats.uniqueLogin'|trans({}, 'messages',  app.user.locale) }}</span>
						</div>
					</div>

					<div class="name">
						{{ 'stats.access'|trans({}, 'messages',  app.user.locale) }}
					</div>
				</div>
			</div>

			<div class="box-info type--1">
				<div class="content" v-if="divicesSesion">
					<div class="box-icon">
						<i class="fas fa-desktop"></i>
					</div>

					<div class="title">
						<div class="box-subtitle">
							<b>${ calculatedPercent('computer').toFixed(2) }%</b>
							<span></span>
						</div>
					</div>

					<div class="name">
						{{ 'user.configureFields.computer'|trans({}, 'messages',  app.user.locale) }}
					</div>
				</div>
			</div>

			<div class="box-info type--5">
				<div class="content" v-if="divicesSesion">
					<div class="box-icon">
						<i class="fas fa-mobile-alt"></i>
					</div>

					<div class="title">
						<div class="box-subtitle">
							<b>${ calculatedPercent('mobile').toFixed(2) }%</b>
						</div>
					</div>

					<div class="name">
						{{ 'user.configureFields.mobile'|trans({}, 'messages',  app.user.locale) }}
					</div>
				</div>
			</div>

			<div class="box-info type--4">
				<div class="content" v-if="divicesSesion">
					<div class="box-icon">
						<i class="fas fa-tablet-alt"></i>
					</div>

					<div class="title">
						<div class="box-subtitle">
							<b>${ calculatedPercent('tablet').toFixed(2) }%</b>
							<span></span>
						</div>
					</div>

					<div class="name">
						{{ 'user.configureFields.tablet'|trans({}, 'messages',  app.user.locale) }}
					</div>
				</div>
			</div>
		</div>

		{% if chartAge %}
			<div class="ages-users">
				<h2 class="subtitle">
					<i class="fas fa-user-friends"></i>
					{{ 'stats.distribution_ages'|trans({}, 'messages',  app.user.locale) }}
				</h2>
				<div class="chapters-chart dashboard-panel">
					<pie-chart v-if="distributionAgeUsers" :series-data="distributionAgeUsers" color-to-monochrome="--color-primary-darker" :inner-size="'80%'"/>
				</div>
			</div>
		{% endif %}

		<div class="users-session">
			<h2 class="subtitle">
				<i class="fas fa-history"></i>
				{{ 'stats.daily_login'|trans({}, 'messages',  app.user.locale) }}
			</h2>

			<div class="dashboard-panel">
				<line-chart v-if="logins" :series-data="logins" color="var(--color-dashboard-1)" :tooltip-title="'{{ 'stats.daily_login_tooltip'|trans({}, 'messages',  app.user.locale) }}'"/>
			</div>
		</div>
	</div>

{% endblock valoration_class %}
