<link href="file://{{ project_dir }}/public/build/diplomaNovomatic.css" rel="stylesheet">

<!-- Usamos una tabla para estructurar el diploma, ya que mPDF tiene mejor soporte para tablas -->
<div class="novomatic-diploma novomatic-preview" style="background-image: url('{{ asset(project_dir ~ '/public/assets/diploma/novomatic/diploma_frame.png') }}');">
  <!-- Tabla principal con altura relativa para centrado vertical -->
  <table class="diploma-table-outer">
    <tr>
      <td style="height: 119px; vertical-align: top;">&nbsp;</td>
    </tr>
    <tr>
      <td style="height: 556px; vertical-align: middle; padding: 0;">
        <!-- Tabla interna para el contenido -->
        <table class="diploma-table-inner">
          <tr>
            <td style="width: 280px; vertical-align: middle; text-align: right; padding: 0;">
              <!-- Icono de graduación izquierdo -->
              <img src="{{ asset(project_dir ~ '/public/assets/diploma/novomatic/graduation_icon.png') }}" alt="Graduation" class="novomatic-graduation-icon" />
            </td>
            <td style="width: 563px; vertical-align: middle; text-align: center; padding: 0;">
              <!-- Contenido central del diploma -->
              <div class="novomatic-content">
                <!-- Title -->
                <div class="novomatic-title">{{ 'message_api.diploma.diploma'|trans({}, 'message_api', locale) }}</div>

                <!-- Espaciado después del título -->
                <br/>
                <br/>
                <br/>
                <br/>

                <!-- Granted to text -->
                <div class="novomatic-granted">{{ 'message_api.diploma.granted'|trans({}, 'message_api', locale) }}:</div>

                <!-- Student name -->
                <div class="novomatic-student-name">{{ user.firstName }} {{ user.lastName }}</div>

                <!-- Espaciado después del nombre -->
                <br/>
                <br/>

                <!-- For having exceeded text -->
                <div class="novomatic-course-intro">{{ 'message_api.diploma.supered'|trans({}, 'message_api', locale) }}</div>

                <!-- Course name -->
                <div class="novomatic-course-name">{{ course.name }}</div>

                <!-- Espaciado después del curso -->
                <br/>
                <br/>

                <!-- Date -->
                <div class="novomatic-date">{{ 'message_api.diploma.date'|trans({}, 'message_api', locale) }}: {{ date }}</div>

                <!-- Espaciado antes del logo -->
                <br/>
                <br/>
                <br/>
                <br/>

                <!-- Logo -->
                <div class="novomatic-logo">
                  <img src="{{ asset(project_dir ~ '/public/assets/diploma/novomatic/logo_diploma.png') }}" alt="Campus Novomatic" width="200" />
                </div>
              </div>
            </td>
            <td style="width: 280px; vertical-align: middle; text-align: left; padding: 0;">
              <!-- Icono de graduación derecho -->
              <img src="{{ asset(project_dir ~ '/public/assets/diploma/novomatic/graduation_icon.png') }}" alt="Graduation" class="novomatic-graduation-icon" />
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td style="height: 119px; vertical-align: bottom;">&nbsp;</td>
    </tr>
  </table>
</div>