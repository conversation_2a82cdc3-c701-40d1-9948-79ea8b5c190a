{% block head_stylesheets %}
  {{ encore_entry_link_tags('diploma') }}
{% endblock %}
<link href="{{ asset(build_dir ~ '/diploma.css') }}" rel="stylesheet">

<div class="fondo" style="background-image: url('{{ asset(project_dir ~ '/public/assets/diploma/fundae/f_fundae.svg') }}');">
  <div class="headFundae">
    <div class="logoFundae">
      <img src="{{ asset(project_dir ~ '/public/assets/diploma/fundae/logo_fundae.png') }}" width="21rem" style="margin-bottom: 3rem" />
    </div>
    <p class="border"></p>
    <p class="textDiplomaFundae">{{ 'message_api.diploma.diploma'|trans({}, 'message_api', locale) }}</p>
    <p class="accreditation">{{ 'message_api.diploma.acreditation'|trans({}, 'message_api', locale) }}</p>
    <p class="border"></p>
  </div>

  <div class="bodyDiplomaFundae">
    <table width="100%">
      <tr>
        <td class="text" colspan="2">
          <div style="display: flex;">
            <span>{{ 'message_api.diploma.trato'|trans({}, 'message_api', locale) }}:</span>
            <span class="text-field" style="flex: 1 0 0"><strong>{{ user.firstName }} {{ user.lastName }}</strong></span>
          </div>
        </td>

        <td class="text" colspan="2">
          {{ 'message_api.diploma.nif'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>register key</strong></span>
        </td>
      </tr>

      <tr>
        <td class="text" colspan="2">
          {{ 'message_api.diploma.services'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>{{ enterprise }}</strong></span>
        </td>

        <td class="text" colspan="2">
          {{ 'message_api.diploma.cif'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>{{ enterpriseCIF }}</strong></span>
        </td>
      </tr>
    </table>

    <br />

    <table>
      <tr>
        <td class="text" colspan="4">
          {{ 'message_api.diploma.evaluation'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>{{ course }}</strong></span>
        </td>
      </tr>

      <tr>
        <td class="text" colspan="2">
          {{ 'message_api.diploma.code'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>{{ user.firstName }} {{ user.lastName }}</strong></span>
        </td>

        <td class="text" colspan="2">
          {{ 'message_api.diploma.between'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>{{ announcement.startAt|date('d-m-Y') }} / {{ announcement.finishAt|date('d-m-Y') }}</strong></span>
        </td>
      </tr>

      <tr>
        <td class="text" colspan="2">
          {{ 'message_api.diploma.duration'|trans({}, 'message_api', locale) }}:
          <span class="text" style="text-align: center; margin-left: 3rem"><strong>{{ announcement.totalHours }}</strong></span>
        </td>

        <td class="text" colspan="2">
          {{ 'message_api.diploma.fomartionType'|trans({}, 'message_api', locale) }}:
          <span class="text-field"><strong>{{ type }}</strong></span>
        </td>
      </tr>
    </table>

    <br />

    <div style="text-align: right">
      <span>{{ 'message_api.diploma.courseContent'|trans({}, 'message_api', locale) }}</span>
    </div>

    <div class="footerFundae">
      <table width="100%" style="font-size: 10px">
        <tr>
          <td style="text-align: center">--</td>
          <td style="text-align: center">{{ 'now'|date('m/d/Y') }}</td>
          <td style="text-align: center">--</td>
        </tr>
        <tr>
          <td class="text" style="text-transform: uppercase; text-align: center">
            <div class="signature">
              <p class="border"></p>
              <p>
                <span style="text-transform: uppercase">{{ 'message_api.diploma.signatureSeal'|trans({}, 'message_api', locale)|raw }}</span>
              </p>
            </div>
          </td>
          <td class="text" style="text-transform: uppercase; text-align: center">
            <div class="signature">
              <p class="border"></p>
              <p>{{ 'message_api.diploma.signatureDate'|trans({}, 'message_api', locale) }}</p>
            </div>
          </td>

          <td class="text" style="text-transform: uppercase; text-align: center">
            <div class="signature">
              <p class="border"></p>
              <p>{{ 'message_api.diploma.signature'|trans({}, 'message_api', locale) }}</p>
            </div>
          </td>
        </tr>
      </table>
    </div>
  </div>
</div>


