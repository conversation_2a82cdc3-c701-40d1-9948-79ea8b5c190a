{% extends 'emails/layout.html.twig' %}


{% block main %}
    <h2 style="font-size: 25px; font-weight: 600">{{ title | raw }}</h2>

    <p style="font-size: 20px; font-weight: lighter;">
        {{ line1 | raw }}
        {{ line2 | raw }}
    </p>

    <p style="margin-top: 20px;  font-size: 20px;">
        {% if 'http' in  url %}
            {% set finalUrl = url %}
        {% else %}
            {% set finalUrl = getSchemeAndHttpHost ~ url %}
        {% endif %}

        <a href="{{ finalUrl }}" style="display: block; background-color: #019ddf; color: #ffffff; border-radius: 5px; padding: 15px 0; text-decoration: none;">
            {{ button }}
        </a>
    </p>

    {% if footer is defined %}
        <p style="font-size: 13px; font-weight: lighter; color:gray;">
            {{ footer | raw }}
        </p>
    {% endif %}

{% endblock main %}
