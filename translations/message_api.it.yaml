message_api.content.scorm_correctly: 'Scorm avviato correttamente'
message_api.content.token_not_send: 'Token non inviato'
message_api.content.error_get_content: 'Si è verificato un errore durante il tentativo di avviare il capitolo:'
message_api.content.scorm_user_not_access: 'L''utente non ha accesso a questo corso'
message_api.content.scorm_not_content: 'Il capitolo non è un pacchetto di contenuti'
message_api.base.credentials_no_correct: 'Le credenziali inserite non sono corrette.'
message_api.base.authorized_access: 'Accesso negato'
message_api.base.user_successfully: 'L''utente si è disconnesso con successo'
message_api.base.error_login: 'Si è verificato un errore durante il tentativo di accesso dell''utente - Errore:'
message_api.base.authorized_token_fail: 'Non è stato possibile accedere tramite SSO. Se non avete ancora registrato e attivato il vostro utente sulla piattaforma, dovete farlo per abilitare questo tipo di accesso SSO.'
message_api.base.user_not_exists_in_ad: 'Credenziali errate o utente inesistente in Active Directory'
message_api.controller.error_get_course: 'Si è verificato un errore durante il tentativo di registrare l''utente - Errore:'
message_api.controller.chapter_init: 'Capitolo iniziato correttamente'
message_api.controller.error_chapter_init: 'Si è verificato un errore durante il tentativo di avviare il capitolo:'
message_api.controller.error_chapter_update: 'Capitolo correttamente aggiornato'
message_api.controller.help_text: 'Aiuto. I testi sono stati recuperati con successo'
message_api.controller.course_not_found: 'Nessun corso trovato'
message_api.controller.user_not_access: 'L''utente non ha accesso a questo corso'
message_api.controller.evaluation_course: 'Valutazione presentata'
message_api.controller.training_itinerary: 'Tu itinerario formativo'
message_api.controller.continue_training: 'Formazione continua'
message_api.message.not_found: 'Messaggio non trovato'
message_api.message.not_access: 'Accesso negato'
message_api.scorm.init: 'Scorm avviato correttamente'
message_api.scorm.not_package: 'Il capitolo non è un pacchetto SCORM'
message_api.scorm.button_add_scorm: 'Aggiungere il pacchetto scorm'
message_api.scorm.button_delete_scorm: 'Rimuovere il pacchetto scorm'
message_api.scorm.title_package: 'Paquete scorm'
message_api.scorm.upload_file_scorm: 'Selezionare il file .zip'
message_api.scorm.info_upload_scorm: 'È necessario caricare il pacchetto scorm'
message_api.notification.not_found: 'Notifica non trovata'
message_api.player.no_content: 'Questo capitolo non ha contenuto'
message_api.alert.minimal_question: 'Questo gioco richiede un minimo di %numero% di domande.'
message_api.alert.image_puzzle: 'Il puzzle deve avere un''immagine'
message_api.alert.question_correct: 'A questa domanda è necessario aggiungere una risposta corretta'
message_api.alert.chapter_content: 'Questo capitolo manca di contenuti'
message_api.alert.pdf: 'Questo capitolo necessita di un pdf'
message_api.alert.video: 'Questo capitolo manca di contenuti'
message_api.alert.slider: 'Questo capitolo ha un minimo di %numero% di immagini.'
message_api.alert.scorm: 'Questo capitolo ha bisogno di un pacchetto scorm'
message_api.alert.question_hidden: 'Questa domanda deve contenere una sola risposta e deve essere corretta'
message_api.alert.minimal_pair: 'Questo capitolo richiede un minimo di coppie %number%.'
message_api.announcement.call_for_aplications: Convocatoria
message_api.alert.course_content_incomplete: 'Ci sono informazioni mancanti nei capitoli, si prega di rivedere e aggiungere contenuti al fine di continuare con la creazione del bando.'
message_api.diploma.supered: 'Per superare'
message_api.diploma.granted: 'Concesso a'
message_api.diploma.date: Data
message_api.diploma.diploma: Diploma
message_api.diploma.trato: D./Dña
message_api.diploma.nif: 'con codice fiscale'
message_api.diploma.services: 'che fornisce servizi alla Società'
message_api.diploma.cif: 'con numero di partita IVA'
message_api.diploma.evaluation: 'Avete superato l''Azione formativa con una valutazione positiva'
message_api.diploma.code: 'Codice AF/GRUPO'
message_api.diploma.between: 'Durante i giorni'
message_api.diploma.duration: 'con una durata totale di'
message_api.diploma.fomartionType: 'ore in modalità di allenamento'
message_api.diploma.courseContent: 'Contenuti del corso alla pagina seguente'
message_api.diploma.signatureSeal: 'Firma e timbro dell''ente responsabile <br>della formazione.'
message_api.diploma.signatureDate: 'Data di emissione'
message_api.diploma.acreditation: Acreditativo
message_api.diploma.signature: 'Firma del partecipante'
message.api.diploma.type_course_default: E-learning
message.api.diploma.dates: 'Data da %dateFrom% a %dateTo%'
message.api.diploma.hours: '{0} %count% ore|{1} %count% ora|]1,Inf] %count% ore'
message.api.diploma.duration: Durata
message.api.diploma.with_dni: 'con %nameIdentification% %dni%'
message_api.announcement.cancelation_announcement: 'Annullamento della chiamata %course%'
