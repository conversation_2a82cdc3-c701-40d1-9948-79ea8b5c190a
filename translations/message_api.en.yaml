message_api.content.scorm_correctly: '<PERSON><PERSON> correctly started'
message_api.content.token_not_send: 'Token not sended'
message_api.content.error_get_content: 'An error has occurred trying to start chapter:'
message_api.content.scorm_user_not_access: 'The user has no access to this course'
message_api.content.scorm_not_content: 'The Chapter is not a Contents package'
message_api.base.credentials_no_correct: 'The credentials entered are not correct.'
message_api.base.authorized_access: 'The access data are not valid'
message_api.base.user_successfully: 'User successfully logout'
message_api.base.error_login: 'An error has occurred trying to login the user - Error:'
message_api.base.authorized_token_fail: 'You have not been able to access via SSO. If you have not yet registered and activated your user on the platform, you must do so in order to enable this type of SSO access.'
message_api.base.user_not_exists_in_ad: 'Invalid credentials or user does not exists in Active Directory'
message_api.controller.error_get_course: 'An error has occurred trying to register the user - Error: '
message_api.controller.chapter_init: 'Chapter correctly started'
message_api.controller.error_chapter_init: 'An error has occurred trying to start chapter:'
message_api.controller.error_chapter_update: 'Chapter correctly updated'
message_api.controller.help_text: 'Help Texts retrieve successfully'
message_api.controller.course_not_found: 'No courses found'
message_api.controller.user_not_access: 'The user has no access to this course'
message_api.controller.evaluation_course: 'Review submitted'
message_api.controller.training_itinerary: 'My learning journey'
message_api.controller.continue_training: 'Continue learning'
message_api.message.not_found: 'Message not found'
message_api.message.not_access: 'access not allowed'
message_api.scorm.init: 'Scorm correctly started'
message_api.scorm.not_package: 'The Chapter is not a SCORM package'
message_api.scorm.button_add_scorm: 'Add scorm package'
message_api.scorm.button_delete_scorm: 'Delete paquete scorm'
message_api.scorm.title_package: 'Scorm package'
message_api.scorm.upload_file_scorm: 'Select .zip file'
message_api.scorm.info_upload_scorm: 'You must load the scorm package'
message_api.notification.not_found: 'Notification not found'''
message_api.player.no_content: 'This chapter has no content'
message_api.alert.minimal_question: 'This game has a minimum of %number% questions'
message_api.alert.image_puzzle: 'The puzzle must have an image'
message_api.alert.question_correct: 'To this question you must add a correct answer'
message_api.alert.chapter_content: 'This chapter lacks content'
message_api.alert.pdf: 'This chapter needs a pdf'
message_api.alert.video: 'This chapter lacks content'
message_api.alert.slider: 'This chapter has a minimum of %number% images'
message_api.alert.scorm: 'This chapter needs a scorm package'
message_api.alert.question_hidden: 'This question must only contain one answer and it must be correct'
message_api.alert.minimal_pair: 'This chapter needs a minimum of %number% couples'
message_api.announcement.call_for_aplications: Convocation
message_api.alert.course_content_incomplete: 'There is missing information in the chapters, please revise and add content in order to continue with the creation of the call for proposals.'
message_api.diploma.supered: 'For having exceeded'
message_api.diploma.granted: 'Granted to'
message_api.diploma.date: Date
message_api.diploma.diploma: Diploma
message_api.diploma.trato: Mr./Ms.
message_api.diploma.nif: 'with NIF'
message_api.diploma.services: 'who provides services to the Company'
message_api.diploma.cif: 'with CIF'
message_api.diploma.evaluation: 'Has passed the Training Action with a positive evaluation.'
message_api.diploma.code: 'Code AF/GRUPO'
message_api.diploma.between: 'During the days'
message_api.diploma.duration: 'with a total duration of'
message_api.diploma.fomartionType: 'hours in the training mode'
message_api.diploma.courseContent: 'Course contents on the next page'
message_api.diploma.signatureSeal: 'Signature and stamp of the entity responsible <br> for providing the training.'
message_api.diploma.signatureDate: 'Date of issue'
message_api.diploma.acreditation: Certificate
message_api.diploma.signature: 'Participant''s signature'
message.api.diploma.type_course_default: E-learning
message.api.diploma.dates: 'Date from %dateFrom% to %dateTo%'
message.api.diploma.hours: '{0} %count% hours|{1} %count% hour|]1,Inf] %count% hours'
message.api.diploma.duration: Duration
message.api.diploma.with_dni: 'with %nameIdentification% %dni%'
message_api.announcement.cancelation_announcement: 'Cancellation of the call %course%'
message_api.help_category.delete_error: 'Cannot delete category %categoryName%, it has linked content'
