
@page {
  margin: 0mm;
  margin-header: 0mm;
  margin-footer: 0mm;
}

.fondo {
  height: 100%;
  background-size: 100%;
  background-position: bottom;
  background-repeat: no-repeat;
}

.head {
  text-align: center;
  padding-top: 3rem;
  .logoDiploma{
    padding-top: 3rem;
    padding-left: 2rem;
    margin-bottom: 0;
    align-content: center;
    text-align: center;
    background: transparent $logo-image no-repeat center center / contain;
  }
  img {
    width: 18rem;
  }
}

.textDiploma {
  text-align: center;
  font-size: 4rem;
  color: var(--color-secondary);
 /*  margin-right: 4rem; */
  text-transform: uppercase;
  margin-bottom: 0.4rem;
  margin-top: 2rem;
}

.bodyDiploma {
  text-align: center;
  font-size: 1rem;
  margin-top: 0.8rem;
}

.concedido {
  font-size: 1.8rem;
  color: var(--color-secondary);
  margin-bottom: 1rem;
}

.profile-name {
  color: var(--color-secondary);
  font-weight: 800;
  text-transform: capitalize;
  font-size: 2.5rem;
  margin: 0;
}
.dni{  
  font-size: 18px;
  margin-top: 0;
}

.profile-center {
  color: #68bbb0;
  font-weight: 300;
  text-transform: uppercase;
  font-size: 1.5rem;
  margin: 0;
}

.profile-supered {
  color: var(--color-secondary);
  font-size: 1.8rem;
  margin-top: 2rem;
  margin-bottom: 0.5rem;
}

.profile-course {
  color: var(--color-primary);
  font-size: 2rem;
  margin-top: 1rem;
}

.profile-date {
  font-size: 1.5rem;
  color: var(--color-secondary);
}

.headFundae {
  .logoFundae{
    text-align: center;
    padding-top: 6rem;
    margin-bottom: 1rem;
  }


  .textDiplomaFundae {
    text-align: center;
    font-size: 3rem;
    color: #16336BFF;
    text-transform: uppercase;
    font-weight: 300;
    margin-top: 0.2rem;
    margin-bottom: 0rem;
  }

  .accreditation{
    margin-top: -1rem;
    font-size: 1rem;
    text-transform: uppercase;
    margin-bottom: 0.8rem;
    text-align: center;
    color: grey;
  }

  .border{
    width: 25rem;
    border-bottom: solid 2px #721212;
    text-align: center;
    margin:auto;

  }
}

.bodyDiplomaFundae{
  text-align: center;
  font-size: 1rem;
  padding-right: 8rem;
  padding-left: 8rem;
  margin-top: 5rem;
  font-size: 12px;

}

.footerFundae{
  margin-top: 4rem;
  text-transform: uppercase;
  color: grey;
  opacity: 0.8;
}

.signature{
  .border{
    width: 18rem;
    border-bottom: solid 1px #721212 !important;
  }
}

.bodyContentFundae{
  margin-top: 5rem;
  padding-left: 8rem;
  padding-right: 8rem;
  h2{
    padding-top: 6rem;
    text-transform: uppercase;
    text-align: center;
  }

  table {
    border-collapse: collapse;
    th, td{
      border: 1px solid black;
    }
  }
}

.headHobetuz {
  .logoHobetuz{
    text-align: center;
    padding-top: 3rem;
    margin-bottom: 1rem;
  }

  .textDiplomaHobetuz {
    text-align: center;
    font-size: 4rem;
    color: #16336BFF;
    text-transform: uppercase;
    font-weight: 300;
    margin-top: 0.2rem;
    margin-bottom: 0rem;
  }
}

.bodyDiplomaHobetuz{
  text-align: center;
  font-size: 1rem;
  padding-right: 8rem;
  padding-left: 8rem;
  font-size: 12px;
  margin-top: 1rem;
}

.footerHobetuz{
  margin-top: 4rem;
}

.signatures {
  margin-top: 5rem;

  table {
    font-size: 10px;
    margin: auto;
    width: 60%;

    td {
      vertical-align: middle;
      position: relative;

      .text {
        text-transform: uppercase;

        .signature {
          p {
            margin: 0;
          }
        }
      }     
    }
  } 
  
 }
//Diploma de la empresa para convocatoria
.diploma {
  &__background {
    height: 100%;
    background-size: 100%;
    background-position: bottom;
    background-repeat: no-repeat;
  }

  &__head {
    text-align: center;
    padding-top: 3rem;

    &__logo {
      padding-top: 3rem;
      padding-left: 2rem;
      margin-bottom: 0;

      img {
        width: 18rem;
      }
    }

    &__title {
      font-size: 3rem;
      text-transform: uppercase;
      margin-bottom: 0.4rem;
      margin-top: 2rem;
    }
  }

  &__body {
    text-align: center;
    font-size: 1rem;
    margin-top: 0.8rem;

    &__concedido {
      font-size: 1.4rem;
      color: var(--color-secondary);
      margin-bottom: 0.5rem;
    }

    &__profile {
      &__name {
        color: var(--color-secondary);
        font-weight: 800;
        text-transform: capitalize;
        font-size: 1.6rem;
        margin: 0;
      }

      &__dni {
        font-size: 18px;
        margin-top: 0;
      }

      &__supered {
        color: var(--color-secondary);
        font-size: 1.4rem;
        margin-top: 2rem;
        margin-bottom: 0.5rem;     
      }

      &__course {
        color: var(--color-primary);
        font-size: 1.6rem;
        margin: 0;
        text-transform: capitalize;
      }
    }

    &__type-course {      
      font-size: 18px;
      margin-top: 0;  

      
    }

    &__announcement {
      font-size: 1.2rem;     
      margin-top: 1rem;

      &__dates{
        font-size: 16px;  
        margin: 0;        
      }

      &__hours{
        font-size: 16px;       
        margin-top: 0;
      }      
    }  
  } 
}


