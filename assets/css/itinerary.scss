.itinerary {
  .parent-data {
    .form-panel {
      display: none;
    }
  }

  .itinerary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    align-self: start;
  }

  .td-progress {
    display: flex;
    gap: 1rem;
    align-items: center;
    background: var(--color-primary-lighter);
    padding: 0.5rem 1rem;
    border-radius: 5px;

    &:hover{
      cursor: pointer;
    }

    span{
      white-space: nowrap;

      &.completed {
        color: var(--color-primary-dark);
      }

      b{
        color: var(--color-primary-dark);
        font-weight: 500;
      }
    }
  }

  div.progress {
    width: 100%;
    height: 10px;
    background-color: var(--color-neutral-mid-darker);

    //&:after {
    //  content: "";
    //  height: 10px;
    //  width: 50px;
    //  background-color: #6cbcb3;
    //}
    div {
      height: 10px;
      background-color: var(--color-primary);
    }
  }

  #modal-add-courses, .modal-add-users, .modal-add-managers {
    .saving-status {
      position: absolute;
      left: 0;
      right: 0;
      margin: auto;
      z-index: 10;
      height: 100%;


      background-color: rgba(255,255,255,0.6);
      backdrop-filter: blur(3px);

      .loader-container {
        background: white;
        border-radius: 10px;
        box-shadow: 1px 1px 1px 1px #212121;
        padding: 10px;
      }
    }

    .modal-header {
      text-align: center;

    }
    .modal-header h5 {
      color: white;
    }


    .modal-header button {
      background-color: white;
    }

    .modal-dialog {
      width: 98%;
      max-width: 100%;
      margin: 1rem auto;
    }

    .modal-dialog .card-information {
      text-align: left;
    }

    .ItineraryUserFilter .users-container {
      min-height: 50vh;
    }

    .ItineraryCategoryFilter .contentFilters {

      min-height: 25em;
      background: var(--color-neutral-lighter);

    }

    .itinerary-manager-filter-action {
      text-align: right;      
    }

    .course-container {
      .drop-zone {
        width: 100%;
        display: grid;
        align-content: flex-start;
        grid-template-columns: repeat(auto-fit, minmax(450px,1fr));
        min-height: 20vh;
        height: 70vh;
        overflow-y: auto;
        overflow-x: hidden;
        // border: 1px solid var(--color-neutral-mid-light);
        background: var(--color-neutral-lighter);
        // border-radius: 5px;
        padding: 0.5rem;
        gap: 0.5rem;
      }

      @media screen and (max-width: 1024px) {
        .course-image {
          margin-top: 0;
          margin-bottom: auto;
          img {
            width: 50px;
            height: 50px;
          }
        }
      }
    }
  }

  .ordered-courses {
    .course-card {
      width: 32%;

      @media screen and (max-width: 425px) {
        width: 100%;
      }
    }
  }

  .basic-info {
    display: flex;
    flex-flow: column;
  }

  .description {
    width: 100%;
    text-align: justify;
    text-justify: auto;
    max-height: 70vh;
    overflow-y: auto;
  }

  .courses {
    &>div {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(450px,1fr));
      border: 1px solid var(--color-neutral-mid-light);
      background: var(--color-neutral-lighter);
      border-radius: 5px;
      padding: 0.5rem;
      gap: 0.5rem;
    }
  }

  .course-card {
    display: flex;
    flex-flow: row nowrap;
    padding: .5rem;
    gap: .5rem;
    // overflow: hidden;

    .course-image {
      position: relative;
      background: var(--color-neutral-lighter);
      // margin-top: auto;
      // margin-bottom: auto;

      .position {
        position: absolute;
        top: 0;
        left: 0;
        padding: 0 0.5rem;
        color: #212121;
        background: white;
        font-size: .8rem;
        font-weight: 600;
        border: 1px solid var(--color-neutral-mid);
      }
    }

    img {
      width: 100px;
      height: 100px;
    }

    .course-body {
      display: flex;
      flex-flow: column;
      margin-left: 5px;
      margin-right: 5px;

      .delete-action {
        cursor: pointer;
        position: absolute;
        top: 40%;
        right: 0;
        padding: 0 0.5rem;
        margin-right: 0.5em;
        color: #212121;
        /*background: var(--color-neutral-lightest);*/
        font-size: .8rem;
        font-weight: 600;
        border-left: 1px solid var(--color-neutral-mid-light);
        border-bottom: 1px solid var(--color-neutral-mid-light);
        aspect-ratio: 1/1;
        display: grid;
        place-content: center;
        border-radius: 0 0 0 3px;
        transition: all 0.5s ease;

        &:hover {
          color: var(--color-danger);
        }
      }

      .add-action {
        cursor: pointer;
        position: absolute;
        top: 40%;
        right: 0;
        padding: 0 0.5rem;
        margin-right: 0.5em;
        color: #ffffff;
        font-size: .8rem;
        font-weight: 600;
        border-left: 1px solid var(--color-neutral-mid-light);
        border-bottom: 1px solid var(--color-neutral-mid-light);
        aspect-ratio: 1/1;
        display: grid;
        place-content: center;
        border-radius: 0 0 0 3px;
        transition: all 0.5s ease;

        &:hover {
          color: var(--color-primary-lighter);
        }
      }

      .course-title {
        font-weight: 500;
      }

      .course-code {
        font-size: var(--font-size-s);
        color: var(--color-neutral-dark);
        font-style: italic;
        margin-bottom: .5rem;
      }

      .course-category{
        font-size: var(--font-size-s);
        color: var(--color-primary-darker);
        text-transform: uppercase;
      }
    }
  }
}
