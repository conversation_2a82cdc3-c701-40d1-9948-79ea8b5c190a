@page {
  margin: 0;
  padding: 0;
  size: landscape;
  /* mPDF no soporta vertical-align en @page, lo manejamos con tablas */
  /* Configuración optimizada para mPDF - orientación horizontal */
}

/* Estilos para listas */
ul, ol {
  list-style-type: disc;
  margin-left: 20px;
  padding-left: 20px;
}

ol {
  list-style-type: decimal;
}

li {
  margin-bottom: 5px;
}

/* Estilos adaptados para mPDF */
.novomatic-diploma {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  color: #333;
  text-align: center; /* Centrar contenido */
  position: relative;
  background-size: cover; /* Cubrir exactamente todo el espacio */
  background-position: center;
  background-repeat: no-repeat;
  background-color: #003366; /* Fondo azul oscuro como en la imagen de referencia */
  overflow: hidden;
  /* Eliminado page-break-after: always para evitar página en blanco adicional */
}

/* <PERSON> de fondo - usando background-size: cover del CSS principal */

.novomatic-content {
  width: 100%;
  margin: 0 auto; /* Centrar horizontalmente */
  padding: 0;
  text-align: center;
  position: relative;
  /* Sin bordes ni fondos, todo transparente para que se vea la imagen de fondo */
  background-color: transparent;
  /* Optimizado para mPDF - evitar padding complejo */
}

/* Estilos de texto adaptados para mPDF - usando píxeles basados en dimensiones reales */
.novomatic-title {
  font-size: 64px; /* Título principal grande */
  font-weight: bold;
  margin: 0 0 50px 0; /* Más separación después del título */
  color: #000000; /* Negro para mayor contraste */
  text-transform: uppercase;
  line-height: 1.2;
}

.novomatic-granted {
  font-size: 24px; /* Texto "Granted to" */
  font-style: italic;
  margin: 0;
  color: #555555;
  line-height: 1.2;
}

.novomatic-student-name {
  font-size: 42px; /* Nombre del estudiante destacado */
  font-weight: bold;
  margin: 0;
  color: #000000; /* Negro para mayor contraste */
  text-transform: none; /* Nombre en formato normal */
  line-height: 1.2;
}

.novomatic-course-intro {
  font-size: 24px; /* Texto "For having exceeded" */
  margin: 0;
  color: #555555;
  line-height: 1.2;
}

.novomatic-course-name {
  font-size: 35px; /* Nombre del curso */
  font-weight: bold;
  margin: 0;
  color: #000000; /* Negro para mayor contraste */
  font-style: normal;
  line-height: 1.2;
}

.novomatic-date {
  font-size: 24px; /* Fecha */
  margin: 0;
  color: #555555;
  font-weight: normal;
  line-height: 1.2;
}

/* Estilos del logo adaptados para mPDF */
.novomatic-logo {
  margin: 0; /* Sin márgenes, usamos bloques vacíos para espaciado */
  position: relative;
  text-align: center;
}

.novomatic-logo img {
  width: 200px; /* Ancho fijo para mPDF */
  height: auto; /* Altura automática para mantener proporciones */
}

/* Eliminamos la línea dorada debajo del logo */
.novomatic-logo-line {
  display: none;
}

/* Estilos para los iconos de graduación - adaptables para mPDF */
.novomatic-graduation-icon {
  width: 150px;
  height: auto; /* Altura automática para mantener proporciones */
  opacity: 0.7;
}

/* Eliminamos posicionamiento absoluto que puede causar problemas en mPDF */
/* Los iconos ahora se posicionan usando la estructura de tabla */

/* Estilos específicos para las tablas de centrado vertical - optimizados para mPDF */
.diploma-table-outer {
  width: 100%;
  height: 750px; /* Altura más conservadora para evitar overflow */
  border-collapse: collapse;
  border: none;
  margin: 0;
  padding: 0;
  table-layout: fixed;
  /* Altura fija para evitar overflow y páginas adicionales */
}

.diploma-table-outer td {
  border: none;
  margin: 0;
  padding: 0;
}

.diploma-table-inner {
  width: 100%;
  border-collapse: collapse;
  border: none;
  margin: 0 auto;
  padding: 0;
  table-layout: fixed;
}

.diploma-table-inner td {
  border: none;
  margin: 0;
  padding: 0;
}

/* Estilos específicos para preview con mayor separación vertical */
.novomatic-preview {
  .novomatic-title {
    margin: 0; /* Sin márgenes, usamos bloques vacíos para espaciado */
  }

  .novomatic-granted {
    font-size: 18px; /* Más pequeño y elegante */
    color: #666666;
    margin: 0;
  }

  .novomatic-student-name {
    margin: 0; /* Sin márgenes, usamos bloques vacíos para espaciado */
  }

  .novomatic-course-intro {
    font-size: 18px; /* Más pequeño y elegante */
    color: #666666;
    margin: 0;
  }

  .novomatic-course-name {
    margin: 0; /* Sin márgenes, usamos bloques vacíos para espaciado */
  }

  .novomatic-date {
    font-size: 18px; /* Más pequeño y elegante */
    color: #666666;
    margin: 0; /* Sin márgenes, usamos bloques vacíos para espaciado */
  }
}

/* Estilos mejorados para archivos base y bonus - aplicar tipografía elegante */
.novomatic-diploma {
  .novomatic-granted {
    font-size: 20px; /* Texto "Granted to" más pequeño */
    color: #666666; /* Color más suave */
  }

  .novomatic-course-intro {
    font-size: 20px; /* Texto "For having exceeded" más pequeño */
    color: #666666; /* Color más suave */
  }

  .novomatic-date {
    font-size: 20px; /* Fecha más pequeña */
    color: #666666; /* Color más suave */
  }
}

/*
 * OPTIMIZACIONES PARA mPDF - DIPLOMA NOVOMATIC:
 *
 * DIMENSIONES REALES A4 HORIZONTAL:
 * - Ancho: 1123px (297mm)
 * - Alto: 794px (210mm)
 * - Distribución vertical: 119px + 556px + 119px = 794px
 * - Distribución horizontal: 225px + 673px + 225px = 1123px
 *
 * OPTIMIZACIONES APLICADAS:
 * 1. Alturas exactas: Usamos píxeles exactos basados en dimensiones reales de A4
 * 2. Eliminado page-break-after: Para evitar páginas en blanco adicionales
 * 3. Estructura de tabla: Usamos tablas para el centrado vertical
 * 4. Márgenes simplificados: Formato "margin: top right bottom left"
 * 5. Posicionamiento: Evitamos position: absolute, usamos estructura de tabla
 * 6. Clases CSS: Aplicamos clases en lugar de estilos inline complejos
 * 7. Line-height: Añadimos line-height para mejor espaciado de texto
 * 8. Altura fija: 794px en lugar de 100vh para evitar overflow
 * 9. Imágenes adaptables: width en px y height: auto para mantener proporciones
 *
 * Referencia de CSS soportado por mPDF: https://mpdf.github.io/css-stylesheets/supported-css.html
 */