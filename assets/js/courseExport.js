import Vue from 'vue';
import axios from "axios";
import VueToast from "vue-toast-notification";
import 'vue-toast-notification/dist/theme-sugar.css';
import TaskQueueMixin from '../vue/mixins/TaskQueueMixin';

Vue.use(VueToast);
new Vue({
    delimiters: ['${', '}'],
    mixins: [TaskQueueMixin],
    data() {
        return {
            locale: 'es',
            toastLocales: {
                'es' : { error: 'Se ha producido un error', success: 'Solicitud realizada con exito', info: 'Procesando su solicitud, por favor espere', fileSuccess: 'Sus archivos han sido generados con exito', notFound: 'No se encontro el reporte' },
                'en' : { error: 'An error has occurred', success: 'Successful request', info: 'Processing your request, please wait', fileSuccess: 'Your files have been successfully generated', notFound: 'Report not found' },
                'pt' : { error: 'Ocorreu um erro.', success: 'Pedido de sucesso', info: 'Processamento do seu pedido, por favor, espere', fileSuccess: 'Os seus ficheiros foram gerados com sucesso', notFound: 'Relatório não encontrado' },
            }
        }
    },
    beforeMount() {
        if (this.$el.hasAttribute('locale')) {
            this.locale = this.$el.attributes['locale'].value
        }
    },
    mounted() {
        if (this.$el.hasAttribute('shared-url')) {
            const sharedUrl = this.$el.attributes['shared-url'].value;
            const self = this;
            if (sharedUrl !== undefined && sharedUrl.length > 0) {
                navigator.clipboard.writeText(sharedUrl).then(() => {
                    self.$toast.success('URL copied');
                }, () => {
                    self.$toast.error(`Failed to copy: ${sharedUrl}`, {
                        duration: 0
                    })
                });
            }
        }
    },
    methods: {
        async export_catalog() {
            try {
                await this.enqueueTask({
                    url: '/admin/course/export-catalog',
                    messages: {
                        success: this.toastLocales[this.locale].success,
                        error: this.toastLocales[this.locale].error
                    }
                });
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
}).$mount('#app');
