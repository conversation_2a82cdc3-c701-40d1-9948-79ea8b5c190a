import $ from 'jquery';
import 'bootstrap'

$(function() {
    $('.action-delete').on('click', function(e) {
        e.preventDefault();

        const url = $(this).attr('href');

        $('#modal-delete').modal({ backdrop: true, keyboard: true })
            .off('click', '#modal-delete-button')
            .on('click', '#modal-delete-button', function () {
                let deleteForm = $('#delete-form');
                deleteForm.attr('action', url);
                deleteForm.trigger('submit');
            });
    });
});
