import $ from 'jquery';
import 'bootstrap'

$(function() {
    $('.page-actions a.btn[data-action-name="translate"]').on('click', function(e) {
        e.preventDefault();

        const formAction = $(this).attr('href');
        const courseId = $('#course').val();
        const localeCourse = $('#locale-course').val();

        $('#form_language').data('course', courseId);
        $('select').val(localeCourse);

        $('#modal-translate').modal({ backdrop: true, keyboard: true })
            .off('click', '#modal-translate-button')
            .off('change', '#form_language')
            .on('click', '#modal-translate-button', function () {
                let translateForm = $('form[name=translate-form]');
                translateForm.attr('action', formAction + '&language=' + $('#form_language').val());
                translateForm.trigger('submit');

            })
            .on('change', '#form_language', function () {
                const checkLocaleUrl = 'admin/course/check-locale';
                const locale = $(this).val();

                console.log("locale", locale);
                const courseId = $(this).data('course');

                let checkLocaleRequest = $.ajax({ type: "POST", url: checkLocaleUrl + '/' + courseId, data: { locale: locale } });

                checkLocaleRequest.done(function(result) {
                    if(result.message !== undefined){
                        $('#modal-translate div.alert').html(result.message).fadeIn();
                        $("#modal-translate-button").prop("disabled", true);
                    }
                    else{
                        $('#modal-translate div.alert').fadeOut();
                        $("#modal-translate-button").prop("disabled", false);
                    }
                });
            })
        ;
    });
});
