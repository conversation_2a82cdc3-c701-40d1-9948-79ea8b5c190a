import axios from 'axios';

window.addEventListener('load', function () {
    let actions = document.querySelectorAll('.action-impersonate');
    actions.forEach(action => {
        action.addEventListener('click', function (e) {
            e.preventDefault();

            if (this.href.indexOf('#') !== -1) {
                let id = this.href.substr(this.href.indexOf('#') + 1);

                axios.post('/admin/user/impersonate', {'user': id})
                    .then(result => {
                        //  localStorage.setItem('user-token', 'Bearer ' + result.data.token);
                        //En el front se espera  sin Bearer

                     /*    localStorage.setItem('user-token', result.data.token);
                        localStorage.setItem('user-data', result.data.user); */
                        let openedWindow = window.open(result.data.url, '_blank')
                        openedWindow.focus();
                    });
            } else {
                location.href = this.href; 
            }
        });
    })

}, false);
