<template>
  <div class="TrueOrFalse">
    <div class="col align-self-end text-right">
      <button
        type="button"
        class="btn btn-primary mb-4"
        data-bs-toggle="modal"
        :data-bs-target="`#modal-chapter-${chapterType}`"
        @click="newQuestion"
      >
        {{ translationsVue.question_configureFields_add_question }}
      </button>
    </div>

    <!-- Modal -->
    <div
      class="modal fade"
      :id="`modal-chapter-${chapterType}`"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="modal-chapter-12Label"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modal-chapter-12Label">
              {{ translationsVue.quiz_configureFields_title_creation }}
            </h5>
            <button
              type="button"
              class="btn-close sendQuestionFormDataEdit"
              data-bs-dismiss="modal"
              aria-label="Close"
              :id="`close-modal-chapter-${chapterType}`"
            ></button>
          </div>
          <div class="modal-body">
            <Options
              class="options"
              :chapter-id="chapterId"
              :current-line="currentLine"
              @reloadBlock="reloadBlocks"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="content-table">
      <loader :isLoaded="!showCalled"></loader>
      <div v-if="showCalled">
        <div v-if="lines" class="col-md-12">
          <table class="datagrid">
            <thead class="thead-light">
              <tr>
                <th>
                  <span>{{ translationsVue.common_areas_image }}</span>
                </th>
                <th>
                  <span>{{ translationsVue.question_label_in_singular }}</span>
                </th>
                <th>
                  <span>{{ translationsVue.user_configureFields_time }}</span>
                </th>
                <th>
                  <span>{{ translationsVue.games_text_common_answer }}</span>
                </th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              <Resume
                v-for="(block, index) in questions"
                :key="index"
                :block="block"
                @deleteLine="deleteLine"
                @modifyLine="modifyLine"
              />
            </tbody>
          </table>
        </div>
      </div>

      <!-- Modal Edit -->
      <div
        class="modal fade"
        id="editQuestion"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
        tabindex="-1"
        aria-labelledby="editQuestionLabel"
        aria-hidden="true"
      >
        <div class="modal-dialog modal-dialog-centered modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="editQuestionLabel">
                {{ translationsVue.quiz_configureFields_title_creation }}
              </h5>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
                @click="closeModalEdit()"
              ></button>
            </div>
            <div class="modal-body">
              <Options
                class="options"
                :chapter-id="chapterId"
                :current-line="currentLine"
                @reload-blocks="reloadBlocks"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Options from "../components/true-or-false/Options";
import Resume from "../components/true-or-false/Resume";
import { get } from "vuex-pathify";
import Loader from "../components/Loader";

export default {
  props: {
    blocks: {
      type: Array,
      default: () => [],
    },
    chapterId: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      called: true,
      lines: this.blocks,
      currentLine: {},
      chapterType,
      translationsVue,
    };
  },

  components: {
    Options,
    Resume,
    Loader,
  },

  computed: {
    ...get("callModule", ["isLoading"]),
    ...get("trueOrFalseModule", ["getQuestions"]),

    showCalled() {
      return !this.isLoading() && this.called;
    },

    questions() {
      return this.getQuestions();
    },
  },

  created() {
    this.reloadBlocks();
  },

  methods: {
    async reloadBlocks() {
      const data = {
        chapterId: this.chapterId,
      };
      this.called = false;

      const result = await this.$store.dispatch(
        "trueOrFalseModule/reloadBlock",
        data
      );

      this.called = true;
      this.lines = result.data.data;
    },

    deleteLine(value) {
      const data = {
        id: value,
      };

      this.$store
        .dispatch("trueOrFalseModule/deleteLine", data)
        .then((response) => {
          this.reloadBlocks();
        });
    },

    modifyLine(value) {
      const modalEdit = document.getElementById("editQuestion");
      modalEdit.classList.add("show");
      modalEdit.style.display = "block";

      modalEdit.setAttribute("aria-modal", "true");
      modalEdit.setAttribute("role", "dialog");
      modalEdit.removeAttribute("aria-hidden");
      modalEdit.setAttribute("style", "display: block; padding-right: 17px;");

      this.currentLine = value;
    },

    closeModalEdit() {
      const modalEdit = document.getElementById("editQuestion");
      modalEdit.classList.remove("show");
      modalEdit.style.display = "none";

      modalEdit.setAttribute("aria-modal", "false");
      modalEdit.setAttribute("role", "dialog");
      modalEdit.setAttribute("aria-hidden", "true");
      modalEdit.setAttribute("style", "display: none; padding-right: 17px;");
    },

    newQuestion() {
      this.currentLine = {};
    },
  },
};
</script>

 <style scoped lang="scss"> 
.content-table {
  background: #fff;
}
</style>
