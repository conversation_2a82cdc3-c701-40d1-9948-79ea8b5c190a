<template>
  <div class="VisorPdf">
    <div class="zoom" v-if="infoChapter" >
      <button class="btn-zoom-out" @click="zoomDisminuir()">
        <i class="fas fa-search-minus"></i>
      </button>

        <span> {{ scale }} %</span>

      <button @click="zoomAument()">
        <i class="fas fa-search-plus"></i>
      </button>

      <button v-if="is_downloadable" class="download-btn" @click="downloadFile()"><i class="fas fa-download"></i></button>
      
    </div>

    <div class="pdf">
      <pdf
        v-if="infoChapter && !infoChapter.finished"
        :page="currentPage"
        :src="src"
        id="pdf"
        @num-pages="pageCount = $event"
        @page-loaded="currentPage = $event"
        class="iframePdf"
        :style="{
          width: scale + '%',
          marginTop: '3rem',
          marginBottom: '3rem',
        }"
      >
      </pdf>

      <spinner class="spinner" v-if="!infoChapter" />

      <div v-if="infoChapter && infoChapter.finished" class="pdf">
        <pdf
          v-for="i in numPages"
          :key="i"
          :src="src"
          :page="i"
          id="pdf"
          class="pdfCompleted"
          :style="{
            width: scale + '%',
            marginTop: '3rem',
            marginBottom: '3rem',
          }"
        >
        </pdf>
      </div>
      <!--   <embed :src="`/uploads/pdf/packages/${namepdf}`+ '#toolbar=0'" type="application/pdf" width="100%" height="100%" v-if="infoChapter && infoChapter.finished"> -->
    </div>

    <div class="actions" v-if="infoChapter && !infoChapter.finished">
      <button @click="lastPage()">
        <i class="fas fa-caret-left"></i>
      </button>

      <span>{{ currentPage }} / {{ pageCount }}</span>

      <button
        @click="nextPage()"
        v-if="currentPage < pageCount"
      >
        <i class="fas fa-caret-right"></i>
      </button>
    </div>
  </div>
</template>


<script>
import { get } from "vuex-pathify";
import pdf from "vue-pdf";
import Spinner from "../components/base/Spinner";

const KEYBOARD_CODES = { LEFT: 'ArrowLeft', RIGHT: 'ArrowRight' };

export default {
  name: "visorPdf",
  components: {
    pdf,
    Spinner,
  },
  props: ["id", "namepdf", 'is_downloadable'],
  data() {
    return {
      src: pdf.createLoadingTask(`/uploads/pdf/packages/${this.namepdf}`),
      numPages: 0,
      infoChapter: undefined,
      currentPage: 1,
      pageCount: 0,
      scale: 80,
      mypage: 0,
      keyboardPaginationEnable: true,
    };
  },

  async created() {
    this.infoChapter = await this.$store.dispatch("chapterModule/getState", this.id);

    if (this.infoChapter) {
      this.currentPage = this.infoChapter.pagePdf;
    }

    await this.$store.dispatch("chapterModule/getState", this.id)
      .then(async (data) => {
        await this.$store
          .dispatch("timeModule/initStart", this.userChapter())
          .then(() => {
            this.src.promise.then((pdf) => {
              this.numPages = pdf.numPages;
              console.log(this.numPages);
              if (this.numPages == 1) this.nextPage();
            });
        });

        const chapterStatus = {
          chapter: this.id,
          finished: false,
        };

        if (!this.infoChapter) {
          await this.$store.dispatch("chapterModule/updateState", chapterStatus);
        }
    });

    // todo
    this.loadKeyboardNavigation();
  },

  computed: {
    ...get("chapterModule", ["userChapter"]),
  },

  methods: {
    loadKeyboardNavigation() {
      window.addEventListener('keydown', ({ key }) => {
        if (this.keyboardPaginationEnable && Object.values(KEYBOARD_CODES).includes(key)) {
          this.changeBlockWithKeyboad(key);
          this.keyboardPaginationEnable = false;
        }
      });

      window.addEventListener('keyup', () => {
        this.keyboardPaginationEnable = true;
      });
    },

    changeBlockWithKeyboad(key) {
      if (key === KEYBOARD_CODES.LEFT) {
        this.lastPage();
      }

      if (key === KEYBOARD_CODES.RIGHT) {
        this.nextPage();
      }
    },

    async nextPage() {
      const endPage = this.numPages - this.currentPage;

      const chapterStatus = {
        chapter: this.id,
        finished: true,
      };

      const chapterStatusPendient = {
        chapter: this.id,
        finished: false,
        pagePdf: this.currentPage + 1,
      };

      if (endPage <= 1) {
        await this.$store.dispatch("chapterModule/updateState", chapterStatus);
      } else {
        await this.$store.dispatch(
          "chapterModule/updateState",
          chapterStatusPendient
        );
      }

      if (this.currentPage < this.numPages) {
        window.scrollTo(0, 0);
        return (this.currentPage = this.currentPage + 1);
      }
    },

    lastPage() {
      if (this.currentPage > 1) {
        window.scrollTo(0, 0);
        return (this.currentPage = this.currentPage - 1);
      }
    },

    zoomAument() {
      return (this.scale = this.scale + 10);
    },

    zoomDisminuir() {
      if (this.scale > 30) {
        return (this.scale = this.scale - 10);
      }
    },

    async downloadFile() {
      await this.$store.dispatch("chapterModule/downloadFile", this.namepdf);
    }
  },
};
</script>

 <style scoped lang="scss"> 
.visorPdf {
  display: flex;
  flex-direction: column;
 /*  width: 100%;
  height: 100%; */
  background: black;

  .zoom,
  .actions{
    display: flex;
    justify-content: center;
    width: 100%;
    padding: 0.25rem;
    align-items: center;
    gap: 1rem;
    flex: 0;

    button{
      border: none;
      border-radius: 3px;
      padding: 0.5rem;
      width: 40px;
      color: #fff;
      transition: all .3s ease;
    }
  }

  .zoom {
    background: #ffffff;
    box-shadow: 0 4px 4px #0000002e;
    color: #565656;
    position: fixed;
    z-index: 1;

    button{
      background: var(--color-neutral-dark);

      &:hover{
        background: var(--color-neutral-darkest);
      }
    }

    .download-btn, .btn-zoom-out {
      margin-left: auto;
    }
  }

  .pdf {
    background: #c7c7c7;
    width: 100%;
    overflow-y: auto;
    flex: 1;
  }

  .actions {
    color: white;
    background: var(--color-secondary);
    position: fixed;
    bottom: 0;

    button{
      background: transparent;

      &:hover{
        background: var(--color-secondary-darkest);
      }
    }
  }

  .iframePdf {
    margin: auto;
    padding-bottom: 3rem;
  }

  .pdfCompleted {
      margin: auto;
  }

  .spinner {
    margin: auto;
  }
}
</style>
