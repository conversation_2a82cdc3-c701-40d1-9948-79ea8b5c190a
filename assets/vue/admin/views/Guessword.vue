<template>
  <div class="Guessword">
    <div class="col align-self-end text-right mb-2">
      <button
        type="button"
        class="btn btn-primary btn-sm"
        data-bs-toggle="modal"
        data-bs-target="#modal-chapter-18"
        @click="adicionar"
      >
        {{ translationsVue.question_configureFields_add_question }}
      </button>
    </div>

    <div class="for-new">
      <div
        class="modal fade"
        id="modal-chapter-18"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
        tabindex="-1"
        aria-labelledby="modal-chapter-18Label"
        aria-hidden="true"
      >
        <div class="modal-dialog modal-dialog-centered modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="modal-chapter-18Label">
                {{ translationsVue.quiz_configureFields_title_creation }}
              </h5>
              <button
                type="button"
                class="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
                id="close-modal-chapter-18"
              ></button>
            </div>
            <div class="modal-body">
              <form @submit.prevent="submitForm" id="formQuestion">
                <div class="form">
                  <div class="form-first">
                    <div>
                      <BaseTextTarea
                        :label="translationsVue.quiz_configureFields_question"
                        :max="110"
                        :value.sync="question"
                        :placeholder="
                          translationsVue.quiz_configureFields_question_placeholder
                        "
                        :required="true"
                        :rows="3"
                        :submitted="submitted"
                      ></BaseTextTarea>
                    </div>
                  </div>
                  <div class="form-second">                   
                    <div>
                      <BaseTextTarea
                        :label="
                          translationsVue.guesword_configureFields_word_title
                        "
                        :min="2"
                        :max="10"
                        :value.sync="word"
                        :placeholder="
                          translationsVue.guesword_configureFields_word_title_placeholder
                        "
                        :required="true"
                        :rows="1"
                        :submitted="submitted"
                        :preventSpace="true"
                        :validateSpecialCharacters="true"
                        :acceptSpecialCharactersEnie="true"
                        :acceptSpecialCharactersAccent="true"
                      ></BaseTextTarea>
                    </div>

                    <div class="mt-3">
                      <label for="title" class="form-label"
                        >{{ translationsVue.games_text_common_time }}
                      </label>
                      <BaseInputTime
                        v-model="time"
                        :options="['minutes', 'seconds']"
                        :maxMinutes="31"
                        :time="time"
                        @time-update="timeUpdate"
                      />
                    </div>
                  </div>
                </div>

                <div class="col align-self-end text-center mb-2 mt-5">
                  <button
                    v-show="0"
                    type="button"
                    class="btn btn-secondary"
                    data-bs-dismiss="modal"
                    ref="closeChapterContentModal"
                  ></button>

                  <button
                    v-if="!processSave"
                    type="submit"
                    data-dismiss="modal"
                    class="btn btn-primary btn-sm"
                    @click="submitted = true"
                  >
                    {{ translationsVue.Save }}
                  </button>
                  <loader :isLoaded="processSave"></loader>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div>
      <loader :isLoaded="!showCalled"></loader>
      <div v-if="showCalled">
        <div v-if="lines" class="col-md-12">
          <table class="table datagrid">
            <thead class="">
              <tr>
                <th>
                  <span>{{ translationsVue.question_label_in_singular }}</span>
                </th>
                <th>
                  <span>{{ translationsVue.games_text_common_answer }}</span>
                </th>
                <th>
                  <span>{{ translationsVue.games_text_common_time }}</span>
                </th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              <Resume
                v-for="(block, index) in questions"
                :key="index"
                :block="block"
                @deleteLine="deleteLine"
                @modifyLine="modifyLine"
              />
            </tbody>
          </table>
        </div>
        <div v-else>
          <div class="alert alert-info" role="alert">
            {{ translationsVue.games_text_common_no_questions }}
          </div>
        </div>
      </div>

      <BaseModalDelete
        identifier="modalDeleteQuestion"
        :title="translationsVue.quiz_configureFields_question_delete"
        @delete-element="deleteQuestion(question.id)"
      />
    </div>
  </div>
</template>

<script>
import Resume from "../components/guessword/Resume";
import { get } from "vuex-pathify";
import Loader from "../components/Loader";

import { alertToastMixin } from "../../mixins/alertToastMixin";
import { formatDateMixin } from "../../mixins/formatDateMixin";
import { modalMixin } from "../../mixins/modalMixin";

export default {
  props: {
    blocks: {
      type: Array,
      default: () => [],
    },
    chapterId: {
      type: Number,
      default: 0,
    },
  },

  mixins: [alertToastMixin, formatDateMixin, modalMixin],

  data() {
    return {
      called: true,
      lines: this.blocks,
      addLine: false,
      question: "",
      word: "",
      id: 0,
      time: "00:00:30",
      mensajeActivo: false,
      modificarRegistro: false,
      titulo: "",
      mostrarRespuestas: false,
      caracteresMinimosWord: 4,
      caracteresMaximosWord: 10,
      translationsVue,
      dataDelete: "",
      processSave: false,
      submitted: false,
    };
  },

  components: {
    Resume,
    Loader,
  },

  computed: {
    ...get("callModule", ["isLoading"]),

    ...get("guesswordModule", ["getQuestions"]),

    showCalled() {
      return !this.isLoading() && this.called;
    },

    questions() {
      return this.getQuestions();
    },
  },

  async created() {
    await this.fetchQuestions();
  },

  methods: {
    adicionar() {
      this.clearCurrent();
      this.titulo = "Adicionar pregunta";
      this.addLine = true;
      this.modificarRegistro = false;
      this.submitted = false;
    },

    cerrarVentanaEdit() {
      this.addLine = false;
      this.mensajeActivo = false;
      this.modificarRegistro = false;
    },

    cancelar() {
      this.clearCurrent();
      this.addLine = false;
      this.mensajeActivo = false;
      this.modificarRegistro = false;
    },

    clearCurrent() {
      this.question = "";
      this.word = "";
      this.time = "00:00:30";
    },

    async reloadBlocks() {
      const data = {
        chapterId: this.chapterId,
        gametype: "guessword",
      };
      this.called = false;

      this.$store
        .dispatch("guesswordModule/reloadBlock", data)
        .then((response) => {
          this.called = true;
          this.lines = response.data.data;
        });
    },

    deleteLine(value) {
      this.dataDelete = {
        id: value,
      };

      this.openModal("modalDeleteQuestion");
    },

    deleteQuestion() {
      this.$store
        .dispatch("guesswordModule/deleteLine", this.dataDelete)
        .then((response) => {
          this.reloadBlocks();
        });

      this.closeModal("modalDeleteQuestion");
    },

    modifyLine(data) {
      this.id = data.id;
      this.question = data.question;
      this.time = this.convertSecondToHoursMinutesAndSeconds(data.time);
      this.word = data.word;
      this.addLine = true;
      this.modificarRegistro = true;
      this.titulo = "Modificar pregunta";

      // this.openModal("editQuestion");
    },

    async submitForm(event) {
      const form = document.getElementById("formQuestion");
      if (!form.checkValidity()) return;

      try {
        await this.saveQuestion();
        this.alertSuccesSave();
        this.$refs["closeChapterContentModal"].click();
        await this.fetchQuestions();
        this.processSave = false;
      } catch (error) {
        this.alertErrorSave();
      }
    },

    async saveQuestion() {
      this.processSave = true;
      if (this.modificarRegistro) {
        await this.actionUpdateQuestion();
      } else {
        await this.actionSaveQuestion();
      }
    },

    async actionSaveQuestion() {
      await this.$store.dispatch(
        "guesswordModule/setBlock",
        this.dataForsubmitForm()
      );
    },

    async actionUpdateQuestion() {
      await this.$store.dispatch(
        "guesswordModule/editBlock",
        this.dataForsubmitForm()
      );
    },

    dataForsubmitForm() {
      const secondsTime = this.convertDateHoursMinutesAndSeconds(this.time);

      let data = {
        id: this.id,
        chapterId: this.chapterId,
        title: this.question,
        words_array: this.word,
        time: secondsTime,
        gametype: "guessword",
      };

      if (!this.modificarRegistro) {
        const { id, ...dataWithoutId } = data;
        return dataWithoutId;
      }

      return data;
    },

    async fetchQuestions() {
      const data = {
        chapterId: this.chapterId,
        gametype: "guessword",
      };

      await this.$store.dispatch("guesswordModule/reloadBlock", data);
    },
    timeUpdate(time) {
      this.time = time;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Guessword {
  background: #fff;
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.question {
  display: flex;
  flex-direction: column;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
}

.form {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 1rem;

  .form-first {
    flex-basis: 100%;
  }

  .form-second {
    flex: 1;
  }
}
.labelMensaje {
  width: 60%;
  padding: 0.5rem;
  align-content: center;
  color: red;
}
</style>
