import Vue from 'vue';
import CategoryFilter from './components/CategoryFilter';
import jQuery from 'jquery';

import '../../css/user.scss';

new Vue({
  delimiters: ['${', '}'],
  components: {CategoryFilter},
  data() {
    return {
      filters: [],
      filtersRequired: false,
    };
  },
  beforeMount() {
    if (this.$el.hasAttribute('user-filters')) {
      this.filters = JSON.parse(this.$el.attributes['user-filters'].value);
    }
  },
  mounted() {
    document.getElementById('User_filtersJson').value = JSON.stringify(this.filters);

    const saveButtons = document.querySelectorAll('.action-save');
    saveButtons.forEach((btn) => {
      btn.addEventListener('click', (event) => {
        if (this.filters.length > 0 && !this.hasSelectedFilters()) {
          event.preventDefault();

          this.filtersRequired = true;
          document.getElementById('filtersRequiredAlert').scrollIntoView();
        }
      });
    });
  },
  computed: {},
  methods: {
    onFilterUpdated(values) {
      document.getElementById('User_filtersJson').value = JSON.stringify(values);
      this.filtersRequired = false;
    },
    hasSelectedFilters() {
      let filters = JSON.parse(document.getElementById('User_filtersJson').value);
      for (let i = 0; i < filters.length; i++) {
        if (filters[i].selected.length) {
          return true;
        }
      }

      return true;
    },
  },
}).$mount('#app');