import Vue from 'vue';
import axios from "axios";

new Vue({
    delimiters: ['${', '}'],
    components: {},
    data() {
        return {
            email: null,
            recipients: [],
            page: 1,
            pageSize: 0,
            totalCount: 0,
            totalPages: 0,
        }
    },

    async created() {
        this.email = typeof email !== 'undefined' ? email : null;

        await this.paginate();
    },

    methods: {
        async paginate() {

            let url = '/admin/emailing/recipients/' + this.email + '/' + this.page;

            await axios.get(url)
                .then(response => {
                    this.recipients = response.data.data.recipients;
                    this.pageSize = response.data.data.pagination.numItemsPerPage;
                    this.totalCount = response.data.data.pagination.totalCount;
                    this.totalPages = response.data.data.pagination.countPages;
                })
        },

        nextPage() {
            this.page++;
        },

        prevPage() {
            this.page--;
        },
    },

    watch: {
        async page(){
            await this.paginate();
        },
    },
    computed: {
        init() {
            return this.page * this.pageSize - this.pageSize + 1;
        },

        end() {
            return Math.min(this.page * this.pageSize, this.totalCount);
        }
    }
}).$mount('#email-detail')
