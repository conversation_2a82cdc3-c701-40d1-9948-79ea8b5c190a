import Vue from 'vue';

import $ from 'jquery'
import 'bootstrap'
import './filters/index';
import '../../css/transitions.scss';
import axios from "axios";
import store from './store';
import QuizResults from "./components/results/QuizResults";
import RouletteResults from "./components/results/RouletteResults";
import RouletteWordsResults from "./components/results/RouletteWordsResult";
import PuzzleResults from "./components/results/PuzzleResults";
import TaskUserAnnouncement from "./components/results/TaskUserAnnouncement";
import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';

new Vue({
    delimiters: ['${', '}'],
    components: {QuizResults, RouletteResults, PuzzleResults, TaskUserAnnouncement, RouletteWordsResults},
    store,  
    data() {
        return {
            quizzes: [],
            roulette: [],
            puzzles: [],
            rouletteWords: [],
        }
    },
    async created() {

    },

    methods: {
        showResults(id, type) {
            this.quizzes = [];
            this.roulette = [];
            this.puzzles = [];
            this.rouletteWords = [];
console.log('hola',type);
            if (type === 'quiz') {
                this.showQuiz(id);
            } else if (type === 'roulette') {
                this.showRoulette(id);
            } else if (type === 'puzzle') {
                this.showPuzzle(id);
            } else if (type === 'letterswheel') {
                this.showRouletteWords(id);
            }
        },

        showQuiz(id) {
            const url = `/admin/subsidizer/user-chapter-results/${id}`;
            try {
                axios
                    .get(url)
                    .then(response => {
                        this.quizzes = response.data.data.quizzes;
                    });

                $('#results-modal').modal('show');
            } finally {
            }
        },

        showRoulette(id) {
            const url = `/admin/subsidizer/user-chapter-results/${id}`;
            try {
                axios
                    .get(url)
                    .then(response => {
                       // this.roulette = response.data.data.questions;
                    });
                $('#results-modal').modal('show');
            } finally {
            }
        },

        showRouletteWords(id) {
            const url = `/admin/subsidizer/user-chapter-results/${id}`;
            try {
                axios
                    .get(url)
                    .then(response => {
                        this.rouletteWords = response.data.data.questions;
                        console.log('respuesta', this.rouletteWords);

                    });
                $('#results-modal').modal('show');
            } finally {
            }
        },

        showPuzzle(id) {
            const url = `/admin/subsidizer/user-chapter-results/${id}`;
            try {
                axios
                    .get(url)
                    .then(response => {
                        this.puzzles = response.data.data.puzzles;
                    });

                $('#results-modal').modal('show');
            } finally {
            }
        }
    }

}).$mount('#subsidizer-user');

Vue.use(VueToast, {
    duration: 5000,
    position: 'top-right',
});
