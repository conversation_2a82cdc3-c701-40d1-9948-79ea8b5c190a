import axios from "axios";
import { make } from "vuex-pathify";

const getDefaultState = () => ({
  loading: false,
  questions: undefined,
  categoriesOptions: [],
  categories: [],
  routeChapter: "",
});

const state = () => getDefaultState();

export const getters = {
  isLoading: (state) => () => state.loading,

  getQuestions: (state) => () => state.questions,

  getCategoriesOptions: (state) => () => state.categoriesOptions,

  getCategories: (state) => () => state.categories,

  getRouteChapter: (state) => () => state.routeChapter,

  getHigherLower: (state) => () => state.higherLower,
};

export const mutations = {
  ...make.mutations(state),
};

export const actions = {
  async createQuestion({ commit }, formData) {
    const url = `/admin/save-question`;
    try {
      commit("SET_LOADING", true);
      const { data } = await axios.post(url, formData);

      return data;
    } catch (e) {
      console.log(e);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async updateQuestion({ commit }, formData) {
    const url = `/admin/update-question`;
    try {
      commit("SET_LOADING", true);
      const { data } = await axios.post(url, formData);

      return data;
    } catch (e) {
      console.log(e);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async deleteQuestion({ commit }, body) {
    const url = `/admin/delete-question`;
    try {
      commit("SET_LOADING", true);

      const { data } = await axios.post(url, body);

      return data;
    } catch (e) {
      console.log(e);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async fetchQuestionsGames({ commit }, idChapter) {
    const url = `/admin/list-questions/${idChapter}`;
    try {
      commit("SET_LOADING", true);
      const { data } = await axios.get(url);

      commit("SET_QUESTIONS", data?.data);
    } catch (e) {
      console.log(e);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async fetchCategoriesOptions({ commit }, chapterId) {
    const url = `/admin/categorize-options/${chapterId}`;
    try {
      commit("SET_LOADING", true);
      const { data } = await axios.get(url);

      commit("SET_CATEGORIES_OPTIONS", data?.data);
    } catch (e) {
      console.log(e);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async newCategoriesOptions({ commit }, formData) {
    try {
      commit("SET_LOADING", true);

      const url = "/admin/categorize-options/new";
      const { data } = await axios.post(url, formData);

      commit("SET_CATEGORIES_OPTIONS", data?.data);
    } catch (e) {
      console.log(e);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async editCategoriesOptions({ commit }, formData) {
    try {
      commit("SET_LOADING", true);

      const url = "/admin/categorize-options/edit";
      const { data } = await axios.post(url, formData);

      commit("SET_CATEGORIES_OPTIONS", data?.data);
    } catch (e) {
      console.log(e);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async deleteCategoryOptions({ commit }, formData) {
    try {
      commit("SET_LOADING", true);
      const { data } = await axios.post(
        "/admin/categorize-options/delete",
        formData
      );

      commit("SET_CATEGORIES_OPTIONS", data?.data);
      window.Vue.$toast.success(data?.message);
    } catch (e) {
      console.log(e);
      if (e.response.status === 409) {
        window.Vue.$toast.info(e.response.data.message);        
      }
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async fetchCategories({ commit }, chapterId) {
    const url = `/admin/categorize/${chapterId}`;
    try {
      commit("SET_LOADING", true);
      const { data } = await axios.get(url);

      commit("SET_CATEGORIES", data?.data);
      commit("SET_ROUTE_CHAPTER", data?.route);
    } catch (e) {
      console.log(e);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async newCategories({ commit }, formData) {
    const url = "/admin/categorize/new";
    try {
      commit("SET_LOADING", true);
      const { data } = await axios.post(url, formData);

      commit("SET_CATEGORIES", data?.data);
    } catch (e) {
      console.log(e);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async editCategories({ commit }, formData) {
    const url = "/admin/categorize/edit";
    try {
      commit("SET_LOADING", true);
      const { data } = await axios.post(url, formData);

      commit("SET_CATEGORIES", data?.data);
    } catch (e) {
      console.log(e);
    } finally {
      commit("SET_LOADING", false);
    }
  },

  async deleteCategories({ commit }, formData) {
    try {
      commit("SET_LOADING", true);
      const { data } = await axios.post("/admin/categorize/delete", formData);

      commit("SET_CATEGORIES", data?.data);
    } catch (e) {
      console.log(e);
    } finally {
      commit("SET_LOADING", false);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
