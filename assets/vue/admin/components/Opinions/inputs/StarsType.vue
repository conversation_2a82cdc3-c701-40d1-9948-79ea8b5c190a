<template>
  <div class="StarsType">
    <p class="my-0">{{ question }}</p>
    <div class="rating d-flex flex-row nowrap align-items-center">
      <i v-for="i in 5" class="fa" :class="star(i)" :key="i"></i>
    </div>
  </div>
</template>

<script>
import typesMixin from './typesMixin'

export default {
  name: "StarsType",
  mixins: [typesMixin],
  computed: {
    rating() {
      return this.answers / 2;
    },
  },
  methods: {
    star(index) {
      const rating = this.rating;
      if (rating - Math.floor(rating) !== 0) {
        if (index - 1 === Math.floor(rating)) return "fa-star-half-alt active";
      }
      return index <= rating ? "fa-star active" : "fa-star";
    },
  },
}
</script>

<style scoped lang="scss">
.StarsType {
  .rating {
    justify-self: right;
    font-size: 1.5rem;
    
    .fa-star {
      color: var(--color-neutral-mid);
    }
    
    .fa-star.active,
    .fa-star-half-alt.active {
      color: #f7be73;
    }
  }
}
</style>