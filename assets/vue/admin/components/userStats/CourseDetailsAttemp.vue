<template>
<div class="CourseDetailsAttemp" :class="{active}" @click="$emit('click')">
  <p class="title mb-2 font-weight-bold">
    <i class="fa fa-check-circle text-success" v-if="attempt.state === 'ok'"/>
    <i class="fa fa-times-circle text-danger" v-else/>
    {{ $t('Attempt') }} {{attempt.attemp}}
  </p>
  <div class="dateContainer">
    <p class="my-0"><span class="font-weight-bold">{{ getDate(attempt.date) }}</span> {{ getTime(attempt.date) }}</p>
    <p class="my-0"><span class="font-weight-bold">{{ dateEnd[0] }}</span> {{ dateEnd[1] }}</p>
  </div>
  <span class="totalTime">{{ getTimeText() }}</span>
</div>
</template>

<script>
export default {
  name: "CourseDetailsAttemp",
  props: {
    attempt: {
      type: Object,
      default: () => ({})
    },
    active: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    dateEnd() {
      const initDate = new Date(this.attempt.date)?.getTime();
      if (isNaN(initDate)) return ['', '']

      const { timeTotal } = this.attempt
      const endDate = new Date(initDate + (timeTotal * 1000))

      const date = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${
          String(endDate.getDate()).padStart(2, '0')}`

      const time = `${
        String(endDate.getHours()).padStart(2, '0')}:${
        String(endDate.getMinutes()).padStart(2, '0')}:${
        String(endDate.getSeconds()).padStart(2, '0')}`

      return [date, time]
    }
  },
  methods: {
    getDate(date) {
      if (!(date || '').length) return '';
      return date.split(' ')[0] || ''
    },
    getTime(date) {
      if (!(date || '').length) return '';
      const [hour, minute, seconds] = (date.split(' ')[1] || ['', '', '']).split(':')
      return `${String(hour || 0).padStart(2, '0')}:${
          String(minute || 0).padStart(2, '0')}:${
          String(seconds || 0).padStart(2, '0')}`
    },
    getTimeText() {
      const { timeTotal } = this.attempt
      const secsToMin = parseInt(`${ timeTotal / 60}`)
      const seconds = timeTotal - (secsToMin * 60)

      const hours = parseInt(`${secsToMin / 60}`)
      const minutes = secsToMin - (hours * 60)

      const hourText = `${hours} ${this.$tc('TIME.HOURS', [hours])}`
      const minuteText = `${minutes} ${this.$tc('TIME.MINUTES', [minutes])}`
      const secondText = `${seconds} ${this.$tc('TIME.SECONDS', [seconds])}`

      if (hours) {
        if (minutes && seconds) return `${hourText}, ${minuteText} ${this.$t('TIME.AND')} ${secondText}`
        if (minutes) return `${hourText} ${this.$t('TIME.AND')} ${minuteText}`
        if (seconds) return `${hourText} ${this.$t('TIME.AND')} ${secondText}`
        return `${hourText}`
      }
      if (minutes) {
        return (seconds) ? `${minuteText} ${this.$t('TIME.AND')} ${secondText}` : `${minuteText}`
      }
      return (seconds) ? `${secondText}` : '--'
    }
  }
}
</script>

 <style scoped lang="scss"> 
.CourseDetailsAttemp {
  text-align: center;
  border-radius: 0.5rem;
  padding: 0.5rem;
  background-color: var(--color-neutral-mid-dark);
  opacity: 0.7;
  cursor: pointer;

  .title {
    line-break: anywhere;
    font-size: 0.9rem;
  }

  .dateContainer {
    display: grid;
    grid-template-columns: 4rem 4rem;
    gap: 1rem;
    font-size: 0.7rem;
    text-align: center;
    justify-content: center;
  }

  .totalTime {
    display: block;
    font-weight: bold;
    font-size: 0.7rem;
  }

  &.active {
    background-color: white;
    border: 2px solid var(--color-primary);
    opacity: 1;
  }
}
</style>
