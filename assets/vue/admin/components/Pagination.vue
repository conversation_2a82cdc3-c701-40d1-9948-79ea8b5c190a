<template>
  <ul class="Pagination p-0" v-show="totalPages > 1">
    <li v-if="generateChips.length">
      <a class="chip prev" @click="setCurrentPage(currentPage > 1 ? currentPage - 1 : 1)">
        <i class="fas fa-angle-left"></i>
      </a>
    </li>

    <li
      v-for="chip in generateChips"
      :class="['chip', {'active': currentPage === chip.number}]"
      v-show="chip.visible"
    >
      <a @click="setCurrentPage(chip.number)">{{ chip.number }}</a>
    </li>

    <li v-if="generateChips.length">
      <a class="chip next" @click="setCurrentPage(currentPage < totalPages ? currentPage + 1 : totalPages)">
        <i class="fas fa-angle-right"></i>
      </a>
    </li>
  </ul>
</template>

<script>
export default {
  name: "Pagination",
  props: {
    items: null,//If items is null then emit currentPage, otherwise emit items
    totalItems: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    numberOfChips: {
      type: Number,
      default: 4
    },
    propCurrentPage: {
      type: Number,
      default: 1
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentPage: this.propCurrentPage,
      pages: []
    };
  },
  computed: {
    prevPage() {
      return this.currentPage > 1 ? this.currentPage - 1 : 1;
    },
    totalPages() {
      if (this.pageSize > 0 ) {
        const total = this.items ? this.items.length : this.totalItems;
        return Math.ceil(total / this.pageSize);
      }
      return 0;
    },
    nextPage() {
      return this.currentPage < this.totalPages ? this.currentPage + 1 : this.totalPages;
    },
    generateChips() {
      const totalPages = this.totalPages;
      const currentPage = this.currentPage;
      const pages = [];
      for (let i = 1; i <= totalPages; i++) {
        let visible = false;
        if (currentPage <= 4 && i <= 5) {
          visible = true;
        } else if(currentPage > 4) {
          const defMin = totalPages - 4;
          let min = currentPage - 2;
          min = min > defMin ? defMin : min;
          const max = currentPage + 2;
          visible = i >= min && i <= max;
        } else {
          visible = false;
        }
        pages.push({number: i, active: false, visible: visible})
      }
      if (pages.length > 0) {
        pages[0].visible = true;
        pages[pages.length - 1].visible = true;
      }
      return pages;
    }
  },
  watch: {
    items() {
      this.generatePages();
      this.emitData();
    },
    currentPage(page) {
      this.emitData();
    },
    propCurrentPage() {
      this.currentPage = this.propCurrentPage;
    }
  },
  methods: {
    emitData() {
      if (this.items) {
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        const items = this.items.slice(start, end);
        this.$emit('items-page', items);
        this.$emit('current-page', this.currentPage);
      } else {
        this.$emit('current-page', this.currentPage);
      }
    },
    generatePages() {
      const totalPages = this.totalPages;
      const currentPage = this.currentPage;
      const pages = [];
      for (let i = 1; i <= totalPages; i++) {
        let visible = false;
        if (currentPage <= 4 && i <= 5) {
          visible = true;
        } else if(currentPage > 4) {
          const defMin = totalPages - 4;
          let min = currentPage - 2;
          min = min > defMin ? defMin : min;
          const max = currentPage + 2;
          visible = i >= min && i <= max;
        } else {
          visible = false;
        }
        pages.push({number: i, active: false, visible: visible})
      }
      if (pages.length > 0) {
        pages[0].visible = true;
        pages[pages.length - 1].visible = true;
      }
      this.pages = pages;
    },
    setCurrentPage(value) {
      if (this.disabled) return null
      this.currentPage = value
    }
  }
}
</script>

 <style scoped lang="scss"> 
.Pagination {
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  list-style: none;
  margin: 1rem 0;
  gap: 2px;

  li a{
    padding: 0.4rem 0.8rem;
  }

  .chip {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    color: var(--color-neutral-dark);
    font-size: var(--font-size-s);
    height: 100%;
    text-decoration: none;

    &.active {
      background: var(--color-primary);
      color: var(--color-neutral-lightest);
    }

    &:not(.active):hover{
      background: var(--color-primary-lighter);
    }
  }
}
</style>
