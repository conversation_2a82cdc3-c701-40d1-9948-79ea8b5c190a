<template>
  <div class="GameBlock">
    <div class="select-block">
      <select class="select-type" id="type" v-model="option">
        <option v-for="({ value, text }, i) in options" :key="i" :value="value">
          <div v-html="text" />
        </option>
      </select>
    </div>

    <div class="question">
      <BaseTextTarea
        :label="translationsVue.rouletteWord_configureFields_statement"
        :max="150"
        :value.sync="statement"
        :placeholder="translationsVue.pairs_configureFields_placeholder_title"
        :required="true"
        :rows="4"
        :submitted="submitted"
      ></BaseTextTarea>

      <FormError
        v-if="statementErrorMessage"
        :message="statementErrorMessage"
      />
    </div>

    <div class="response">
      <BaseTextTarea
        :label="translationsVue.rouletteWord_configureFields_answer"
        :max="25"
        :value.sync="answer"
        :placeholder="translationsVue.games_help_write_answer"
        :required="true"
        :rows="1"
        :submitted="submitted"
      ></BaseTextTarea>

      <FormError v-if="answerErrorMessage" :message="answerErrorMessage" />
    </div>

    <div class="button-zone">
      <button
        v-if="letter.id"
        data-dismiss="modal"
        class="btn btn-secondary"
        data-bs-toggle="modal"
        :data-bs-target="`#deleteModal`"
      >
        {{ translationsVue.Delete }}

        <i v-if="deleteButtonLoading" class="loader fa fa-sync-alt" />
      </button>

      <BaseModalDelete
        :identifier="`deleteModal`"
        :title="translationsVue.quiz_configureFields_question_delete"
        @delete-element="emit('delete')"
      />

      <button
        data-dismiss="modal"
        class="btn btn-primary"
        :disabled="statementErrorMessage || answerErrorMessage"
        @click="emit('save')"
      >
        {{ translationsVue.Save }}

        <i v-if="saveButtonLoading" class="loader fa fa-sync-alt" />
      </button>
    </div>
  </div>
</template>

<script>
const STATEMENT_MAX_CHARACTERS = 150;
const ANSWER_MAX_CHARACTERS = 25;
const TYPE_OPTIONS = Object.freeze({
  STARTS_WITH: 0,
  INCLUDES: 1,
});
const EMITS = Object.freeze({
  SAVE: "save",
  DELETE: "delete",
});

import FormError from "../common/FormError";

export default {
  components: {
    FormError,
  },

  props: {
    loading: {
      type: Boolean,
      default: false,
    },

    letter: {
      type: Object,
      required: true,
    },

    submitted: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      statementMaxCharacters: STATEMENT_MAX_CHARACTERS,
      answerMaxCharacters: ANSWER_MAX_CHARACTERS,
      translationsVue,
    };
  },

  computed: {
    deleteButtonLoading() {
      return this.loading && this.action === EMITS.DELETE;
    },

    saveButtonLoading() {
      return this.loading && this.action === EMITS.SAVE;
    },

    character() {
      return this.letter?.letter ?? "?";
    },

    option: {
      get() {
        return Number(this.letter?.type ?? 0);
      },

      set(value) {
        this.$emit("update", { type: Boolean(value) });
      },
    },

    statement: {
      get() {
        return this.letter?.question ?? "";
      },

      set(value) {
        this.$emit("update", { question: value });
      },
    },

    answer: {
      get() {
        return this.letter?.word ?? "";
      },

      set(value) {
        this.$emit("update", { word: value });
      },
    },

    options() {
      const startsWithText = `${this.translationsVue.rouletteWord_configureFields_type_0} - ${this.character}`;
      const includesText = `${this.translationsVue.rouletteWord_configureFields_type_1} - ${this.character}`;

      return [
        { value: TYPE_OPTIONS.STARTS_WITH, text: startsWithText },
        { value: TYPE_OPTIONS.INCLUDES, text: includesText },
      ];
    },

    statementRemainingCharacters() {
      return this.statement?.length ?? 0;
    },

    answerRemainingCharacters() {
      return this.answer?.length ?? 0;
    },

    statementError() {
      return this.statementRemainingCharacters > this.statementMaxCharacters;
    },

    statementErrorMessage() {
      if (this.statementError) {
        return this.translationsVue.rouletteWord_configureFields_error_statement_max.replace(
          "{max}",
          this.statementMaxCharacters
        );
      }

      if (this.statementRemainingCharacters === 0) {
        return this.translationsVue
          .rouletteWord_configureFields_error_statement_empty;
      }

      return undefined;
    },

    answerError() {
      return this.answerRemainingCharacters > this.answerMaxCharacters;
    },

    answerErrorMessage() {
      if (this.answerError) {
        return this.translationsVue.rouletteWord_configureFields_error_answer_max.replace(
          "{max}",
          this.answerMaxCharacters
        );
      }

      if (this.answerRemainingCharacters === 0) {
        return this.translationsVue
          .rouletteWord_configureFields_error_answer_empty;
      }

      const startsWithRegexp = new RegExp(`^${this.character}`, "i");
      const startCorrectly = startsWithRegexp.test(this.answer);
      if (this.option === TYPE_OPTIONS.STARTS_WITH && !startCorrectly) {
        return `${this.translationsVue.rouletteWord_configureFields_error_answer_starts} - ${this.character}`;
      }

      const includesRegexp = new RegExp(`${this.character}`, "i");
      const includesCorrectly = includesRegexp.test(this.answer);
      if (this.option === TYPE_OPTIONS.INCLUDES && !includesCorrectly) {
        return `${this.translationsVue.rouletteWord_configureFields_error_answer_includes} - ${this.character}`;
      }

      return undefined;
    },

    statementCounter() {
      return this.counterTemplate(
        this.statementRemainingCharacters,
        this.statementMaxCharacters
      );
    },

    answerCounter() {
      return this.counterTemplate(
        this.answerRemainingCharacters,
        this.answerMaxCharacters
      );
    },
  },

  methods: {
    emit(action) {
      if (!Object.values(EMITS).includes(action)) {
        throw new Error(`Action ${action} is not allowed`);
      }

      this.action = action;
      this.$emit(action);
    },

    counterTemplate(current, total) {
      return `${current} / <b>${total}</b>`;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.GameBlock {
  display: grid;
  gap: 1rem;

  .button-zone {
    display: flex;
    justify-content: end;
    gap: 0.5rem;
  }

  .game-block {
    height: 30rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .label-block {
    display: flex;
    justify-content: space-between;

    p {
      margin: 0;
    }
  }

  .question,
  .response {
    display: flex;
    flex-direction: column;
  }

  #text,
  #response {
    border: none;
    // border-radius: 7px;
  }

  #text {
    height: 10rem;
    padding: 0.5rem 0.75rem;
  }

  #response {
    padding: 0.5rem 0.75rem;
  }

  label {
    text-transform: uppercase;
    font-weight: 500;
  }

  select {
    border: none;
    border-radius: 5px;
    padding: 0.5rem 0.75rem;
  }

  .options {
    height: 100%;
    flex: 2;

    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }

  input,
  textarea {
    margin-bottom: $spacing-xs;
  }

  .loader {
    animation: spin 1s linear infinite;
    margin-left: $spacing-2xs;
    font-size: var(--font-size-s);
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
