<!-- Vue component -->
<template>
  <div class="BaseSelect">
    <label v-bind:for="labelID">{{label}}</label>
    <div>
      <select v-bind:id="labelID" v-model="selectModel" class="form-control">
        <option value="">{{ empty }}</option>
        <option
            v-for="(item, i) in newList" :key="i" v-bind:value="item.id"> {{ item.name }}
        </option>
      </select>
    </div>
  </div>
</template>

<script>
export default {
  name : 'custom-multi-select',
  props: ['value', 'empty', 'list', 'label'],
  model: {
    prop: 'value',
    event: 'change'
  },
  data () {
    return {
      localValue: (this.value || []).map(v => v.id),
      selectModel : '',
      labelID : ''
    }
  },
  created() {
    this.labelID = 'inputDate' + Math.floor((Math.random() * 1000))
  },
  methods: {
    addTag (newTag) {
      const tag = {
        name: newTag,
        code: newTag.substring(0, 2) + Math.floor((Math.random() * 10000000))
      }
      this.list.push(tag)
      this.localValue.push(tag)
    }
  },
  watch: {
    value(newValue) {
      this.localValue = newValue
    },
    selectModel(newValue) {
      if (newValue) {
        this.$emit('change', [...(this.value || []), newValue]);
        this.selectModel = '';
      }
    }
  },
  computed : {
    newList : function () {
      return (this.localValue ? this.list.filter(l => !this.localValue.find(valueID => valueID === l.id)) : this.list) || [];
    }
  }
}
</script>

<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>

<style scoped lang="scss">
.BaseSelect{
  label {
    text-transform: uppercase;
    font-weight: 500;
    padding: 0 .5rem;
    margin-bottom: .25rem;
  }

  .form-control {
    background-color: #FFFFFF;
    border: 1px solid #E5E5E5;
    border-radius: 50px;
  }
}
</style>
