<template>
  <div class="UploadVideo">
    <div v-if="load == false">
      <div class="selectOption">
        <label for="select" v-if="translate">{{ translate.type }} </label>
        <select v-model="selected" class="form-select">
          <option disabled value="">Selecciona una opción</option>
          <option value="Url">
            {{ translationsVue.component_video_url_video }}
          </option>
          <option value="Archivo">
            {{ translationsVue.documentation_file }}
          </option>
        </select>
      </div>

      <div v-if="selected == 'Url'" class="inputUrl">
        <label for="url" v-if="translate">{{ translate.url_video }}</label>

        <div>
          <form>
            <input
              type="text"
              name="url"
              class="form-control"
              id="url"
              v-model="url"
              required
            />
          </form>
        </div>
      </div>

      <div v-if="selected == 'Archivo'" class="uploadFile">
        <label for="file" v-if="translate">{{
          translate.upload_file_video
        }}</label>
        <form>
          <input
            type="file"
            name="file"
            class="form-control-file"
            id="file"
            accept="video/*"
            @change="fileSelected($event.target.files)"
          />
        </form>
      </div>

      <div v-if="selected == 'Archivo'">
        <br />
        <label class="container"
          ><p v-if="translate">
            {{ translate.file_subtitle }} <strong>"{{ locale }}"</strong>
          </p>
          <input type="checkbox" v-model="track" />
          <span class="checkmark"></span>
        </label>
      </div>

      <div v-if="track && selected == 'Archivo'" class="uploadFile">
        <form>
          <input
            type="file"
            name="texttrack"
            class="form-control-file"
            id="file"
            accept=".srt, .vtt"
            @change="textTrackSelected($event.target.files)"
          />
        </form>
      </div>

      <div class="text-center mt-3">
        <button class="btn btn-primary" @click="savePackageVideo()">
          {{ translationsVue.Save }}
        </button>
      </div>
    </div>

    <div v-if="load && selected == 'Archivo'">
      <h3 class="text-center" v-if="translate">
        {{ translate.preparing_file }}
      </h3>
      <spinner />
    </div>
    <spinner v-if="load && selected == 'Url'" />
  </div>
</template>
<script>
import Spinner from "../base/Spinner";

export default {
  name: "new-video",

  components: {
    Spinner,
  },

  props: ["chapter", "vimeouploadsubdomain", "locale", "translate"],

  data() {
    return {
      selected: "Url",
      chapterId: 7,
      url: "",
      preview: null,
      video: null,
      detailVideo: undefined,
      load: false,
      track: false,
      fileTextTrack: "",
      translationsVue,
    };
  },

  mounted() {
    const referer = document.referrer;
    console.log(referer)
  },

  methods: {
    fileSelected(files) {
      if (!files || files.length === 0) return;

      this.video = files[0];
    },

    textTrackSelected(files) {
      if (!files || files.length === 0) return;
      this.fileTextTrack = files[0];
    },

    async savePackageVideo() {
      const referer = document.referrer;
      try {
        const data = {
          newVideo: this.video,
          typeVideo: "2",
          chapter: this.chapter,
          namePackage: this.selected,
          domain: this.vimeouploadsubdomain,
          track: this.track,
          fileTextTrack: this.fileTextTrack,
          language: this.locale,
        };

        const dataUrl = {
          typeVideo: "1",
          chapter: this.chapter,
          urlVideo: this.url,
          namePackage: this.selected,
          referer: referer
        };

        // Expresiones regulares para detectar si es de Vimeo o YouTube
        const regexVimeo = /vimeo\.com\/[0-9]{1,}/;
        const regexYoutube = /(youtube\.com\/watch\?v=[a-zA-Z0-9]{1,}|youtu\.be\/[a-zA-Z0-9_-]{1,})/;

        if (this.selected == "Url" && this.url != "") {
          this.load = true;

          if (regexVimeo.test(this.url) || regexYoutube.test(this.url)) {
            const result = await this.$store.dispatch(
              "uploadModule/newPackageVideoByUrl",
              dataUrl
            );
            window.location.href = result?.route
          } else {
            this.$toast.open({
              message: "El link no es de Vimeo o Youtube",
              type: "error",
              duration: 5000,
              position: "top-right",
            });
          }

          //location.reload();
        } else if (this.selected == "Archivo" && this.video != null) {
          this.load = true;
          const detailVideo = await this.$store.dispatch(
            "uploadModule/newVideoByVimeo",
            data
          );

          window.location.href = `/admin/chapter/${this.chapter}/redirect-to-course?ref=${referer}`;
        }
      } catch (error) {
        this.$toast.open({
          message: "ha ocurrido un error",
          type: "error",
          duration: 5000,
          position: "top-right",
        });

        this.load = false;
      }
    },
  },
};
</script>

 <style scoped lang="scss"> 
.UploadVideo {
  width: 100%;
  background: white;
  padding: 1rem;

  select {
    width: 100%;
    height: 2rem;
  }

  .inputUrl {
    margin-top: 1rem;

    input {
      height: 2rem;
    }
  }

  .uploadFile {
    margin-top: 1rem;

    input[type="file"] {
      height: 4rem;
      background: #f7fbff;
      padding: 1rem;
      border-radius: 0.2rem;
      border: solid 1px;
    }
  }

  .btnSave {
    width: 100%;
    margin-top: 1rem;
    height: 3rem;
    background: #68bbb0;
    color: white;
    border: none;
    border-radius: 0.3rem;
  }
}

/* The container */
.container {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  font-size: 1rem;
}

/* Hide the browser's default checkbox */
.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #eee;
  border: solid 1px rgb(29, 28, 28);
}

.container:hover input ~ .checkmark {
  background-color: #ccc;
}

.container input:checked ~ .checkmark {
  background-color: #2196f3;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.container input:checked ~ .checkmark:after {
  display: block;
}

.container .checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
</style>
