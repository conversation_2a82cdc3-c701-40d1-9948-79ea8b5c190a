<template>
  <div class="EditVideo" v-if="detailVideo">
    <div v-if="load == false">
      <div class="selectOption">
        <label for="select" v-if="translate"> {{ translate.type }}</label>
        <select v-model="selected" class="form-select">
          <option disabled value="">Selecciona una opción</option>
          <option value="Url">
            {{ translationsVue.component_video_url_video }}
          </option>
          <option value="Archivo">
            {{ translationsVue.documentation_file }}
          </option>
        </select>
      </div>

      <div v-if="selected == 'Url'" class="inputUrl">
        <label for="url" v-if="translate">{{ translate.url_video }}</label>

        <div>
          <input
            type="text"
            name="url"
            class="form-control"
            id="url"
            v-model="detailVideo.url"
          />
        </div>
      </div>

      <div v-if="selected == 'Archivo'" class="uploadFile">
        <label for="file">Subir archivo vídeo</label>
        <input
          type="file"
          name="file"
          class="form-control-file"
          id="file"
          accept="video/*"
          @change="fileSelected($event.target.files)"
        />
      </div>

      <div
        v-if="
          (detailVideo.origen == 'vimeo' && detailVideo.type == '2') ||
          selected == 'Archivo'
        "
      >
        <br />
        <label class="container"
          ><p v-if="translate">
            {{ translate.file_subtitle }} <strong>"{{ locale }}"</strong>
          </p>
          <input type="checkbox" v-model="track" />
          <span class="checkmark"></span>
        </label>
      </div>

      <div
        class="alert alert-warning alert-dismissible fade show"
        role="alert"
        v-if="languagestTextTrackVideo && track && selected == 'Url'"
      >
        <p v-if="translate">{{ translate.text_content_subtitle_video }}</p>
        <button
          type="button"
          class="close"
          data-dismiss="alert"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>

      <div
        v-if="
          (track && detailVideo.origen == 'vimeo' && detailVideo.type == '2') ||
          (selected == 'Archivo' && track)
        "
        class="uploadFile"
      >
        <form>
          <input
            type="file"
            name="texttrack"
            class="form-control-file"
            id="file"
            accept=".srt, .vtt"
            @change="textTrackSelected($event.target.files)"
          />
        </form>
      </div>

      <div class="text-center mt-3">
        <button class="btn btn-primary" @click="updatePackageVideo()">
          <span v-if="translate"> {{ translate.button_save }}</span>
        </button>
      </div>
    </div>

    <div v-if="load && selected == 'Archivo'">
      <h3 class="text-center" v-if="translate">
        {{ translate.preparing_file }}
      </h3>
      <spinner />
    </div>

    <spinner v-if="load && selected == 'Url'" />
  </div>
</template>

<script>
import Spinner from "../base/Spinner";

export default {
  name: "edit-video",

  components: {
    Spinner,
  },

  props: ["chapter", "idvideo", "vimeouploadsubdomain", "locale", "translate"],

  data() {
    return {
      selected: "Url",
      url: "",
      preview: null,
      video: null,
      detailVideo: undefined,
      detailVideoCopy: {
        id: undefined,
        url: undefined,
        chapter: undefined,
      },
      load: false,
      track: false,
      fileTextTrack: "",
      translationsVue,
    };
  },

  computed: {
    languagestTextTrackVideo() {
      if (this.detailVideo && this.detailVideo.textTrackVideo) {
        const detailVideo = this.detailVideo;
        let result = detailVideo.textTrackVideo.filter(
          (textTrack) => (textTrack.language = this.locale)
        );
        return result;
      } else {
        return "";
      }
    },
  },

  async created() {
    const detailVideo = await this.$store.dispatch(
      "uploadModule/detailVideo",
      this.idvideo
    );
    console.log("Details video", detailVideo);
    this.detailVideo = detailVideo.data.data;
  },

  methods: {
    fileSelected(files) {
      if (!files || files.length === 0) return;

      this.video = files[0];
    },

    textTrackSelected(files) {
      if (!files || files.length === 0) return;

      this.fileTextTrack = files[0];
    },

    async updatePackageVideo() {
      try {
        const data = {
          id: this.detailVideo.id,
          newVideo: this.video,
          typeVideo: "2",
          chapter: this.detailVideo.chapter,
          namePackage: this.selected,
          identifier: this.detailVideo.identifier,
          typePackage: this.detailVideo.name,
          domain: this.vimeouploadsubdomain,
          track: this.track,
          fileTextTrack: this.fileTextTrack,
          language: this.locale,
        };

        const dataUrl = {
          id: this.detailVideo.id,
          typeVideo: this.detailVideo.type,
          chapter: this.detailVideo.chapter,
          urlVideo: this.detailVideo.url,
          namePackage: this.selected,
          identifier: this.detailVideo.identifier,
          track: this.track,
          origen: this.detailVideo.origen,
          fileTextTrack: this.fileTextTrack,
          language: this.locale,
          uriTextTrack: this.languagestTextTrackVideo[0]?.uri ?? "",
        };

        const regexVimeo = /vimeo\.com\/[0-9]{1,}/;
        const regexYoutube = /(youtube\.com\/watch\?v=[a-zA-Z0-9]{1,}|youtu\.be\/[a-zA-Z0-9_-]{1,})/;

        if (this.selected == "Url") {
          if (
            regexVimeo.test(this.detailVideo.url) ||
            regexYoutube.test(this.detailVideo.url)
          ) {
            this.load = true;

            await this.$store.dispatch(
              "uploadModule/updatePackageVideoByUrl",
              dataUrl
            );
            location.reload();
          } else {
            this.$toast.open({
              message: "El link no es de Vimeo o Youtube",
              type: "error",
              duration: 5000,
              position: "top-right",
            });
          }
        } else if (this.selected === "Archivo") {
          this.load = true;
          await this.$store.dispatch("uploadModule/updateVideoByVimeo", data);
          location.reload();
        }
      } catch (error) {
        this.$toast.open({
          message: "ha ocurrido un error",
          type: "error",
          duration: 5000,
          position: "top-right",
        });
        this.load = false;
      }
    },

    customLabel({ name }) {
      return `${name}`;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.EditVideo {
  width: 100%;
  background: white;
  padding: 1rem;

  select {
    width: 100%;
    height: 2rem;
  }

  .inputUrl {
    margin-top: 1rem;
    input {
      height: 2rem;
    }
  }

  .uploadFile {
    margin-top: 1rem;
    input[type="file"] {
      height: 4rem;
      background: #f7fbff;
      padding: 1rem;
      border-radius: 0.2rem;
      border: solid 1px;
    }
  }

  .btnSave {
    width: 100%;
    margin-top: 1rem;
    height: 3rem;
    background: #68bbb0;
    color: white;
    border: none;
    border-radius: 0.3rem;
  }
}

/* The container */
.container {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  font-size: 1rem;
}

/* Hide the browser's default checkbox */
.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 25px;
  width: 25px;
  background-color: #eee;
  border: solid 1px rgb(29, 28, 28);
}

.container:hover input ~ .checkmark {
  background-color: #ccc;
}

.container input:checked ~ .checkmark {
  background-color: #2196f3;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.container input:checked ~ .checkmark:after {
  display: block;
}

.container .checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
</style>
