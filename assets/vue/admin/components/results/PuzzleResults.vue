<template>
    <div class="results">
        <div class="header">
            <h3>Intento {{ number }}</h3>
            <span class="results-info">({{ correctAnswers }}/{{ puzzle.length }})</span>
            <span class="toggle-questions fas"
                  :class="show ? 'fa-minus' : 'fa-plus'"
                  @click="show = !show"

            ></span>
        </div>
        <div class="questions" v-show="show">
            <div class="question" v-for="(item, index) in puzzle"  :key="index">
                <h5>{{ item.question.question }}</h5>
                <div class="answers">
                    <div class="answer"
                         v-for="(answer, option) in item.question.answers"
                         :class="answer.id === item.id ? 'selected' : ''"
                    >
                        <div class="answer-option"
                             :class="answer.correct ? 'correct' : answer.id === item.id ? 'wrong' : ''"
                        >{{ options[option] }}</div>
                        <div class="answer-text">{{ answer.answer }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    name: 'PuzzleResults',
    props: ['puzzle', 'number'],

    data() {
        return {
            options: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],
            show: false,
        }
    },

    computed: {

        correctAnswers: function () {
            let correct = 0;
            for (let i = 0; i < this.puzzle.length; i++) {
                if (this.puzzle[i].correct) {
                    correct++;
                }
            }

            return correct;
        },
    }
};
</script>

 <style scoped lang="scss"> 
</style>
