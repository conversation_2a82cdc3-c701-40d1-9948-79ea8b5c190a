
<template>
  <div class="task-by-user" v-if="taskByUser && taskByUser.length > 0">
    <h4>{{ translationsVue.course_configureFields_task }}</h4>
    <div class="accordion accordion-flush" id="accordionFlushExample">
      <div
        class="accordion-item"
        v-for="(task, index) in taskByUser"
        :key="task.id"
      >
        <h2 class="accordion-header" :id="`flush-heading${task.task.id}`">
          <button
            class="accordion-button collapsed"
            type="button"
            data-bs-toggle="collapse"
            :data-bs-target="`#flush-collapse${task.task.id}`"
            aria-expanded="false"
            :aria-controls="`flush-collapse${task.task.id}`"
          >
            <span class="h4" style="margin-right: 0.5rem">
              {{ index + 1 }}</span
            >
            <span
              :style="{
                background: colorButton(task.lastHistory.state),
                minWidth: '6rem',
              }"
              class="badge"
            >
              {{ stateTask(task.lastHistory.state) }}
            </span>

            <span style="margin-left: 0.5rem">{{ task.task.title }}</span>
          </button>
        </h2>
        <div
          :id="`flush-collapse${task.task.id}`"
          class="accordion-collapse collapse"
          :aria-labelledby="`flush-heading${task.task.id}`"
          data-bs-parent="#accordionFlushExample"
        >
          <div class="accordion-body">
            <div class="header-title">
              <div class="row">
                <div class="col-md-8 col-sms-12">
                  <h6 class="card-subtitle mb-2 text-muted">
                    {{
                      translationsVue.taskCourse_configureFields_dateDelivery
                    }}
                    : {{ task.task.dateDelivery }}
                  </h6>
                  <h6
                    class="card-subtitle mb-2 text-muted"
                    v-if="task.lastHistory.state > 0"
                  >
                    {{ translationsVue.taskCourse_configureFields_senTaskUser }}
                    : {{ task.lastHistory.updatedAt }}
                  </h6>
                </div>

                <div class="col-md-4 col-sm-12 text-right">
                  <div class="btn-group" v-if="task.lastHistory.state > 0 && origin === ''">
                    <select
                      :style="{
                        color: colorButton(task.lastHistory.state),
                      }"
                      class="form-select"
                      aria-label="Default select example"
                      v-model="state"
                      @change="ChangeSelection($event, task.lastHistory.id)"
                    >
                      <option>Seleccione una opción</option>
                      <option
                        v-for="option in options"
                        :key="option.id"
                        :value="option.id"
                      >
                        {{ option.name }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="text-card" v-html="task.task.description"></div>

              <div class="history-task">
                <HistoryTask
                  :history-task-user="task.historyTask"
                  :origin="origin"
                  @fetch-task-User="fetchTaskUser()"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import BaseSelect from "../base/BaseSelect";
import HistoryTask from "../call/task_by_user/HistoryTask";

const colorState = {
  state0: "#A0A6AB",
  state1: "#31D2F2",
  state2: "#c370d3",
  state3: "#EA4335",
  state4: "#19863A",
};

export default {
  components: {
    HistoryTask,
    BaseSelect,
  },

  props: {
    idUser: {
      type: Number,
      required: true,
    },

    origin:{
      type: String,
      default: ''
    }
  },

  data() {
    return {
      translationsVue,
      state: 2,
      options: [
        {
          id: 1,
          name: translationsVue.taskCourse_configureFields_state_1,
        },
        {
          id: 2,
          name: translationsVue.taskCourse_configureFields_state_2,
        },
        {
          id: 3,
          name: translationsVue.taskCourse_configureFields_state_3,
        },
        {
          id: 4,
          name: translationsVue.taskCourse_configureFields_state_4,
        },
      ],
    };
  },

  computed: {
    ...get("callModule", ["isLoading", "getTaskByUser"]),

    taskByUser() {
      return this.getTaskByUser();
    },
  },

  created() {
    this.fetchTaskUser();
  },

  methods: {
    async fetchTaskUser() {
      const data = {
        idUser: this.idUser,
        announcement: idAnnouncement,
      };

      await this.$store.dispatch("callModule/fetchTaskByUser", data);
    },

    colorButton(state) {
      return colorState[`state${state}`];
    },

    stateTask(state) {
      if (state === 1) {
        this.state = parseInt(state, 10);
        return this.translationsVue.taskCourse_configureFields_state_1;
      } else if (state === 2) {
        this.state = parseInt(state, 10);
        return this.translationsVue.taskCourse_configureFields_state_2;
      } else if (state === 3) {
        this.state = parseInt(state, 10);
        return this.translationsVue.taskCourse_configureFields_state_3;
      } else if (state === 4) {
        this.state = parseInt(state, 10);
        return this.translationsVue.taskCourse_configureFields_state_4;
      }

      return this.translationsVue.taskCourse_configureFields_state_0;
    },
    async changeStateTask(idHistory, state) {
      const request = {
        idHistory: idHistory,
        state: state,
      };

      await this.$store.dispatch("materialCourse/commentTask", request);
      this.comment = "";
      this.fetchTaskUser();
    },

    async ChangeSelection(event, idHistory) {
      if (event.target.value > 0) {
        const request = {
          idHistory: idHistory,
          state: event.target.value,
        };

        await this.$store.dispatch("materialCourse/commentTask", request);
        this.comment = "";
        this.fetchTaskUser();
      }
    },
  },
};
</script>

 <style scoped lang="scss"> 
.task-by-user {
  margin-bottom: 2rem;
  .text-card {
    margin-top: 1rem;
    font-weight: 300;
  }

  .info-user {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 1rem;
    gap: 0.5rem;
    img {
      width: 7rem;
      height: 7rem;
      border-radius: 50%;
      border: solid 1px #e7e7e7;
    }

    .user {
      .name {
        font-size: 24px;
      }

      .email {
        font-size: 16px;
      }
    }
  }
}
</style>