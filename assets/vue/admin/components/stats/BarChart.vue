<template>
    <highcharts class="hc" :options="chartOptions" ref="chart"></highcharts>
</template>

<script>

export default {
    name: "Bar<PERSON><PERSON>",
    components: {},
    props: {
        title: {
            type: String,
            default: "",
        },
        seriesData: {
            type: Array,
            default: () => [],
        },
        tooltip: {
            type: String,
            default: "",
        },
        legend: {
            type: String,
            default: "{name}",
        },
        type: {
            type: String,
            default: "column",
        },
        colors: {
            type: Array,
            default: () => ['var(--color-primary)'],
        },
        innerSize: false,
    },
    data() {
        return {
            chartOptions: {
                chart: {
                    plotBackgroundColor: null,
                    plotBorderWidth: null,
                    plotShadow: false,
                    type: this.type, // bar/column cuando sean mas
                    height: 300
                },
                credits: {
                    enabled: false,
                },
                title: {
                    text: this.title,
                },
                tooltip: {
                    pointFormat: this.tooltip,
                },
                colors: this.colors,
                yAxis: { title: false },
                xAxis: {
                    categories: [],
                    crosshair: true
                },
                series: [{
                    showInLegend: false,
                    name: this.title,
                    colorByPoint: true,
                    data: this.seriesData
                }],
                lang: {
                    noData: this.$t('NO_INFORMATION'),
                },
                noData: {
                    style: {
                        fontWeight: 'bold',
                        fontSize: '15px',
                        color: '#303030'
                    }
                }
            }
        };
    },

    watch: {
        seriesData: {
            immediate: true,
            handler (newVal, oldVal) { // watch it
              this.chartOptions.series[0].data = newVal;

              this.chartOptions.xAxis.categories = [];
                for (let i = 0; i < newVal.length; i++) {
                    this.chartOptions.xAxis.categories.push(newVal[i].name);
                }
            }
        }
    }
};
</script>

 <style scoped lang="scss"> 


</style>
