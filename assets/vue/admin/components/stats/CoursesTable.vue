<template>
  <div>
    <h3 class="float-left">{{ filterType === 'itinerary' ? itineraryTitle : title}}</h3>
    <div class="text-right mb-1">
      <div class="mr-4 d-inline-block">
        <button v-on:click="filterType = 'itinerary'" class="btn btn-sm"
                :class="filterType === 'itinerary' ? 'btn-info' :'btn-secondary'">
          Itinerario
        </button>
        <button v-on:click="filterType = 'announcement'" class="btn btn-sm"
                :class="filterType === 'announcement' ? 'btn-info' :'btn-secondary'">
          Convocatoria
        </button>
        <button v-on:click="filterType = 'professional'"
                class="btn btn-sm"
                :class="filterType === 'professional' ? 'btn-info' :'btn-secondary'"
        >
          Categoría Profesional
        </button>
        <button v-on:click="filterType = 'all'"
                class="btn btn-sm"
                :class="filterType === 'all' ? 'btn-info' :'btn-secondary'"
        >
          Todos
        </button>
      </div>

      <div class="d-inline-block">
        <button v-on:click="filterStatus = 'finished'" class="btn btn-sm"
                :class="filterStatus === 'finished' ? 'btn-primary' :'btn-secondary'">
          Finalizados
        </button>
        <button v-on:click="filterStatus = 'unfinished'"
                class="btn btn-sm"
                :class="filterStatus === 'unfinished' ? 'btn-primary' :'btn-secondary'"
        >
          Sin finalizar
        </button>
        <button v-on:click="filterStatus = 'all'"
                class="btn btn-sm"
                :class="filterStatus === 'all' ? 'btn-primary' :'btn-secondary'"
        >
          Todos
        </button>
      </div>
    </div>
    <table v-if="filterType !== 'itinerary'" class="table table-responsive-sm datagrid with-rounded-top ">
      <thead>
      <tr>
        <th>
          <a href="javascript:void(0)" @click="sort('name')">
            Curso
            <i class="fa fa-fw fa-sort"></i>
          </a>
        </th>
        <th>
          <a href="javascript:void(0)" @click="sort('type')">
            Tipo
            <i class="fa fa-fw fa-sort"></i>
          </a>
        </th>
        <th>
          <a href="javascript:void(0)" @click="sort('started')">
            Comenzado
            <i class="fa fa-fw fa-sort"></i>
          </a>
        </th>
        <th>
          <a href="javascript:void(0)" @click="sort('finished')">
            Finalizado
            <i class="fa fa-fw fa-sort"></i>
          </a>
        </th>
        <th>
          <a href="javascript:void(0)" @click="sort('timeSpent')">
            Tiempo invertido
            <i class="fa fa-fw fa-sort"></i>
          </a>
        </th>
        <th></th>
      </tr>
      </thead>
      <tbody>
<!--                  <tr v-for="course in sortedCourses" :key="course.id">-->
      <tr v-for="course in pageOfCourses" :key="course.id">
        <td>{{ course.name }}</td>
        <td>{{
          course.type === 'professional' ? 'Cat. Profesional' : (course.type === 'announcement' ? 'Convocatoria' : '')
          }}
        </td>
        <td>{{ course.started | formatDate }}</td>
        <td>{{ course.finished | formatDate }}</td>
        <td>{{ course.timeSpent | nicetime }}</td>
        <td><a :href="course.url">Detalles</a></td>
      </tr>
      </tbody>
      <tfoot>
      <tr>
        <td colspan="4">
          <div class="d-flex align-items-center justify-content-start" v-if="sortedCourses.length > 10">
            <jw-pagination :items="sortedCourses" v-on:changePage="onCourseChangePage"
                           :labels="paginationCustomLabels"></jw-pagination>
          </div>
        </td>
        <td colspan="2" class="text-right font-weight-bold">
          <span class="fas fa-clock"></span>
          Tiempo total:
          {{totalTime | nicetime}}
        </td>
      </tr>
      </tfoot>
    </table>

    <table v-if="filterType === 'itinerary'" class="table table-responsive-sm datagrid with-rounded-top ">
      <thead>
      <tr>
        <th scope="col"><a href="javascript:void(0)" @click="sortItinerary('name')">{{ itineraryTableTranslations.name }} <i class="fa fa-fw fa-sort"></i></a></th>
        <th scope="col"><a href="javascript:void(0)" @click="sortItinerary('total')">{{ itineraryTableTranslations.total }} <i class="fa fa-fw fa-sort"></i></a></th>
        <th scope="col"><a href="javascript:void(0)" @click="sortItinerary('started')">{{ itineraryTableTranslations.started }} <i class="fa fa-fw fa-sort"></i></a></th>
        <th scope="col"><a href="javascript:void(0)" @click="sortItinerary('completed')">{{ itineraryTableTranslations.completed }} <i class="fa fa-fw fa-sort"></i></a></th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="itinerary in pageOfItineraries">
        <td>{{ itinerary.name }}</td>
        <td>{{ itinerary.total }}</td>
        <td>{{ itinerary.started }}</td>
        <td>{{ itinerary.completed }}</td>
        <td><a :href="itinerary.url" target="_blank">Detalles</a></td>
      </tr>
      <tr>
        <td colspan="5">
          <div class="d-flex align-items-center justify-content-start" v-if="sortedItineraries.length > 10">
            <jw-pagination :items="sortedItineraries" v-on:changePage="onItineraryChangePage"
                           :labels="paginationCustomLabels"></jw-pagination>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import JwPagination from 'jw-vue-pagination';

const PAGE_SIZE = 10;

export default {
  name: "CoursesTable",
  components: {JwPagination},
  props: {
    title: String,
    courses: Array,

    itineraryTitle: String,
    itineraries: Array,
    itineraryTableTranslations: Object,

    status: {
      type: String,
      default: 'all'
    },
    type: {
      type: String,
      default: 'all'
    },
  },

  data() {
    return {
      currentSort: 'name',
      currentSortDir: 'asc',
      filterStatus: 'all',
      filterType: 'all',
      pageOfCourses: [],
      pageOfItineraries: [],
      itinerarySort: {
        current: 'name',
        direction: 'asc'
      }
    }
  },

  created() {
    this.filterStatus = this.status;
    this.filterType = this.type;
  },

  computed: {
    filteredCourses() {
      let courses = this.courses;
      if (this.filterStatus === 'finished') {
        courses = courses.filter(course => course.finished)
      } else if (this.filterStatus === 'unfinished') {
        courses = courses.filter(course => !course.finished)
      }

      if (this.filterType === 'professional') {
        courses = courses.filter(course => course.type === 'professional')
      } else if (this.filterType === 'announcement') {
        courses = courses.filter(course => course.type === 'announcement')
      }

      return courses;
    },

    filteredItineraries: function () {
      let itineraries = this.itineraries;
      if (this.filterStatus === 'finished') {
        itineraries = itineraries.filter((itinerary => parseInt(itinerary.completed) === parseInt(itinerary.total)));
      } else if (this.filterStatus === 'unfinished') {
        itineraries = itineraries.filter((itinerary => parseInt(itinerary.completed) !== parseInt(itinerary.total)));
      }

      return itineraries;
    },

    sortedCourses: function () {
      let courses = this.filteredCourses;
      if (this.currentSort) {
        courses = this.filteredCourses.sort((a, b) => {
          let modifier = 1;
          if (this.currentSortDir === 'desc') modifier = -1;
          if (a[this.currentSort] < b[this.currentSort]) return -1 * modifier;
          if (a[this.currentSort] > b[this.currentSort]) return 1 * modifier;
          return 0;
        });
      }

      if (courses.length === 0) {
        this.pageOfCourses = [];
      } else if (courses.length <= PAGE_SIZE) {
        this.pageOfCourses = courses
      }

      return courses;
    },

    sortedItineraries: function () {
      let itineraries = this.filteredItineraries;
      if (this.itinerarySort.current) {
        itineraries = itineraries.sort((a, b) => {
          let sort = this.itinerarySort.current;
          let modifier =  1;
          if (this.itinerarySort.direction === 'desc') modifier = -1;
          if (a[sort] < b[sort]) return modifier * -1;
          if (a[sort] > b[sort]) return modifier;
          return 0;
        })
      }
      if (itineraries.length === 0) {
        this.pageOfItineraries = [];
      }
      else if (itineraries.length <= PAGE_SIZE && itineraries.length > 0) {
        this.pageOfItineraries = itineraries;
      }

      return itineraries;
    },

    totalTime: function () {
      let time = 0;
      for (let i = 0; i < this.courses.length; i++) {
        time += this.courses[i].timeSpent;
      }

      return time;
    },

    paginationCustomLabels() {
      return {
        first: '<<',
        last: '>>',
        previous: '<',
        next: '>'
      }
    },
  },

  methods: {
    sort: function (s) {
      if (s === this.currentSort) {
        this.currentSortDir = this.currentSortDir === 'asc' ? 'desc' : 'asc';
      }
      this.currentSort = s;
    },

    sortItinerary(s) {
      if (s === this.itinerarySort.current) {
        this.itinerarySort.direction = this.itinerarySort.direction === 'asc' ? 'desc' : 'asc';
      }
      this.itinerarySort.current = s;
    },

    onCourseChangePage(pageOfCourses) {
      this.pageOfCourses = pageOfCourses;
    },

    onItineraryChangePage(pageOfItineraries) {
      this.pageOfItineraries = pageOfItineraries;
    }
  },
};
</script>

 <style scoped lang="scss"> 
</style>
