<template>
  <CreateHighLower :lower="lower" />
</template>

<script>
import CreateHighLower from "./CreateHighLower";

export default {
  components: {
    CreateHighLower,
  },

  props: {
    lower: {
      type: Object,
      required: true,
    },
  },

  computed: {
    copyLower() {
      return JSON.parse(JSON.stringify(this.lower));
      //return structuredClone(this.lower);
    },
  },
};
</script>

 <style scoped lang="scss"> 
</style>
