<template>
  <div class="AddMaterialCourse">
    <form-file-uploader
        id="form-upload-observation-files"
        :file-types="fileTypes"
        :uploading="uploading"
        :show-cancel="true"
        :show-submit="true"
        @on-type-selection="selectedType = $event"
        @cancel="$emit('cancel')"
        @upload="upload"
    >
    </form-file-uploader>
  </div>
</template>

<script>
import {get} from "vuex-pathify";

import FileSelector from "../../../common/components/FileSelector.vue";
import FormFileUploader from "../../../common/components/FormFileUploader.vue";
import Spinner from "../../../admin/components/base/Spinner.vue";

/**
 * @event success
 * @event cancel
 */
export default {
  name: "AddMaterialCourse",
  components: {FormFileUploader, Spinner, FileSelector},
  props: {
    id: {
        type: Number,
        required: true
    },
    announcementId: {
        type: Number,
        default: 0
    }
  },
  data() {
    return {
      uploadingFiles: false,
      uploading: false,
      selectedType: null
    };
  },
  computed: {
    fileTypes: get('materialCourseModule/allowedFileTypes'),
  },
  methods: {
    upload(formData) {

      let files = []
      const filesLength = formData.get('filesLength');
      if (filesLength > 0) {
        for (let i = 0; i< filesLength; i++) {
          files.push(formData.get(`file_${i}`))
        }
      } else {
        files.push(formData.get(`file`))
      }

      this.$alertify.confirmWithTitle(
          this.$t('FILE_UPLOAD.CONFIRM_UPLOAD.TITLE'),
          this.$t('FILE_UPLOAD.CONFIRM_UPLOAD.DESCRIPTION'),
          async () => {
            try {
                this.uploadingFiles = true;
                const data = {
                    course: this.id,
                    files,
                    typeMaterial: this.selectedType.type,
                    announcement: '',
                };
                await this.$store.dispatch("materialCourse/newMaterialCourse", data);
                this.$toast.success(this.$t('FILE_UPLOAD.UPLOAD.SUCCESS') + '');
                this.$emit('success');
            } catch (error) {
                this.$toast.error(this.$t('FILE_UPLOAD.UPLOAD.FAILED') + '');
            } finally {
                this.uploadingFiles = false;
            }
          },
          () => {}
      );
    }
  }
}
</script>

 <style scoped lang="scss"> 
.AddMaterialCourse {
  &--type {
    @include boxed-selector;

    button.selector {
      width: 80px !important;
      height: 80px !important;
    }
  }

  &--file {
    width: 100%;
    form {
      width: 100%;
      @media #{min-small-screen()} {
        display: grid;
        grid-template-columns: 400px auto;
      }
    }
  }
}
</style>
