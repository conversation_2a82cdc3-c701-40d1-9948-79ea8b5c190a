<template>
  <div class="CategoryCard">
    <div class="content-card">
      <button class="image" @click="$refs.inputFile.click()">
        <img
          :src="previewImage || '/assets/chapters/noimg.svg'"
          :class="{ 'no-image': !previewImage }"
          onerror="this.onerror=null; this.src='/assets/chapters/noimg.svg'"
        />

        <i class="fas fa-plus"></i>

        <input
          type="file"
          @change="loadImage($event)"
          accept="image/*"
          ref="inputFile"
          @blur="saveTextInput"
        />
      </button>

      <input
        type="text"
        class="form-control"
        name="name"
        required
        v-model="name"
        :maxlength="maxInputSize"
        :placeholder="translationsVue.common_write"
        @blur="saveTextInput"
      />
    </div>

    <div v-if="isErasable || hasBeenModified" class="actions">
      <button
        v-if="isErasable"
        class="btn btn-danger btn-sm"
        @click="$emit('remove')"
      >
        <i class="fas fa-times"></i>
      </button>

      <button
        v-if="hasBeenModified"
        class="btn btn-primary btn-sm"
        @click="saveChanges"
      >
        <i class="fas fa-check"></i>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      preview: null,
      hasBeenModified: false,
      translationsVue,
      maxInputSize: 50
    };
  },

  props: {
    value: {
      type: Object,
      default: undefined,
    },

    isErasable: {
      type: Boolean,
      default: true,
    },
  },

  created() {},

  computed: {
    previewImage() {
      if (this.preview) return this.preview;

      if (this.image) return `/uploads/games/categorize_options/${this.image}`;

      return undefined;
    },

    category() {
      return this.value;
    },

    name: {
      get() {
        return this.category?.name;
      },

      set(value) {
        this.hasBeenModified = true;

        const newCategory = { ...this.category, name: value };
        this.setCategory(newCategory);
      },
    },

    image: {
      get() {
        return this.category?.image;
      },

      set(value) {
        this.hasBeenModified = true;

        const newCategory = { ...this.category, image: value };
        this.setCategory(newCategory);
      },
    },
  },

  methods: {
    saveChanges() {
      this.hasBeenModified = false;
      this.$emit("add");
    },

    setCategory(newCategory) {
      this.$emit("input", newCategory);
    },

    loadImage(event) {
      const { files } = event.target;

      if (files && files[0]) {
        const reader = new FileReader();

        reader.onload = (e) => {
          this.preview = e.target.result;
        };

        const image = files[0];
        reader.readAsDataURL(image);

        this.image = image;
        this.saveChanges();
      }
    },

    saveTextInput() {
      this.name = this.name.slice(0, this.maxInputSize)
      this.saveChanges();
    },
  },
};
</script>

 <style scoped lang="scss"> 
.CategoryCard {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: flex-start;

  .content-card {
    button.image {
      all: unset;
      position: relative;
      width: 100%;
      height: 150px;
      background-color: #eae9e9;
      margin-bottom: $spacing-2xs;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;

        &.no-image {
          object-fit: contain;
        }
      }

      i {
        position: absolute;
        inset: 0;
        display: grid;
        place-content: center;
        font-size: var(--font-size-2xl);
        opacity: 0;
        transition: all 0.3s ease;
      }

      &:hover {
        cursor: pointer;

        i {
          opacity: 1;
          background-color: adjust-color($color: $color-primary, $alpha: -0.3);
          color: #fff;
        }
      }

      input[type="file"] {
        display: none;
      }
    }
  }

  .actions {
    width: 35px;
    display: grid;
    gap: $spacing-2xs;
    margin-left: $spacing-2xs;
  }
}
</style>
