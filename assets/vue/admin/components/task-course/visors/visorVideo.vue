<template>
  <div>
    <vimeo-player
        ref="player"
        :video-url="urlMaterial"
        @ready="onReady"
        @error="onError"
        :options="{
          title: false,
          responsive: true,
          loop: false
        }"
    >
    </vimeo-player>

    <div class="messageVideo" v-if="inProccessLoad">
      <p v-if="translationsVue">!{{translationsVue.component_video_text_good}}! </p>
      <p v-if="translationsVue">
        <Spinner/> {{translationsVue.component_video_optimizing_video}}
      </p>
    </div>

  </div>
</template>

<script>
import Spinner from "../../base/Spinner";

export default {
  components:{
    Spinner
  },

  props: {
    name: {
      type: String,
      default: "",
    },

    identifierVideo: {
      type: String,
      default: "",
    },

    urlMaterial: {
      type: String,
      default: "",
    },
    codeVideo: {
      type: String,
      default: "",
    },

    autoplay: {
      type: Boolean,
      default: false,
    },
  },

  data(){
    return {
      translationsVue,
      inProccessLoad: 0
    }
  },
  computed:{
    player() {
      return this.$refs.player.player;
    },
  },

  watch: {
    autoplay() {
      if(!this.autoplay){
        this.pauseVideo();
      }
    },
  },

  methods: {
    onReady() {
      this.inProccessLoad = 0;
    },

    onError() {
      this.inProccessLoad = 1;
      setInterval(() => {
        this.showStateVideo();
      }, 60000);
    },

    showStateVideo() {
      if (this.inProccessLoad == 1) {       ;
        location.reload();
      }
    },

    pauseVideo(){
      this.player.pause();
    }
  },
};
</script>

 <style scoped lang="scss"> 
.messageVideo {
  margin: 2rem;
  background: #eff0f0;
  width: 70%;
  padding: 2rem;
  p:nth-child(1) {
    font-size: 4rem;
    margin-bottom: 0px;
    text-align: center;
  }

  p:nth-child(2) {
    font-size: 1.5rem;
    text-align: center;
  }
}
</style>