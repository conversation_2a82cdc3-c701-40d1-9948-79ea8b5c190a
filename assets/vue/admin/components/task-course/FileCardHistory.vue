<template>
  <div class="card">
   

    <div class="info-file">
      <div class="typeFile">
        <!--   <p ><i :class='`fas ${iconFile(file.fileOriginalName)}`'><i/></p> -->
        <p>
          <i
            :style="{ background: colorIcon(file.fileOriginalName) }"
            :class="`fas ${iconFile(file.fileOriginalName)}`"
          ></i>
        </p>
        <a v-if="allowAccess"  @click="openModalDialog()" class="allowAccess">{{ file.fileOriginalName }}</a>
        <a v-else>{{ file.fileOriginalName }}</a>
      </div>

      <div class="actions">
        <div class="donwload" @click="downloadFile(file)">
          <i class="fas fa-cloud-download-alt"></i>
        </div>

      <!--   <button
          class="btn btn-secondary"
          v-if="allowAccess"
          @click="openModalDialog()"
        >
          <i class="fas fa-eye"></i>
        </button> -->
      </div>
    </div>

     <div class="title">
      <span><i class="fas fa-paperclip"></i> Entregado</span>
      <span>{{ file.createdAt }}</span>
    </div>

    <dialog>
      <div class="header-dialog">
        <p>{{file.fileOriginalName}}</p>
        <p @click="closeDialog()"><i class="fas fa-window-close"></i></p>
      </div>

      <div class="body-dialog">
        <component
           :is="visorComponent(file.filename)"
            v-if="file"
            :key="file.id"           
            :name="file.filename"
            route-base="/uploads/task_user/"           
          />

      </div>
 
    </dialog>
  </div>
</template>

<script>
import visorImage from "./visors/visorImagen";
import visorPdf from "./visors/visorPdf";
import visorVideo from "./visors/visorVideo";
import visorOffice from "./visors/visorOffice";
import visorTxt from "./visors/visorTxt";

export default {
  components:{
    visorImage,
    visorPdf,
    visorVideo,
    visorOffice,
    visorTxt
  },

  props: {
    file: {
      type: Object,
      default: () => {},
    },
  },

  computed: {
    allowAccess() {
      const extension = this.file.filename.substring(
        this.file.filename.indexOf(".") + 1
      );

      if (["zip", "rar"].includes(extension)) {
        return false;
      }
      return true;
    },
  },

  methods: {
    colorIcon(nameFile) {
      const extension = nameFile?.substring(nameFile.indexOf(".") + 1);
      if (["doc", "docx", "odt", "dot", "docm", "dotx"].includes(extension)) {
        return "#226BD6";
      }
      if (
        ["xlsx", "xlsm", "xlsb", "xltx", "xls", "xml", "csv"].includes(
          extension
        )
      ) {
        return "#17703B";
      }

      if (["pptx", "pptm", "ppt", "ppsx", "pps", "potx"].includes(extension)) {
        return "#C64126";
      }

      if (["accdb", "mdb"].includes(extension)) {
        return "#851222";
      }

      if (["mpp", "mpt"].includes(extension)) {
        return "#249E4A";
      }

      if (
        [
          "png",
          "jpg",
          "gif",
          "JPEG",
          "jpeg",
          "JPG",
          "PNG",
          "BMP",
          "TIFF",
          "webp",
          "WebP",
        ].includes(extension)
      ) {
        return "#E69E22";
      }

      if (["pdf"].includes(extension)) {
        return "#F53C3F";
      }

      if (["zip", "rar"].includes(extension)) {
        return "#D64F6F";
      }

      if (["txt"].includes(extension)) {
        return "#61aa72";
      }

      return "#59ABDB";
    },

    iconFile(nameFile) {
      const extension = nameFile?.substring(nameFile?.indexOf(".") + 1);
      if (["doc", "docx", "odt", "dot", "docm", "dotx"].includes(extension)) {
        return "fa-file-word";
      }
      if (
        ["xlsx", "xlsm", "xlsb", "xltx", "xls", "xml", "csv"].includes(
          extension
        )
      ) {
        return "fa-file-excel";
      }

      if (["pptx", "pptm", "ppt", "ppsx", "pps", "potx"].includes(extension)) {
        return "fa-file-powerpoint";
      }

      if (["accdb", "mdb"].includes(extension)) {
        return "fa-database";
      }

      if (["mpp", "mpt"].includes(extension)) {
        return "fa-project-diagram";
      }

      if (
        [
          "png",
          "jpg",
          "gif",
          "JPEG",
          "jpeg",
          "JPG",
          "PNG",
          "BMP",
          "TIFF",
          "webp",
          "WebP",
          "PNG",
        ].includes(extension)
      ) {
        return "fa-image";
      }

      if (["pdf"].includes(extension)) {
        return "fa-file-pdf";
      }

      if (["zip", "rar"].includes(extension)) {
        return "fa-file-archive";
      }

      if (["txt"].includes(extension)) {
        return "fa-sticky-note";
      }

      return "fa-file";
    },

    async downloadFile(file){
      await this.$store.dispatch('materialCourse/downloadFileTaskUser', file);
    },

     visorComponent(nameFile) {
      const extension = nameFile.substring(nameFile.indexOf('.') + 1);
      if (['doc', 'docx', 'odt', 'dot', 'docm', 'dotx'].includes(extension)) {
        return 'visorOffice';
      }
      if (['xlsx', 'xlsm', 'xlsb', 'xltx', 'xls', 'xml', 'csv'].includes(extension)) {
        return 'visorOffice';
      }

      if (['pptx', 'pptm', 'ppt', 'ppsx', 'pps', 'potx'].includes(extension)) {
        return 'visorOffice';
      }

      if (['accdb', 'mdb'].includes(extension)) {
        return 'visorOffice';
      }

      if (['mpp', 'mpt'].includes(extension)) {
        return 'visorOffice';
      }

      if (['png', 'jpg', 'gif', 'JPEG', 'jpeg', 'JPG', 'PNG', 'BMP', 'TIFF', 'webp', 'WebP'].includes(extension)) {
        return 'visorImage';
      }

      if (['pdf'].includes(extension)) {
        return 'visorPdf';
      }

      if (['txt'].includes(extension)) {
        return 'visorTxt';
      }

      return 'visorTxt';
    },

    openModalDialog() {
      const dialog = this.$el.querySelector("dialog");
      dialog.showModal();
    },

    closeDialog() {
      const dialog = this.$el.querySelector("dialog");
      dialog.close();
    },
  },
};
</script>

 <style scoped lang="scss"> 
@import "/assets/css/config/global.scss";

.card {
  display: flex;
  flex-direction: column;
  min-width: 22rem !important;
  flex-wrap: wrap;
  gap: 1rem;
  background: #fff;
  box-shadow: $shadow-elevation-1;
  padding: $spacing-s;
  border-radius: 10px;
  justify-content: space-around;
  align-items: center;
  .title {
    display: flex;
    /* justify-content: space-between; */
    flex-wrap: wrap;
    width: 100%;
    gap: 1rem;
    font-size: 14px;
    margin-top: -2.5rem;
    opacity: 0.6;
    padding-left: 2rem; 
  }

  .info-file {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: auto;
    width: 100%;
    gap: 1rem;  

    .typeFile {
      display: flex;
      gap: 0.4rem;

      p:nth-child(2) {
        margin: auto;
      }

      .allowAccess{
        color: var(--color-primary);
        cursor: pointer;
        text-decoration: none;
      }
    }

    .actions {
      display: flex;
      gap: 0.5rem;
      .donwload {
        cursor: pointer;
        height: 31px;
        width: 31px;
        background: var(--color-primary);
        border-radius: 50%;
        color: #fff;
        display: flex;
        flex-direction: row;
        align-items: center;
        i {
          text-align: center;
          margin-left: 6px;
        }

        &:hover {
          background: #fff;
          color: var(--color-primary);
        }
      }

      .btn {
        height: 40px;
      }
    }
  }

  p:nth-child(1) {
    i {
      width: 30px;
      height: 30px;
      padding: 0.5rem;
      color: #fff;
      border-radius: 5px;
    }
  }

  .icon-delete {
    color: $color-error;
    font-size: 1rem;
    cursor: pointer;
    &:hover {
      color: red;
    }
  }
  dialog {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    margin: auto;
    border: none;
    .header-dialog{
        display: flex;
        justify-content: space-between;
        background: $color-app;
        color: #fff;
        p{
          margin: 0.5rem;
        }
        p:nth-child(2){
          cursor: pointer;
          font-size: 20px;
          &:hover{
            color: red;
          }
        }
    }
  }
}
</style>
