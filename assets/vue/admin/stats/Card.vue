<template>
  <div class="Card" :class="{loading: isLoading}" >
    <div class="icon-container" :style="{backgroundColor: isLoading ? '#CFD8DC' : options.color}"><i :class="isLoading ? 'fa fa-circle-o-notch fa-spin fa-fw': options.icon"></i></div>
    <div class="text-container">
      <p class="title" :style="{color: options.color}">{{ isLoading ? '' : options.title }}</p>
      <p class="subtitle">{{ isLoading ? '' : options.subtitle }}</p>
      <p class="description">
        {{ isLoading ? '' : options.description }}
        <span class="subtitle"></span>
      </p>
    </div>
  </div>
</template>

<script>

export default {
  name: "Card",
  components: {},
  props: {
    isLoading: {
      type: Boolean,
      default: true
    },
    tag: {
      type: String,
      default: "Card",
    },
    options: {
      type: Object,
      default: () => ({
        title: '',
        subtitle: '',
        description: '',
        color: '#8BC34A',
        icon: 'fa fa-users'
      })
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Card {
  width: 100%;
  background-color: white;
  border: 1px solid #E7EBF0;
  box-shadow: 0 0 2px 2px #ECEFF1;
  overflow: hidden;
  border-radius: 7px;
  display: grid;
  grid-template-columns: 4rem auto;
  gap: 0;

  p {
    margin: 0;
  }

  .icon-container {
    font-size: 2rem;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .text-container {
    display: grid;
    grid-template-columns: auto;

    .title {
      font-weight: bold;
      font-size: 1.2rem;
      padding: 1rem 1rem 0;
    }

    .subtitle {
      font-size: 0.8rem;
      color: #78909C;
      justify-content: flex-start;
      padding: 0 1rem 1rem;
      grid-column: auto;
    }

    .description {
      font-weight: bold;
      font-size: 0.8rem;
      background-color: #ECEFF1;
      color: #37474F;
      padding: 1.5rem 1rem;

      .subtitle {
        visibility: hidden;
        height: 0;
        margin: 0;
        padding: 0;
      }
    }
  }

  &.loading {
    .text-container {
      .title, .subtitle {
        border-radius: 7px;
        animation: opacityAnimation 1.1s linear infinite alternate;
      }

      .title {
        width: clamp(50px, 90%, 100px);
        background-color: #CFD8DC;
        height: 1.2rem;
        margin: 1rem 0 0 1rem;
      }

      .subtitle {
        width: clamp(50px, 70%, 250px);
        background-color: #78909C;
        height: 0.8rem;
        margin: 0.5rem 0 0 1rem;
      }

      .description {
        margin-top: 1rem;
        .subtitle {
          visibility: visible;
          height: 0.8rem;
          background-color: #37474F;
          width: clamp(50px, 90%, 330px);
          margin: 0;
        }
      }
    }
  }
}
</style>
