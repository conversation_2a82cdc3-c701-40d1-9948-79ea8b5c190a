<template>
  <div class="SortModal" v-if="innerValue">
    <div class="dialog">
      <div class="header">{{config.title || 'Ordenar datos'}}</div>
      <div class="body">
        <div>
          <label for="sortBy">Ordenar Por:</label>
          <select id="sortBy" v-model="sortBy">
            <option
                v-for="(option, index) in options"
                :key="'dialogOption' + index"
                :value="option.key">{{option.name}} </option>
          </select>
        </div>
        <div>
          <label for="direction">Orden:</label>
          <select id="direction" v-model="direction">
            <option value="Asc">Ascendente</option>
            <option value="Desc">Descendente</option>
          </select>
        </div>
      </div>
      <div class="footer">
        <button @click="closeModal" class="cancel"> Cancelar </button>
        <button v-show="sortBy" @click="applyFilters" class="apply"> Aplicar </button>
      </div>
    </div>
  </div>
</template>

<script>

import ModelMixin from "../components/mixins/ModelMixin";

export default {
  name: "SortModal",
  mixins: [ModelMixin],
  components: {},
  data() {
    return {
      sortBy: undefined,
      direction : 'Asc',
    }
  },
  props: {
    tag: {
      type: String,
      default: "Modal",
    },
    config: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    options() { return this.config.options; }
  },
  watch: {
    options() { if (this.innerValue) {
      const [sortBy, isAsc] = this.config.currentSort;
      this.sortBy = sortBy;
      this.direction = isAsc ? 'Asc' : 'Desc';
    } }
  },
  methods: {
    closeModal() {
      this.sortBy = undefined;
      this.direction = 'Asc'
      this.innerValue = false;
    },
    applyFilters() {
      this.$emit('apply', [this.config.key, this.sortBy, this.direction === 'Asc']);
      this.innerValue = false;
    }
  }
};
</script>

 <style scoped lang="scss"> 
.SortModal {
  position: fixed;
  inset: 0;
  background-color: rgba(0,0,0,0.7);
  backdrop-filter: blur(7px);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;

  .dialog {
    background-color: #FFFFFF;
    backdrop-filter: blur(7px);
    border-radius: 7px;
    overflow: hidden;

    button {
      border: none;
      border-radius: 7px;
      padding: 0.5rem 1rem;
      color: white;

      &.apply { background-color: #68BBB0; }
      &.cancel { background-color: #FA7A7E; }
    }

    .header, .body, .footer {
      padding: 1rem;
    }

    .header, .footer {
      text-align: center;
    }

    .header {
      background-color: #68BBB0;
      color: white;
      padding: 1rem 2rem;
      font-weight: bold;
    }

    .body {
      display: grid;
      grid-template-columns: auto;
      gap: 1rem;

      label, select {
        display: block;
        width: 100%;
      }

      label {
        font-weight: bold;
      }

      select {
        padding: 0.5rem 1rem;
        border: 1px solid #E5E5E5;
        border-radius: 3px;
      }
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 1rem;
    }
  }
}
</style>
