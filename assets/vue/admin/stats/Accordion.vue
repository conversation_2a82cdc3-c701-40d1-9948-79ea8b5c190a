<template>
  <div class="Accordion">
    <div class="accordionHeader" @click="innerValue = !innerValue">
      <div>
        <i class="headerIcon" :class="icon"></i>
        <span class="title">{{ $t(title) }}</span>
      </div>
      <div>
        <i
          class="hideOnPrint fa"
          :class="innerValue ? 'fa-chevron-up' : 'fa-chevron-down'"
        ></i>
      </div>
    </div>
    <div class="accordionData" v-show="innerValue">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import ModelMixin from "../components/mixins/ModelMixin";

export default {
  name: "Accordion",
  mixins: [ModelMixin],
  components: {},
  props: {
    tag: {
      type: String,
      default: "Accordion",
    },
    icon: {
      type: String,
      default: "fa fa-users",
    },
    title: {
      type: String,
      default: "Title",
    },
  },
};
</script>

 <style scoped lang="scss"> 
.Accordion {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;

  .accordionHeader {
    user-select: none;
    display: flex;
    justify-content: space-between;
    background-color: white;
    box-shadow: 0 2px 4px 4px #eceff1;
    color: #37474f;
    padding: 1rem;
    cursor: pointer;

    .headerIcon {
      width: 1.5rem;
      text-align: center;
    }

    .title {
      margin-left: 1rem;
      font-weight: bold;
    }

    i {
      cursor: pointer;
    }
  }

  .accordionData {
    display: grid;
    gap: 1rem;
  }
}
</style>
