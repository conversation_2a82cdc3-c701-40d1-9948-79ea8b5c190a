import "../../../css/newStats.scss";
import Vue from "vue";
import ContentTemplate from "./ContentTemplate";
import inputDate from "../components/html/input-date";
import mySelect from "../components/html/select";
import myMultiSelect from "../components/html/multiselect";
import SortModal from "./SortModal";
import ExcelGenerator from "./ExcelGenerator";
import axios from "axios";
import * as Highcharts from "highcharts";
import Stock from "highcharts/modules/stock";
import noData from "highcharts/modules/no-data-to-display";
import MapCharts from "highcharts/modules/map";
import HighchartsVue from "highcharts-vue";

import store from "../store";
import initLocale from "../../common/utils/initLocale";

import { getI18nApi } from "../../common/i18n";
import { apply } from "file-loader";

Stock(Highcharts);
noData(Highcharts);
MapCharts(Highcharts);

getI18nApi().then((data) => {
  startVueApp(data);
});

Vue.use(HighchartsVue);

/**
 * Init vue app instance after a successful i18n initialization
 * @param i18n
 * @param locales
 * @param locale
 * */
function startVueApp({ i18n, locales, locale}) {
  let App = new Vue({
    i18n,
    store,
    delimiters: ["${", "}"],
    components: {
      ContentTemplate,
      inputDate,
      mySelect,
      ExcelGenerator,
      myMultiSelect,
      SortModal,
    },
    data() {
      return {
        loadingMethods: ["Parallel", "FirstAvailable", "Chunks"][2],
        showNotification: true,
        showFilters: false,
        printDoc: false,
        isLoading: true,
        bd_filters: {},
        filters: {},
        countries: [],
        courses: [],
        centers: [],
        professionalCategories: [],
        departaments: [],
        genders: [],
        divisions: [],
        divisionCountries: [],
        filterCategories: [],
        statsFormationSettings: [], 
        statsEvolutionSettings : [],
        statsDemographySettings: [],
        statsActivitySettings : [],
        statsItinerarySettings : [],
        onlyActivesList: [
          { id: 2, name: "Activos" },
          { id: 1, name: "Inactivos" },
        ],
        params: {},
        visiblePanels: [false, false, false, false, false],
        chunksLoaded: [false, false, false, false, false],
        visiblePanelsPrint: [true, true, true, true, true],
        content: "",
        loadQueue: [],
        loadersStarted: 2,
        loadersCompleted: 0,
        heatMapCategories: [
          [
            "0-2h",
            "2-4h",
            "4-6h",
            "6-8h",
            "8-10h",
            "10-12h",
            "12-14h",
            "14-16h",
            "16-18h",
            "18-20h",
            "20-22h",
            "22-24h",
          ],
          [
            this.$t("MONDAY"),
            this.$t("TUESDAY"),
            this.$t("WEDNESDAY"),
            this.$t("THURSDAY"),
            this.$t("FRIDAY"),
            this.$t("SATURDAY"),
            this.$t("SUNDAY"),
          ],
        ],
        modalData: {
          title: "",
          showModal: false,
          options: [],
          tooltip: [
            {
              valueDecimals: 2,
              backgroundColor: "#FCFFC5",
            },
          ],
        },
        chartData: {
          // -----------------------------  Formación  -----------------------------
          formationHours: {
            url: "/admin/stats/total/GeneralHour",
            method: "post",
            formatOptions: (data) => ({
              hours: {
                title: `${this.formatNumber(data.horas)} ${this.$t("HOURS")}`,
                subtitle:
                  data.horastotales != ""
                    ? `${this.$t("OUT_OF_TOTAL")} ${this.formatNumber(
                        data.horastotales
                      )}`
                    : ``,
                description: `${this.$t("OF_TRAINING")}`,
                color: "#F7BE73",
                icon: "fa fa-clock",
              },
              average: {
                title: `${data.promedio} ${this.$t("HOURS")}`,
                description: `${this.$t("AVERAGE_OF_TRAINING")}`,
                color: "#68BBB0",
                icon: "fa fa-clock",
              },
            }),
            initFunction: (data) => ({
              horas: Math.ceil(data[0].horas),
              promedio: this.roundNumber(data[0].promedio),
              horastotales: data[1] != null ? Math.ceil(data[1].rawhoras) : "",
            }),
            options: {
              hours: {},
              average: {},
            },
          },
          peopleWithCourses: {
            url: "admin/stats/person/withCourse",
            formatOptions: (data) => ({
              totalPerson: 3, //data.totalPerson,
              percentWithOneCourse: data.percentWithOneCourse,
              totalInCourse: {
                title: `${this.formatNumber(data.totalInCourse)} ${this.$t( "INDIVIDUALS")}`,
                subtitle: `${this.$t("OUT_OF_TOTAL")} ${this.formatNumber(
                  data.totalPerson
                )}`,
                description: `${this.$t("IN_TRAINING")}`,
                color: "#A9C47E",
                icon: "fa fa-users",
              },
              totalOneCourse: {
                title: `${this.formatNumber(data.totalOneCourse)} ${this.$t("INDIVIDUALS")}`,
                subtitle: `${data.percentWithOneCourse} ${i18n.t("OF_THE_TOTAL")}`,
                description: this.$t("STATISTICS.USER.COURSE.ONE_COURSE"),
                color: "#FA7A7E",
                icon: "fa fa-users",
              },
            }),
            initFunction: (data) => ({
              ...data,
              percentWithOneCourse: this.roundNumber(data.percentWithOneCourse),
            }),
            options: {
              totalInCourse: {},
              totalOneCourse: {},
            },
          },
          courseStartedAndFinished: {
            url: "/admin/stats/courses/started-finished",
            method: "post",
            formatOptions: (data) => ({
              started: this.formatNumber(data.started),
              inProcess: this.formatNumber(data.in_process),
              finished: this.formatNumber(data.finished),
            }),
            options: {
              started: "-",
              inProcess: "-",
              finished: "-",
            },
          },
          requiredCourses: {
            url: "/admin/stats/mandatoryCourses",
            formatOptions: (data) => ({
              description: this.$t("COURSES.ASSIGNED"),
              title: `${data} ${this.$t("COURSES.COMPULSORY")}`,
              color: "#FA7A7E",
              icon: "fa fa-book",
            }),
            options: {},
          },
          openedCourses: {
            url: "/admin/stats/voluntaryCourses",
            formatOptions: (data) => ({
              title: `${data}  ${this.$t("COURSES.VOLUNTARY")}`,
              description: this.$t("COURSES.OPEN"),
              color: "#68BBB0",
              icon: "fa fa-book",
            }),
            options: {},
          },
          educativeStatus: {
            url: "/admin/stats/userPoints",
            formatOptions: (data) => {
              let categories = [],
                serieData = [];
              data.forEach((item) => {
                categories = [...categories, item.title];
                serieData = [...serieData, item.points * -1];
              });
              return {
                type: "bar",
                hideLegend: true,
                categories,
                height: 200,
                opposite: true,
                hideLabels: true,
                formatter: function () {
                  return (
                    "<b>" +
                    this.point.category +
                    "</b><br/>" +
                    Math.abs(this.point.y)
                  );
                },
                series: [{ name: "", color: "#F7BE73", data: serieData }],
              };
            },
            initFunction: (data) =>
              data.map((item) => ({
                ...item,
                title: `${item.name} (${item.legend})`,
              })),
            options: {},
          },
          gamifiedPills: {
            url: "/admin/stats/totalGamesByResult",
            formatOptions: (data) => ({
              general: {
                title: `${this.formatNumber(data.totalGame)} ${this.$t("GAMIFIED_PILLS")}`,
                subtitle: `${this.$t("OUT_OF_TOTAL")} ${this.formatNumber(
                  data.totalGeneral
                )}`,
                description: this.$t("HAVE_BEEN_COMPLETED"),
                color: "#A9C47E",
                icon: "fa fa-play-circle",
              },
              failures: {
                title: `${this.formatNumber(data.totalMistake)} ${this.$t(
                  "FAILURES"
                )}`,
                subtitle: `${data.percentMistake} ${this.$t("OF_THE_TOTAL")}`,
                description: this.$t("GAMIFIED_TEST.IN"),
                color: "#FA7A7E",
                icon: "fa fa-times",
              },
              successes: {
                title: `${this.formatNumber(data.totalHits)} ${this.$t(
                  "SUCCESSES"
                )}`,
                subtitle: `${data.percentHits} ${this.$t("OF_THE_TOTAL")}`,
                description: this.$t("GAMIFIED_TEST.IN"),
                color: "#68BBB0",
                icon: "fa fa-check",
              },
            }),
            options: {
              general: {},
              successes: {},
              failures: {},
            },
          },
          gamifiedTest: {
            url: "/admin/stats/totalGameUsed",
            formatOptions: (data) => {
              const arrayInit = new Array(data.length).fill(0);
              const series = {
                chart1: [],
                categories: [],
                hits: [...arrayInit],
                mistake: [...arrayInit],
              };
              const svgColors = {
                DoubleorNothing: "#FB9A99",
                HiddenWords: "#A9C47E",
                Puzzle: "#FDC27F",
                Quiz: "#68BBB0",
                Roulette: "#BFE6F6",
              };
              const totals = (data || []).reduce(
                (acc, cur) => acc + cur.count,
                0
              );
              (data || []).forEach((item, index) => {
                item.hits = parseInt(`${item.hits}`, 10);
                item.mistake = parseInt(`${item.mistake}`, 10);
                const total = item.hits + item.mistake;
                const commonProps = {
                  url: item.url,
                  name: item.name,
                  color: svgColors[item.name.replaceAll(" ", "")],
                };
                series.chart1 = [
                  ...series.chart1,
                  {
                    ...commonProps,
                    y: item.count,
                    totals,
                    percent: (
                      this.roundNumber(item.count * 100) / totals
                    ).toFixed(2),
                  },
                ];

                series.categories = [
                  ...series.categories,
                  `<div class="legend"><img class="icon" src="${item.url}" alt=""> ${item.name}</div>`,
                ];
                series.hits[index] = {
                  ...commonProps,
                  y: item.hits,
                  total,
                  percent: this.roundNumber((item.hits * 100) / total),
                };
                series.mistake[index] = {
                  color: "#B5B5B5",
                  y: item.mistake,
                  total,
                  percent: this.roundNumber((item.mistake * 100) / total),
                };
              });

              return {
                chart1: {
                  type: "donut",
                  height: 300,
                  formatter: `<b>{point.y} pruebas</b><br/>{point.percent:.2f} ${this.$t(
                    "OF_THE_TOTAL"
                  )} {point.totals}`,
                  legend: {
                    useHTML: true,
                    labelFormatter: function () {
                      return `<div class="legend"><img class="icon" src="${this.url}" alt=""> ${this.name}</div>`;
                    },
                  },
                  series: series.chart1,
                },
                chart2: {
                  type: "stackedBar",
                  height: 300,
                  categories: series.categories,
                  formatter: function () {
                    return `<b>${this.series.name}: ${this.point.y}</b><br>${
                      this.point.percent
                    } ${App.$t("OF_THE_TOTAL")} ${this.point.total}`;
                  },
                  hideLegend: true,
                  useHTML: true,
                  series: [
                    {
                      name: "Mistakes",
                      color: "#F7BE73",
                      data: series.mistake,
                    },
                    { name: "Hits", color: "#8BC34A", data: series.hits },
                  ],
                },
              };
            },
            options: {
              chart1: {},
              chart2: {},
            },
          },
          peoplePerformance: {
            url: "admin/stats/courseByPersonGroup",
            formatOptions: (data) => {
              const colors = ["#FA7A7E", "#F7BE73", "#8BC34A", "#68BBB0"];
              return {
                type: "column",
                height: 230,
                hideLegend: true,
                categories: [""],
                series: data.map((item, index) => ({
                  name: item.name,
                  data: [{ color: colors[index] || "#68BBB0", y: item.value }],
                })),
              };
            },
            options: {},
          },
          coursesByStars: {
            url: "admin/stats/courseAndStarts",
            formatOptions: (data) => data.startArr,
            initFunction: (data) => {
              const newData = [0, 0, 0, 0, 0, 0];
              let total = 0;
              data[0].forEach((item) => {
                const index = Math.floor(item.stars);
                const value = parseInt(item.total, 10);
                newData[index] += value;
                total += value;
              });
              return {
                startArr: newData,
                stars: [0, 1, 2, 3, 4, 5],
                total: total,
              };
            },
            defaultValue: [],
            options: [],
          },
          structureAndHotel: {
            url: "admin/stats/segmented/total/structureAndHotel",
            method: "post",
            formatOptions: (data) => ({
              type: "donut",
              formatter: "<b>{point.y} ({point.percent:.2f}%)</b>",
              series: data.map((item) => ({
                name: item.name,
                y: item.count,
                percent: item.percent,
                color:
                  (item.name || "").toLowerCase() === "hotel"
                    ? "#FA7A7E"
                    : "#68BBB0",
              })),
            }),
            initFunction: (data) => {
              let total = data.reduce(
                (acc, cur) => acc + parseInt(`${cur.count}`, 10),
                0
              );
              data.forEach((item) => {
                item.count = parseInt(`${item.count}`, 10);
                item.percent = this.roundNumber(item.count / total) * 100;
              });
              return data;
            },
            options: {},
          },
          schoolFinishedAndProgress: {
            url: "/admin/stats/schoolFinishedAndProgress",
            formatOptions: (data) => {
                let categories = [],
                    serieData = { finished: [], inCourse: [] };
                
                data.forEach((item) => {
                    categories = [...categories, item.name];
                    const finished = parseInt(item.finished, 10) || 0;
                    const inCourse = parseInt(item.inCourse, 10) || 0;
                    const total = finished + inCourse;
                    const finishedPercent = total > 0 ? this.roundNumber((finished * 100) / total) : 0;
                    const inCoursePercent = total > 0 ? this.roundNumber((inCourse * 100) / total) : 0;
                    
                    serieData.finished = [
                        ...serieData.finished,
                        {
                            y: finished,
                            total,
                            percent: finishedPercent
                        },
                    ];
                    serieData.inCourse = [
                        ...serieData.inCourse,
                        {
                            y: inCourse,
                            total,
                            percent: inCoursePercent
                        },
                    ];
                });
                
                return {
                    type: "stackedBar",
                    categories,
                    formatter: function () {
                        return `<b>${this.series.name}: ${this.point.y}</b><br>${
                            this.point.percent
                        } ${App.$t("OF_THE_TOTAL")} ${this.point.total}`;
                    },
                    series: [
                        {
                            name: this.$t("COURSES.FINISHED"),
                            color: "#8BC34A",
                            data: serieData.finished,
                        },
                        {
                            name: this.$t("COURSES.IN_PROGRESS"),
                            color: "#F7BE73",
                            data: serieData.inCourse,
                        },
                    ],
                };
            },
            initFunction: (data) => {
                return data.map((item) => ({
                    ...item,
                    finished: parseInt(item.finished, 10) || 0,
                    inCourse: parseInt(item.inCourse, 10) || 0,
                    total: (parseInt(item.finished, 10) || 0) + (parseInt(item.inCourse, 10) || 0),
                }));
            },
            currentSort: ["total", false],
            sortFunction: this.sortFunction,
            options: {},
          },
          coursesBySchool: {
            url: "/admin/stats/courseBySchool",
            formatOptions: (data) => {
              const totals = data.reduce((acc, cur) => acc + cur.count, 0);
              let categories = [],
                serieData = [];
              data.forEach((item) => {
                categories = [...categories, item.name];
                serieData = [
                  ...serieData,
                  {
                    y: item.count,
                    name: item.name || "",
                    totals,
                    percent: ((item.count * 100) / totals).toFixed(2),
                  },
                ];
              });
              return {
                type: "bar",
                formatter: function () {
                  return `<b>${this.point.name}: ${Math.abs(
                    this.point.y
                  )}</b><br/>${this.point.percent} ${App.$t(
                    "OF_THE_TOTAL"
                  )} ${Math.abs(this.point.totals)}`;
                },
                hideLegend: true,
                categories,
                series: [{ name: "", color: "#8BC34A", data: serieData }],
              };
            },
            currentSort: ["count", false],
            sortFunction: this.sortFunction,
            options: {},
          },
          coursesByDepartment: {
            url: "/admin/stats/departmentMoreCourse",
            formatOptions: (data) => {
              let categories = [],
                serieData = [];
              const totals = data.reduce((acc, cur) => acc + cur.count, 0);
              data.forEach((item) => {
                categories = [...categories, item.name];
                serieData = [
                  ...serieData,
                  {
                    y: item.count,
                    name: item.name || "",
                    totals,
                    percent: ((item.count * 100) / totals).toFixed(2),
                  },
                ];
              });
              return {
                type: "bar",
                formatter: function () {
                  return `<b>${this.point.name}: ${Math.abs(
                    this.point.y
                  )}</b><br/>${this.point.percent} ${App.$t(
                    "OF_THE_TOTAL"
                  )} ${Math.abs(this.point.totals)}`;
                },
                hideLegend: true,
                categories,
                series: [{ name: "", color: "#F7BE73", data: serieData }],
              };
            },
            currentSort: ["count", false],
            sortFunction: this.sortFunction,
            options: {},
          },
          // -------------------------- Evolutivo y Acumulado ---------------------
          finishedCourses: {
            url: "admin/stats/segmented/course",
            formatOptions: (data) => {
              const categories = [];
              const series = [
                {
                  name: this.$t('NEW_PLURAL'),
                  color: "#009688",
                  data: [],
                  dataLabels: {
                    enabled: true,
                    color: "#009688",
                    style: {
                      textOutline: "none",
                    },
                  },
                },
                {
                  name: this.$t('ACCUMULATED'),
                  data: [],
                  color: "#00695C",
                  dataLabels: {
                    enabled: true,
                    color: "#00695C",
                    style: {
                      textOutline: "none",
                    },
                  },
                },
              ];

              let prev = 0;
              Object.keys(data["finished"]).forEach((key) => {
                categories.push(key);
                const period = data["finished"][key].period;
                const total = data["finished"][key].total;

                series[0].data = [...series[0].data, this.roundNumber(period)];
                series[1].data = [...series[1].data, this.roundNumber(total)];
              });
              return {
                type: "line",
                hideLegend: false,
                categories,
                series,
              };
            },
            options: {},
          },

          userNewInPlatformThanFinishedOneCourse: {
            url: "/admin/stats/segmented/course/new-finished",
            formatOptions: (data) => {
              const categories = [];
              const series = [
                {
                  name: this.$t('NEW_PLURAL'),
                  color: "#009688",
                  data: [],
                  dataLabels: {
                    enabled: true,
                    color: "#009688",
                    style: {
                      textOutline: "none",
                    },
                  },
                },
                {
                  name: this.$t('ACCUMULATED'),
                  data: [],
                  color: "#00695C",
                  dataLabels: {
                    enabled: true,
                    color: "#00695C",
                    style: {
                      textOutline: "none",
                    },
                  },
                },
              ];

              let prev = 0;
              if (data.indexOf("finished") >= 0) {
                Object.keys(data["finished"]).forEach((key) => {
                  categories.push(key);
                  const period = data["finished"][key].period;
                  const total = data["finished"][key].total;

                  series[0].data = [
                    ...series[0].data,
                    this.roundNumber(period),
                  ];
                  series[1].data = [...series[1].data, this.roundNumber(total)];
                });
              }
              return {
                type: "line",
                hideLegend: false,
                categories,
                series,
              };
            },
            options: {},
          },

          startedCourses: {
            url: "/admin/stats/segmented/course/started",
            formatOptions: (data) => {
              const categories = [];
              const series = [
                {
                  name: this.$t('NEW_PLURAL'),
                  color: "#009688",
                  data: [],
                  dataLabels: {
                    enabled: true,
                    color: "#009688",
                    style: {
                      textOutline: "none",
                    },
                  },
                },
                {
                  name: this.$t('ACCUMULATED'),
                  data: [],
                  color: "#00695C",
                  dataLabels: {
                    enabled: true,
                    color: "#00695C",
                    style: {
                      textOutline: "none",
                    },
                  },
                },
              ];

              let prev = 0;
              Object.keys(data["started"]).forEach((key) => {
                categories.push(key);
                const period = data["started"][key].period;
                const total = data["started"][key].total;
                series[0].data = [...series[0].data, this.roundNumber(period)];
                series[1].data = [...series[1].data, this.roundNumber(total)];
              });
              return {
                type: "line",
                hideLegend: false,
                categories,
                series,
              };
            },
            options: {},
          },

          proccessCourses: {
            url: "/admin/stats/segmented/course/started-in-process",
            formatOptions: (data) => {
              const categories = [];
              const series = [
                {
                  name: this.$t('NEW_PLURAL'),
                  color: "#009688",
                  data: [],
                  dataLabels: {
                    enabled: true,
                    color: "#009688",
                    style: {
                      textOutline: "none",
                    },
                  },
                },
                {
                  name: this.$t('ACCUMULATED'),
                  data: [],
                  color: "#00695C",
                  dataLabels: {
                    enabled: true,
                    color: "#00695C",
                    style: {
                      textOutline: "none",
                    },
                  },
                },
              ];

              let prev = 0;
              Object.keys(data["started"]).forEach((key) => {
                categories.push(key);
                const period = data["started"][key].period;
                const total = data["started"][key].total;

                series[0].data = [...series[0].data, this.roundNumber(period)];
                series[1].data = [...series[1].data, this.roundNumber(total)];
              });
              return {
                type: "line",
                hideLegend: false,
                categories,
                series,
              };
            },
            options: {},
          },

          trainedPerson: {
            url: "admin/stats/segmented/person",
            formatOptions: (data) => {
              let categories = [];
              const series = [
                {
                  name: "Personas únicas en el mes",
                  color: "#009688",
                  data: [],
                  dataLabels: {
                    enabled: true,
                    color: "#009688",
                    style: {
                      textOutline: "none",
                    },
                  },
                },
                {
                  name: "Personas únicas desde el periodo inicial",
                  data: [],
                  color: "#00695C",
                  dataLabels: {
                    enabled: true,
                    color: "#00695C",
                    style: {
                      textOutline: "none",
                    },
                  },
                },
              ];

              let prev = 0;
              Object.keys(data).forEach((key) => {
                categories = [...categories, key];
                const period = data[key].period;
                const acumulado = data[key].total;
                //prev += period;
                series[0].data = [...series[0].data, this.roundNumber(period)];
                series[1].data = [
                  ...series[1].data,
                  this.roundNumber(acumulado),
                ];
              });
              return {
                type: "line",
                hideLegend: false,
                categories,
                series,
              };
            },
            options: {},
          },
          segmentedHours: {
            url: "admin/stats/segmented/hour",
            formatOptions: (data) => {
              let categories = [];
              const series = [
                {
                  name: this.$t('NEW_PLURAL'),
                  color: "#009688",
                  data: [],
                  dataLabels: {
                    enabled: true,
                    color: "#009688",
                    style: {
                      textOutline: "none",
                    },
                  },
                },
                {
                  name: this.$t('ACCUMULATED'),
                  data: [],
                  color: "#00695C",
                  dataLabels: {
                    enabled: true,
                    color: "#00695C",
                    style: {
                      textOutline: "none",
                    },
                  },
                },
              ];

              let prev = 0;
              Object.keys(data).forEach((key) => {
                categories = [...categories, key];

                const period = data[key].period.toFixed(2);
                const total = data[key].total.toFixed(2);

                //series[0].data = [...series[0].data, this.roundNumber(period)];
                //series[1].data = [...series[1].data, this.roundNumber(total)];
                series[0].data = [...series[0].data, +period];
                series[1].data = [...series[1].data, +total];
              });
              return {
                type: "line",
                hideLegend: false,
                categories,
                series,
              };
            },
            options: {},
          },
          // ----------------------------- Demografia  -----------------------------
          usersBySexAndAge: {
            url: "admin/stats/distributionBySexAndAge",
            formatOptions: (data) => ({
              type: "pyramid",
              height: 300,
              categoriesList: data.categories,
              formatter: function () {
                return `<b>${this.series.name}, ${this.point.category} años:  ${Math.abs(this.point.y)}` + App.$t("INDIVIDUALS")
                    + `</b><br/>${this.point.percent}`
                    + `</b><br/>${this.point.percent}`
                    + App.$t("OF_THE_TOTAL")
                    + `${Math.abs(this.point.totals)}`;
              },
              categories: [
                { description: this.$t("STATISTICS.AGE.MAN") },
                { description: this.$t("STATISTICS.AGE.WOMAN") },
              ],
              series: [
                {
                  name: this.$t("STATISTICS.USER.MAN"),
                  color: "#68BBB0",
                  data: data.man.map((value, index) => ({
                    y: value * -1,
                    percent: data.totals[index].manAVG.toFixed(2),
                    totals: data.totals[index].total,
                  })),
                },
                {
                  name: this.$t("STATISTICS.USER.WOMAN"),
                  color: "#FA7A7E",
                  data: data.woman.map((value, index) => ({
                    y: value,
                    percent: data.totals[index].womanAVG.toFixed(2),
                    totals: data.totals[index].total,
                  })),
                },
              ],
            }),
            initFunction: (data) => {
              const ranges = [
                { name: "0-4", max: 4 },
                { name: "5-9", max: 9 },
                { name: "10-14", max: 14 },
                { name: "15-19", max: 19 },
                { name: "20-24", max: 24 },
                { name: "25-29", max: 29 },
                { name: "30-34", max: 34 },
                { name: "35-40", max: 39 },
                { name: "40-45", max: 44 },
                { name: "45-49", max: 49 },
                { name: "50-54", max: 54 },
                { name: "55-59", max: 59 },
                { name: "60-64", max: 64 },
                { name: "65-69", max: 69 },
                { name: "70-74", max: 74 },
                { name: "75-79", max: 79 },
                { name: "80+", max: 999 },
              ];

              const { man, woman } = data;
              const initValues = new Array(ranges.length).fill(0);
              const newData = {
                man: [...initValues],
                woman: [...initValues],
                totals: [...initValues],
                categories: ranges.map((item) => item.name),
              };

              ranges.forEach((range, index) => {
                let manValues = { counter: 0, total: 0 },
                  womanValues = { counter: 0, total: 0 };
                man.forEach((item) => {
                  if (range.max >= parseInt(item.year, 10)) {
                    manValues.total += parseInt(item.count, 10);
                    manValues.counter += 1;
                  }
                });
                woman.forEach((item) => {
                  if (range.max >= parseInt(item.year, 10)) {
                    womanValues.total += parseInt(item.count, 10);
                    womanValues.counter += 1;
                  }
                });

                newData.man[index] = manValues.total;
                newData.woman[index] = womanValues.total;
                const total = manValues.total + womanValues.total;
                newData.totals[index] = {
                  total,
                  manAVG: this.roundNumber((manValues.total * 100) / total),
                  womanAVG: this.roundNumber((womanValues.total * 100) / total),
                };

                man.splice(0, manValues.counter);
                woman.splice(0, womanValues.counter);
              });

              return {
                man: newData.man.reverse(),
                woman: newData.woman.reverse(),
                categories: newData.categories.reverse(),
                totals: newData.totals.reverse(),
              };
            },
            options: {},
          },
          ageDistribution: {
            url: "/admin/stats/distribution-user-age",
            formatOptions: (data) => {
              let categories = [],
                serieData = [];
              const totals = data.reduce((acc, cur) => acc + cur.y, 0);
              data.forEach((item) => {
                categories = [...categories, item.name];
                serieData = [
                  ...serieData,
                  {
                    y: item.y,
                    totals,
                    percent: ((item.y * 100) / totals).toFixed(2),
                  },
                ];
              });
              return {
                type: "bar",
                height: 300,
                hideLegend: true,
                categories,
                formatter: function () {
                  return `<b>${this.point.category}: ${this.point.y}</b><br/>${
                    this.point.percent
                  } ${App.$t("OF_THE_TOTAL")} ${this.point.totals}`;
                },
                series: [{ name: "", color: "#68BBB0", data: serieData }],
              };
            },
            initFunction: (data) => data.user,
            options: {},
          },
          deviceDistribution: {
            url: "/admin/stats/devices-sesion",
            formatOptions: (data) => {
              const totals = data.reduce((acc, cur) => acc + cur.y, 0);
              return {
                type: "donut",
                height: 300,
                formatter: `<b>{point.y} dispositivos</b><br/>{point.percent:.2f} ${this.$t(
                  "OF_THE_TOTAL"
                )} {point.totals}`,
                series: data.map((device) => ({
                  ...device,
                  percent: (this.roundNumber(device.y * 100) / totals).toFixed(
                    2
                  ),
                  totals,
                  color:
                    (device.name || "").toLowerCase() === "computer"
                      ? "#F7BE73"
                      : "#A9C47E",
                })),
              };
            },
            initFunction: (data) => [...(data.devices || [])],
            options: {},
          },
          usersByCountries: {
            url: "/admin/stats/users-country",
            formatOptions: (data) => ({
              name: this.$t("STATISTICS.USERS.COUNTRY_DISTRIBUTION"),
              type: "map",
              height: 300,
              series: (data || []).map((item) => ({
                code3: item.country,
                z: item.count,
                code: item.country,
                countryName: item.countryName,
                type: "personas",
              })),
            }),
            options: {},
          },
          // -----------------------------  Actividad  -----------------------------
          activityInfo: {
            url: "/admin/stats/activity-info",
            method: "post",
            formatOptions: (data) => ({
              activeUsers: {
                type: "gauge",
                series: [
                  {
                    name: this.$t("STATISTICS.ACTIVES"),
                    color: "#68BBB0",
                    y: +data.activeUsers,
                    dataLabels: { enabled: false },
                  },
                  {
                    name: this.$t("STATISTICS.INACTIVES"),
                    color: "#FA7A7E",
                    y: data.inactives,
                    dataLabels: { enabled: false },
                  },
                ],
                footer: `<p class="total">${
                  data.activeUsers
                }</p><p class="percent">${data.activesAvg} ${this.$t(
                  "OF_THE_TOTAL"
                )}</p>`,
              },
              totalAcc: {
                title: this.$t("TOTAL_REGISTERED_PEOPLE"),
                color: "#F7BE73",
                icon: "fa fa-sign-in",
                value: data.totalUsers,
              },
              loggedOnceUsers: {
                title: `${data.loggedOnceUsers} ${this.$t("INDIVIDUALS")}`,
                subtitle: `${data.loggedOnceUsersAvg} ${this.$t(
                  "OF_THE_TOTAL"
                )}`,
                description: this.$t("ACCESS_AT_LEAST_ONCE"),
                color: "#A9C47E",
                icon: "fa fa-users",
              },
              loggedLastMonth: {
                title: `${data.loggedLastMonth} ${this.$t("INDIVIDUALS")}`,
                subtitle: "",
                description: this.$t("ACCESS_IN_LAST_30_DAYS"),
                color: "#68BBB0",
                icon: "fa fa-users",
              },
              inactiveUsers: {
                title: `${data.inactives} ${this.$t("INDIVIDUALS")}`,
                subtitle: `${data.inactivesAvg} ${this.$t("OF_THE_TOTAL")}`,
                description: this.$t("DISABLED_PEOPLE"),
                color: "#FA7A7E",
                icon: "fa fa-users",
              },
              neverAccess: {
                title: `${data.neverLogged} ${this.$t("INDIVIDUALS")}`,
                subtitle: `${data.neverLoggedAvg} ${this.$t("OF_THE_TOTAL")}`,
                description: this.$t("NEVER_LOGGED"),
                color: "#F7BE73",
                icon: "fa fa-users",
              },
            }),
            initFunction: (data) => {
              const inactives = data.totalUsers - data.activeUsers;
              const inactivesAvg = (
                (inactives * 100) /
                data.totalUsers
              ).toFixed(0);
              const activesAvg = (
                (data.activeUsers * 100) /
                data.totalUsers
              ).toFixed(0);
              const neverLogged = data.totalUsers - data.loggedOnceUsers;
              const neverLoggedAvg = this.roundNumber(
                (neverLogged * 100) / data.totalUsers
              );
              const loggedOnceUsersAvg = this.roundNumber(
                (data.loggedOnceUsers * 100) / data.totalUsers
              );
              return {
                inactives,
                inactivesAvg,
                activesAvg,
                neverLogged,
                neverLoggedAvg,
                loggedOnceUsersAvg,
                cardsData: [
                  { name: "Accesos Totales", value: data.totalUsers },
                  {
                    name: "Personas activas",
                    value: `${data.activeUsers} (${activesAvg}%)`,
                  },
                  {
                    name: "Personas inactivas",
                    value: `${inactives} (${inactivesAvg}%)`,
                  },
                  {
                    name: "Personas que han accedido al menos una vez",
                    value: `${data.loggedOnceUsers} (${loggedOnceUsersAvg}%)`,
                  },
                  {
                    name: "Personas que nunca han entrado en la plataforma",
                    value: `${neverLogged} (${neverLoggedAvg}%)`,
                  },
                  {
                    name: "Personas que han accedido en los últimos 30 días",
                    value: data.loggedLastMonth,
                  },
                ],
                ...data,
              };
            },
            options: {
              activeUsers: {},
              totalAcc: {},
              accessLeastOne: {},
              lastMonth: {},
              inactiveUsers: {},
              neverAccess: {},
            },
          },
          accessDays: {
            url: "/admin/stats/logins",
            formatOptions: (data) => ({
              type: "column",
              series: data.map((item) => [
                new Date(`${item.date} 00:00:01`).getTime(),
                item.count,
              ]),
            }),
            initFunction: (data) => data.logins,
            options: {},
          },
          platformAccessByHours: {
            url: "/admin/stats/heatMap/login",
            method: "post",
            formatOptions: this.formatHeatMap,
            initFunction: this.initHeatMap,
          },
          courseStartTime: {
            url: "/admin/stats/heatMap/start",
            method: "post",
            formatOptions: this.formatHeatMap,
            initFunction: this.initHeatMap,
          },
          courseEndTime: {
            url: "/admin/stats/heatMap/finish",
            method: "post",
            formatOptions: this.formatHeatMap,
            initFunction: this.initHeatMap,
          },
          general: {
            url: "admin/stats/general",
            formatOptions: (data) => ({
              courses: [
                {
                  title: this.$t("COURSES.HIGHEST_RATED"),
                  color: "#8BC34A",
                  icon: "fa fa-thumbs-o-up",
                  values: [],
                  /*data.npsTopCourses
										.map((item) => ({value: item.stars, name: (item.name || '').toLowerCase()})),*/
                },
                {
                  title: this.$t("COURSES.LOWEST_RATED"),
                  color: "#FA7A7E",
                  icon: "fa fa-thumbs-o-down",
                  values: [],
                  /*data.npsWorstCourses
										.map((item) => ({value: item.stars, name: (item.name || '').toLowerCase()})),*/
                },
              ],
              usersMoreActivesByCourses: [
                {
                  title: this.$t("MOST_ACTIVE_USERS"),
                  color: "#8BC34A",
                  headers: [this.$t("COURSES.HOME.TITLE")],
                  icon: "fa fa-user",
                  values: [],
                  /*data.usersMoreActives
										.map((item) => ({value: item.finishedCourses, name: (item.name || '').toLowerCase()})),*/
                },
                {
                  title: this.$t("LESS_ACTIVE_USERS"),
                  color: "#FA7A7E",
                  headers: [this.$t("COURSES.HOME.TITLE")],
                  icon: "fa fa-user",
                  values: [],
                  /*data.usersLessActives
										.map((item) => ({value: item.finishedCourses, name: (item.name || '').toLowerCase()})),*/
                },
              ],
            }),
            initFunction: (data) => ({
              npsTopCourses: data.stats.npsTopCourses,
              npsWorstCourses: data.stats.npsWorstCourses,
              usersMoreActives: data.stats.usersMoreActives,
              usersLessActives: data.stats.usersLessActives,
            }),
            options: {
              courses: [],
              usersMoreActivesByCourses: [],
            },
          },
          coursesStartedVsFinished: {
            url: "/admin/stats/courseFinishedVsStarted",
            formatOptions: (data) => {
              let newData = { categories: [], started: [], finished: [] };
              data.forEach((item) => {
                newData.categories = [
                  ...newData.categories,
                  `${item.percentFinished}% ${item.name}`,
                ];
                const totals = item.started + item.finished;
                newData.started = [
                  ...newData.started,
                  {
                    y: item.started * -1,
                    totals,
                    percent: this.roundNumber((item.started * 100) / totals),
                  },
                ];
                newData.finished = [
                  ...newData.finished,
                  {
                    y: item.finished,
                    totals,
                    percent: this.roundNumber((item.finished * 100) / totals),
                  },
                ];
              });
              return {
                type: "pyramid",
                pagination: 30,
                size: data.length,
                leftLabelDisabled: true,
                categoriesList: newData.categories,
                formatter: function () {
                  const categoryName = (
                    this.point.category.split("%")[1] || ""
                  ).trim();
                  return `${categoryName}<br><b>${this.series.name}: ${Math.abs(
                    this.point.y
                  )} cursos</b><br/>${this.point.percent} ${App.$t(
                    "OF_THE_TOTAL"
                  )} ${Math.abs(this.point.totals)}`;
                },
                categories: [
                  { description: this.$t("COURSES.STARTED") },
                  { description: this.$t("COURSES.FINISHED") },
                ],
                series: [
                  {
                    name: this.$t("COURSES.STARTED"),
                    color: "#68BBB0",
                    data: newData.started,
                  },
                  {
                    name: this.$t("COURSES.FINISHED"),
                    color: "#FA7A7E",
                    data: newData.finished,
                  },
                ],
              };
            },
            initFunction: (data) =>
              data.map((item) => ({
                ...item,
                percentFinished: item.percentFinished || 0,
                total: item.started + item.finished,
              })),
            currentSort: ["total", false],
            sortOptions: [
              { name: this.$t("COURSES.FINISHED"), key: "finished" },
              { name: this.$t("COURSES.STARTED"), key: "started" },
              { name: this.$t("SUBSCRIPTION.NAME"), key: "name" },
              { name: this.$t("STATISTICS.PERCENT_ENDED"), key: "percentFinished" },
              { name: this.$t("STATISTICS.TOTAL_CURSES"), key: "total" },
            ],
            sortFunction: this.sortFunction,
            options: {},
          },
          usersMoreActivesByActivity: {
            url: "admin/stats/user/moreAndLessTime",
            formatOptions: (data) => [
              {
                title: this.$t("MOST_ACTIVE_USERS"),
                color: "#8BC34A",
                icon: "fa fa-user",
                values: data.more,
              },
              {
                title: this.$t("LESS_ACTIVE_USERS"),
                color: "#FA7A7E",
                icon: "fa fa-user",
                values: data.less,
              },
            ],
            initFunction: (data) => {
              return {
                more: data.more.map((item) => {
                  const time = item.total.length
                    ? item.total.split(" ")
                    : ["0h 0min"];
                  return {
                    name: `${item.firstName || ""} ${
                      item.lastName || ""
                    }`.trim(),
                    value: `${time[0] || ""} ${time[1] || ""}`.trim(),
                  };
                }),
                less: data.less.map((item) => {
                  const time = item.total.length
                    ? item.total.split(" ")
                    : ["0h 0min"];
                  return {
                    name: `${item.firstName || ""} ${
                      item.lastName || ""
                    }`.trim(),
                    value: `${time[0] || ""} ${time[1] || ""}`.trim(),
                  };
                }),
              };
            },
            defaultValue: [],
            options: [],
          },
          // ----------------------------- Itinerarios -----------------------------
          itinerariesStartedAndFinished: {
            url: "/admin/stats/itineraryClosedAndProgress",
            formatOptions: (data) => {
              let categories = [],
                serieData = { countOpen: [], countClosed: [] };
              data.forEach((item) => {
                categories = [...categories, item.name];
                const total = item.countOpen + item.countClosed;
                serieData.countOpen = [
                  ...serieData.countOpen,
                  {
                    y: item.countOpen,
                    total,
                    percent: this.roundNumber((item.countOpen * 100) / total),
                  },
                ];
                serieData.countClosed = [
                  ...serieData.countClosed,
                  {
                    y: item.countClosed,
                    total,
                    percent: this.roundNumber((item.countClosed * 100) / total),
                  },
                ];
              });
              return {
                type: "stackedBar",
                pagination: 30,
                formatter: function () {
                  return `<b>${this.series.name}: ${this.point.y}</b><br>${
                    this.point.percent
                  } ${App.$t("OF_THE_TOTAL")} ${this.point.total}`;
                },
                size: data.length,
                categories,
                series: [
                  {
                    name: this.$t("ITINERARIES_STARTED"),
                    color: "#F7BE73",
                    data: serieData.countOpen,
                  },
                  {
                    name: this.$t("ITINERARIES_COMPLETED"),
                    color: "#8BC34A",
                    data: serieData.countClosed,
                  },
                ],
              };
            },
            initFunction: (data) =>
              data.map((item) => ({
                ...item,
                total: item.countClosed + item.countOpen,
              })),
            currentSort: ["name", true],
            sortFunction: this.sortFunction,
            sortOptions: [
              { name: this.$t("SUBSCRIPTION.NAME"), key: "name" },
              { name: this.$t("ITINERARIES_COMPLETED"), key: "countClosed" },
              { name: this.$t("ITINERARIES_STARTED"), key: "countOpen" },
              { name: this.$t("STATISTICS.ITINERARIES_TOTAL"), key: "total" },
            ],
            options: {},
          },
          // itinerariesCompletedByCountries: {
          // 	url: '/admin/stats/itineraryClosedByCountry',
          // 	formatOptions: (data) => ({
          // 		name: 'Itinerarios por países',
          // 		type: 'map',
          // 		series: (data || []).map((item) => ({code3: item.country, z: item.count, code: item.country, countryName: item.countryName, type: 'itinerarios'}))
          // 	}),
          // 	options: {},
          // },
        },
        excelOptions: {},
      };
    },
    computed: {
      currentFilters() {
        let filters = {};

        if (this.filters["dateFrom"]) {
          filters["dateFrom"] = {name: `Desde: ${this.filters["dateFrom"]}`};
        }

        if (this.filters["dateTo"]) {
          filters["dateTo"] = {name: `Hasta: ${this.filters["dateTo"]}`};
        }

        if (this.filters["category"]?.length) {
          filters["category"] = this.filters["category"]
              .map((ids) =>
                  this.professionalCategories.find((pc) => pc.id === ids)
              )
              .filter((pc) => pc?.id);
        }

        if (this.filters["active"]) {
          filters["active"] = this.onlyActivesList.find(
              (item) => item.id === this.filters["active"]
          );
        }

        return filters;
      },

      currentBdFilters() {
        let currentBdFilters = {};
        this.filterCategories.forEach((element) => {
          (this.bd_filters["category_" + element.id] || []).forEach(
            (filter_id) => {
              const filter = element.filters.find(
                (filter) => `${filter.id}` === `${filter_id}`
              );
              currentBdFilters[element.name] = [
                ...(currentBdFilters[element.name] || []),
                filter.name,
              ];
            }
          );
        });
        return currentBdFilters;
      },

      filtersApplied() {
        const categoryToFilters = {};

        (this.filterCategories || []).forEach((category) => {
          const categoryKey = `category_${category.id}`;
          categoryToFilters[categoryKey] = (
            this.bd_filters[categoryKey] || []
          ).map((id) => id);
        });

        return {
          ...this.filters,
          ...categoryToFilters,
        };
      },
      formationVisible() {
        return this.visiblePanels[0];
      },
      accumulatedVisible() {
        return this.visiblePanels[1];
      },
      demographyVisible() {
        return this.visiblePanels[2];
      },
      activityVisible() {
        return this.visiblePanels[3];
      },
      intinerariesVisible() {
        return this.visiblePanels[4];
      },
    },
    watch: {
      formationVisible() {
        if (this.visiblePanels[0]) this.loadByChunks(0);
      },
      accumulatedVisible() {
        if (this.visiblePanels[1]) this.loadByChunks(1);
      },
      demographyVisible() {
        if (this.visiblePanels[2]) this.loadByChunks(2);
      },
      activityVisible() {
        if (this.visiblePanels[3]) this.loadByChunks(3);
      },
      intinerariesVisible() {
        if (this.visiblePanels[4]) this.loadByChunks(4);
      },
    },
    beforeMount() {
      initLocale(this.$store, this.$el, this.$i18n);

     
    },
    mounted() {
      this.$i18n.locale = this.$el.attributes["locale"].value;

      this.filterCategories = filterCategories || [];

      this.filters = this.defaultFilters();
      this.bd_filters = this.defaultBdFilters();

      this.visiblePanels = [
        this.loadingMethods !== "Chunks",
        false,
        false,
        false,
      ];
      this.showNotification = this.loadingMethods !== "Chunks";

      
      this.statsFormationSettings   = statsFormationSettings || [];
      this.statsEvolutionSettings   = statsEvolutionSettings || [];
      this.statsDemographySettings  = statsDemographySettings || [];
      this.statsActivitySettings    = statsActivitySettings || [];
      this.statsItinerarySettings   = statsItinerarySettings || [];
      

      this.loadData();
    },
    methods: {

      checkFormationGroupIsEnabled() {
        return this.statsFormationSettings.length;
      },
      checkEvolutionGroupIsEnabled() {
        return this.statsEvolutionSettings.length;
      },
      checkDemographyGroupIsEnabled() {
        return this.statsDemographySettings.length;
      },
      checkActivityGroupIsEnabled() {
        return this.statsActivitySettings.length;
      },
      checkItineraryGroupIsEnabled() {
        return this.statsItinerarySettings.length;
      },

      checkFormationChartIsEnabled(key) {
        return this.statsFormationSettings.includes(key);
      },
      checkEvolutionChartIsEnabled(key) {
        return this.statsEvolutionSettings.includes(key);
      },
      checkDemographyChartIsEnabled(key) {
        return this.statsDemographySettings.includes(key);
      },
      checkActivityChartIsEnabled(key) {
        return this.statsActivitySettings.includes(key);
      },
      checkItineraryChartIsEnabled(key) {
        return this.statsItinerarySettings.includes(key);
      },

      composeFormationGroupSheetsConfig(){
        if (!this.checkFormationGroupIsEnabled() )
          return null;

        let config = {
          name: "Formación",
          tables:[]
        }

         //formationGeneral
         //peopleWithCourses formationHours
         if (this.checkFormationChartIsEnabled("peopleWithCourses") ||
            this.checkFormationChartIsEnabled("formationHours")
              ){
          config.tables.push ( {
            name: "",
            key: "formationGeneral",
            cells: [
              { name: "Datos Generales", key: "name" },
              { name: "", key: "value" },
            ],
          });
        }
        //formationCourses
        // courseStartedAndFinished requiredCourses openedCourses coursesByStars
        if (this.checkFormationChartIsEnabled("courseStartedAndFinished") ||
            this.checkFormationChartIsEnabled("requiredCourses") ||
            this.checkFormationChartIsEnabled("openedCourses") ||
            this.checkFormationChartIsEnabled("coursesByStars")
          ){
          config.tables.push ( {
            name: "",
            key: "formationCourses",
            cells: [
              { name: "Datos Cursos", key: "name" },
              { name: "", key: "value" },
            ],
          });
        }
        //coursesByStars
        if (this.checkFormationChartIsEnabled("coursesByStars") ){
          config.tables.push ({
            name: "Valoracion de cursos",
            key: "coursesByStars",
            cells: [
              { name: "Número de estrellas", key: "stars" },
              { name: "Cantidad de valoraciones", key: "startArr" },
            ],
          });
        }
        //formationPills
        //gamifiedTest gamifiedPills
        if (this.checkFormationChartIsEnabled("gamifiedPills") ||
            this.checkFormationChartIsEnabled("gamifiedTest")
            ){
          config.tables.push ({
            name: "",
            key: "formationPills",
            cells: [
              { name: "Pildoras Gamificadas", key: "name" },
              { name: "", key: "value" },
              { name: "", key: "subvalue" },
            ],
          });
        }
        //general 
        //peopleWithCourses formationHours
        if (this.checkFormationChartIsEnabled("general") ){
          config.tables.push ({
            name: "Cursos con valoración más alta",
            key: "general",
            cells: [
              { name: "Curso", key: "npsTopCourses.name" },
              {
                name: "Valoración (Estrellas)",
                key: "npsTopCourses.stars",
              },
            ],
         });
        }
          //general
          if (this.checkFormationChartIsEnabled("general") ){
            config.tables.push ({
              name: "Cursos con valoración más baja",
              key: "general",
              cells: [
                { name: "Curso", key: "npsWorstCourses.name" },
                {
                  name: "Valoración (Estrellas)",
                  key: "npsWorstCourses.stars",
                },
              ],
            });
          }
          //general
          if (this.checkFormationChartIsEnabled("general") ){
            config.tables.push ({
              name: "Personas más activas (Cursos Completados)",
              key: "general",
              cells: [
                { name: "Nombre", key: "usersMoreActives.name" },
                {
                  name: this.$t("COURSES.FINISHED"),
                  key: "usersMoreActives.finishedCourses",
                },
              ],
            });
          }
          //general
          if (this.checkFormationChartIsEnabled("general") ){
            config.tables.push ({
              name: "Personas menos activas (Cursos Completados)",
              key: "general",
              cells: [
                { name: "Nombre", key: "usersLessActives.name" },
                {
                  name: this.$t("COURSES.FINISHED"),
                  key: "usersLessActives.finishedCourses",
                },
              ],
            });          
          }

          //educativeStatus
          if (this.checkFormationChartIsEnabled("educativeStatus") ){
            config.tables.push ({
              name: "Estatus formativo",
              key: "educativeStatus",
              cells: [
                { name: "Rango puntos", key: "title" },
                { name: "Cantidad de personas", key: "points" },
              ],
            });          
          }
          //peoplePerformance
          if (this.checkFormationChartIsEnabled("peoplePerformance") ){
            config.tables.push ({
              name: "Desempeño de las personas",
              key: "peoplePerformance",
              cells: [
                { name: "Rango de formación", key: "name" },
                { name: "Cantidad de personas", key: "value" },
              ],
            });          
          }
          //structureAndHotel
          if (this.checkFormationChartIsEnabled("structureAndHotel") ){
            config.tables.push ({
              name: "Porcentaje por colectivo",
              key: "structureAndHotel",
              cells: [
                { name: "Nombre", key: "name" },
                { name: "Total", key: "count" },
                { name: "Porcentaje", key: "percent" },
              ],
            });          
          }
          //schoolFinishedAndProgress
          if (this.checkFormationChartIsEnabled("schoolFinishedAndProgress") ){
            config.tables.push ({
              name: "Escuela con más participación",
              key: "schoolFinishedAndProgress",
              cells: [
                { name: "Nombre", key: "name" },
                { name: this.$t("COURSES.IN_PROGRESS"), key: "inCourse" },
                { name: this.$t("COURSES.FINISHED"), key: "finished" },
              ],
            });          
          }
          //coursesByDepartment
          if (this.checkFormationChartIsEnabled("coursesByDepartment") ){
            config.tables.push ({
              name: "Creación de cursos por departamento",
              key: "coursesByDepartment",
              cells: [
                { name: "Nombre", key: "name" },
                { name: "Cantidad de cursos", key: "count" },
              ],
            });          
          }
          //coursesBySchool
          if (this.checkFormationChartIsEnabled("coursesBySchool") ){
            config.tables.push ({
              name: "Número de cursos por escuela",
              key: "coursesBySchool",
              cells: [
                { name: "Nombre", key: "name" },
                { name: "Cantidad de cursos", key: "count" },
              ],
            });          
          }
          //gamifiedTest
          if (this.checkFormationChartIsEnabled("gamifiedTest") ){
            config.tables.push ({
              name: "Pruebas gamificadas",
              key: "gamifiedTest",
              cells: [
                { name: "Nombre", key: "name" },
                { name: "Cantidad de pruebas", key: "count" },
                { name: this.$t("SUCCESSES"), key: "hits" },
                { name: this.$t("FAILURES"), key: "mistake" },
              ],
            });          
          }
    

        return config;

      },

      composeDemographyGroupSheetsConfig(){
        if (!this.checkDemographyGroupIsEnabled() )
          return null;

        let config = {
          name: this.$t("ADMIN.STATS.DEMOGRAPHICS"),
          tables:[]
        }

        //usersBySexAndAge
        if (this.checkDemographyChartIsEnabled("usersBySexAndAge") ){
          config.tables.push ({
            name: "Usuarios por sexo y edad (Hombres)",
            key: "usersBySexAndAge",
            cells: [
              { name: "Categorias", key: "categories" },
              { name: "Total", key: "man" },
            ],
          });
        }
        //ageDistribution
        if (this.checkDemographyChartIsEnabled("ageDistribution") ){
          config.tables.push ({
            name: this.$t("DISTRIBUTION_BY_AGE"),
            key: "ageDistribution",
            cells: [
              { name: "Categorias", key: "name" },
              { name: "Total", key: "y" },
            ],
          });
        }
        //deviceDistribution
        if (this.checkDemographyChartIsEnabled("deviceDistribution") ){
          config.tables.push ({
            name: this.$t("DISTRIBUTION_BY_DEVICE"),
            key: "deviceDistribution",
            cells: [
              { name: "Categorias", key: "name" },
              { name: "Total", key: "y" },
            ],
          });
        }
        //usersByCountries
        if (this.checkDemographyChartIsEnabled("usersByCountries") ){
          config.tables.push ({
            name: this.$t("DISTRIBUTION_BY_COUNTRY"),
            key: "usersByCountries",
            cells: [
              { name: "Pais", key: "countryName" },
              { name: "Total", key: "count" },
            ],
          });
        }
        
        return config;

      },

      composeActivityGroupSheetsConfig(heatMapCells){
        if (!this.checkActivityGroupIsEnabled() )
          return null;

        let config = {
          name: "Actividad",
          tables:[]
        }

         //activityInfo
         if (this.checkActivityChartIsEnabled("activityInfo") ){
          config.tables.push ({
            name: "",
            key: "activityInfo",
            cells: [
              { name: "Datos Generales", key: "cardsData.name" },
              { name: "", key: "cardsData.value" },
            ],
          });
        }
        //usersMoreActivesByActivity
        if (this.checkActivityChartIsEnabled("usersMoreActivesByActivity") ){
          config.tables.push ({
            name: this.$t("TIME_ACTIVE_PEOPLE"),
            key: "usersMoreActivesByActivity",
            cells: [
              { name: "Nombre", key: "more.name" },
              { name: "Tiempo", key: "more.value" },
            ],
          });
        }
        //usersMoreActivesByActivity
        if (this.checkActivityChartIsEnabled("usersMoreActivesByActivity") ){
          config.tables.push ({
            name: "Personas menos activas (Tiempo de uso de la plataforma)",
            key: "usersMoreActivesByActivity",
            cells: [
              { name: "Nombre", key: "less.name" },
              { name: "Tiempo", key: "less.value" },
            ],
          });
        }
        //accessDays
        if (this.checkActivityChartIsEnabled("accessDays") ){
          config.tables.push ({
            name: "Inicios de sesión",
            key: "accessDays",
            cells: [
              { name: "Fecha", key: "date" },
              { name: "Cantidad", key: "count" },
            ],
          });
        }
        //platformAccessByHours
        if (this.checkActivityChartIsEnabled("platformAccessByHours") ){
          config.tables.push ({
            name: this.$t("PLATFORM_ACCESS_TIME"),
            key: "platformAccessByHours",
            cells: heatMapCells,
          });
        }
        //courseStartTime
        if (this.checkActivityChartIsEnabled("courseStartTime") ){
          config.tables.push ({
            name: this.$t("COURSES.START_TIME"),
            key: "courseStartTime",
            cells: heatMapCells,
          });
        }
        //courseEndTime
        if (this.checkActivityChartIsEnabled("courseEndTime") ){
          config.tables.push ({
            name: this.$t("COURSES.END_TIME"),
            key: "courseEndTime",
            cells: heatMapCells,
          });
        }
        //coursesStartedVsFinished
        if (this.checkActivityChartIsEnabled("coursesStartedVsFinished") ){
          config.tables.push ({
            name: this.$t("COURSES.FINISHED_VS_STARTED"),
            key: "coursesStartedVsFinished",
            cells: [
              { name: "Cursos", key: "name" },
              { name: "Empezados", key: "started" },
              { name: "Finalizados", key: "finished" },
              { name: "Finalizados (%)", key: "percentFinished" },
            ],
          });
        }

        return config;
      },

      
      composeItineraryGroupSheetsConfig(){
        if (!this.checkItineraryGroupIsEnabled() )
          return null;

        let config = {
          name: this.$t("ADMIN.STATS.ITINERARIES"),
          tables:[]
        }

         //itinerariesStartedAndFinished
         if (this.checkItineraryChartIsEnabled("itinerariesStartedAndFinished") ){
          config.tables.push ({
            name: this.$t("ITINERARIES_STARTED_AND_COMPLETED"),
            key: "itinerariesStartedAndFinished",
            cells: [
              { name: "Itinerario", key: "name" },
              { name: "Empezados", key: "countOpen" },
              { name: "Completados", key: "countClosed" },
              { name: "Total", key: "total" },
            ],
          });
        }
        //itinerariesCompletedByCountries
        if (this.checkItineraryChartIsEnabled("itinerariesCompletedByCountries") ){
          config.tables.push ({
            name: "Itinerarios completados por paises",
            key: "itinerariesCompletedByCountries",
            cells: [
              { name: "Pais", key: "countryName" },
              { name: "Total", key: "count" },
            ],
          });
        }

        return config;
      },

      composeFormationGroupGeneralSheetsData(){
        if (!this.checkFormationGroupIsEnabled() )
          return [];

        let config = [];

         //peopleWithCourses
         if (this.checkFormationChartIsEnabled("peopleWithCourses") ){
          config.push (
          {
            name: "Total de Personas",
            //value: `${peopleWithCourses.originalData.totalPerson}`,
            value: this.chartData.peopleWithCourses.originalData.totalPerson
          },
          {
            name: "Personas que se estan formando actualmente",
            //value: `${peopleWithCourses.originalData.totalInCourse}`,
            value: this.chartData.peopleWithCourses.originalData.totalInCourse
          },
          {
            name: "Personas que han completado al menos un curso",
            //value: `${peopleWithCourses.originalData.totalOneCourse} (${peopleWithCourses.originalData.percentWithOneCourse}%)`,
            value: this.chartData.peopleWithCourses.originalData.percentWithOneCourse
          });
        }
        //formationHours
        if (this.checkFormationChartIsEnabled("formationHours") ){
          config.push ({
            name: `${this.$t("HOURS_OF_TRAINING")}`,
            //value: `${formationHours.originalData.horas}`,
            value: this.chartData.formationHours.originalData.horas
          },
          {
            name: "Media de horas por persona",
            //value: `${formationHours.originalData.promedio}`,
            value: this.chartData.formationHours.originalData.promedio
          });
        }

        return config;
      },

      composeFormationGroupCoursesSheetsData (){
        if (!this.checkFormationGroupIsEnabled() ) 
          return [];

        let config = [];

         //courseStartedAndFinished
         if (this.checkFormationChartIsEnabled("courseStartedAndFinished") ){
          config.push ({
            name: this.$t("COURSES.STARTED"),
            //value: `${courseStartedAndFinished.originalData.started}`,
            value: this.chartData.courseStartedAndFinished.originalData.started
          },
          {
            name: this.$t("COURSES.IN_PROGRESS"),
            //value: `${courseStartedAndFinished.originalData.in_process}`,
            value: this.chartData.courseStartedAndFinished.originalData.in_process
          },
          {
            name: this.$t("COURSES.FINISHED"),
            //value: `${courseStartedAndFinished.originalData.finished}`,
            value: this.chartData.courseStartedAndFinished.originalData.finished
          });
        }
        //requiredCourses
        if (this.checkFormationChartIsEnabled("requiredCourses") ){
          config.push ({
            name: "Cantidad de cursos obligatorios",
            //value: `${requiredCourses.originalData}`,
            value: this.chartData.requiredCourses.originalData
          });
        }
        //openedCourses
        if (this.checkFormationChartIsEnabled("openedCourses") ){
          config.push ({
            name: "Cantidad de cursos voluntarios",
            //value: `${openedCourses.originalData}`,
            value: this.chartData.openedCourses.originalData
          });
        }
        //coursesByStars
        if (this.checkFormationChartIsEnabled("coursesByStars") ){
          let data = "";
          if ( this.chartData.coursesByStars.originalData ){
            data = this.chartData.coursesByStars.originalData.total
          }
          config.push ({
            name: "Total de valoraciones",
            //value: `${coursesByStars.originalData.total}`,
            value: data
          });
        }

        return config;
      },

      composeFormationGroupPillsSheetsData (){
        if (!this.checkFormationGroupIsEnabled() ) 
          return [];

        let config = [];

         //gamifiedPills
         if (this.checkFormationChartIsEnabled("gamifiedPills") ){
          config.push ({
            name: "Total de pildoras gamificadas",
            //value: `${gamifiedPills.originalData.totalGame}`,
            value: this.chartData.gamifiedPills.originalData.totalGame,
            subvalue: `${this.$t("OUT_OF_TOTAL")} ${this.formatNumber(
              //gamifiedPills.originalData.totalGeneral
              this.chartData.gamifiedPills.originalData.totalGeneral
            )}`,
          },
          {
            name: "Total de aciertos",
            //value: `${gamifiedPills.originalData.totalHits}`,
            value: this.chartData.gamifiedPills.originalData.totalHits,
            subvalue: `${this.formatNumber(
              //gamifiedPills.originalData.percentHits
              this.chartData.gamifiedPills.originalData.percentHits
            )} ${this.$t("OF_THE_TOTAL")}`,
          },
          {
            name: "Total de fallos",
            //value: `${gamifiedPills.originalData.totalMistake}`,
            value: this.chartData.gamifiedPills.originalData.totalMistake,
            subvalue: `${this.formatNumber(
              //gamifiedPills.originalData.percentMistake
              this.chartData.gamifiedPills.originalData.percentMistake
            )} ${this.$t("OF_THE_TOTAL")}`,
          });
        }
        
        return config;
      },
      

      defaultFilters() {
        return Object.assign(
          {},
          {
            active: 2,
            dateFrom: "",
            dateTo: "",
          }
        );
      },

      defaultBdFilters() {
        return this.filterCategories.reduce(
          (acc, cur) => ({ ...acc, ["category_" + cur.id]: [] }),
          {}
        );
      },

      removeFilter(filter) {
        if (!this.isLoading) this.filters[filter] = "";
      },

      removeCategory(category_id) {
        if (!this.isLoading)
          this.filters["category"] =
            this.filters["category"].filter(
              (filter_id) => filter_id !== category_id
            ) || [];
      },

      removeBdFilter(categoryName) {
        if (!this.isLoading) {
          this.filterCategories.forEach((element) => {
            if (element.name === categoryName)
              this.bd_filters["category_" + element.id] = [];
          });
        }
      },

      toggleFilterDisplay() {
        this.showFilters = !this.showFilters;
      },

      applyFilters() {
        if (!this.isLoading) this.loadData();
      },

      clearFilters() {
        if (!this.isLoading) {
          this.filters = this.defaultFilters();
          this.bd_filters = this.defaultBdFilters();
          this.loadData();
        }
      },

      sortFunction: (data, key = "name", ASC = true) => {
        if (isNaN(data[0][key])) {
          return data.sort((a, b) => {
            const [val1, val2] = ASC ? [-1, 1] : [1, -1];
            if (a[key].toUpperCase() < b[key].toUpperCase()) return val1;
            if (a[key].toUpperCase() > b[key].toUpperCase()) return val2;
            return 0;
          });
        }
        return data.sort((a, b) => (ASC ? a[key] - b[key] : b[key] - a[key]));
      },

      loadData() {
        this.isLoading = true;
        this.showFilters = false;
        this.loadQueue = Object.keys(this.chartData);
        this.loadQueue.forEach((key) => (this.chartData[key].isLoaded = false));
        this.params = Object.assign({}, this.filtersApplied);
        this.chunksLoaded = [false, false, false, false, false];

        if (this.params.active > 0) this.params.active -= 1;
        else delete this.params.active;

        ((
          {
            Parallel: this.loadAll,
            FirstAvailable: this.loadByAvailables,
            Chunks: this.loadOpenedAccordion,
          }[this.loadingMethods] || (() => {})
        )());
      },

      loadFinished() {
        this.prepareExcelData();
        this.isLoading = false;
      },

      loadAll() {
        Promise.all(this.loadQueue.map((key) => this.loadByKey(key))).finally(
          () => this.loadFinished()
        );
      },

      loadOpenedAccordion() {
        this.isLoading = false;
        const firstIndex = this.visiblePanels.findIndex((value) => value);
        if (firstIndex > -1) this.loadByChunks(firstIndex);
      },

      loadByChunks(chunkIndex = 0) {
        if (this.loadingMethods !== "Chunks") return;
        if (this.chunksLoaded[chunkIndex]) return;
        this.isLoading = true;
        this.showNotification = true;
        this.chunksLoaded[chunkIndex] = true;

        const chartData = [ this.statsFormationSettings,
          this.statsEvolutionSettings,
          this.statsDemographySettings,
          this.statsActivitySettings,
          this.statsItinerarySettings          
        ];

        Promise.all(
          chartData[chunkIndex].map((key) => this.loadByKey(key))
        ).finally(() => {
          
            const nextOpenedAndUnloadedChunk = this.visiblePanels.findIndex(
              (value, index) => value && !this.chunksLoaded[index]
            );
            if (nextOpenedAndUnloadedChunk > -1)
              return this.loadByChunks(nextOpenedAndUnloadedChunk);

            const nextUnloadedChunkIndex = this.chunksLoaded.findIndex(
              (chunk) => !chunk
            );
            if (nextUnloadedChunkIndex > -1)
              return this.loadByChunks(nextUnloadedChunkIndex);
          
          this.loadFinished();
        });
      },

      loadByAvailables() {
        this.loadersCompleted = 0;
        for (let i = 0; i < this.loadersStarted; i += 1) {
          this.loadFirstAvailable();
        }
      },

      loadFirstAvailable() {
        this.loadByKey(this.loadQueue.shift());
      },

      loadByKey(key) {
        const {
          url,
          method,
          defaultValue,
          currentSort,
          initFunction,
          formatOptions,
          sortFunction,
        } = {
          url: "",
          method: "get",
          defaultValue: {},
          currentSort: ["name", true],
          initFunction: (data) => data,
          formatOptions: (data) => data,
          sortFunction: (data) => data,
          ...this.chartData[key],
          isLoaded: false,
        };

        if (url.length) {
          this.chartData[key].isLoaded = false;
          this.chartData[key].options = defaultValue;
          const config =
            method === "get" ? { params: this.params } : this.params;
          return axios[method](url, config)
            .then((response) => {
              this.chartData[key].isLoaded = true;
              this.chartData[key].originalData = initFunction(
                response.data.data
              );
              this.chartData[key].options = formatOptions(
                sortFunction(
                  this.chartData[key].originalData,
                  currentSort[0],
                  currentSort[1]
                )
              );
            })
            .finally(() => {
              if (this.loadingMethods === "FirstAvailable") {
                if (this.loadQueue.length) this.loadFirstAvailable();
                else {
                  this.loadersCompleted += 1;
                  if (this.loadersCompleted === this.loadersStarted)
                    this.loadFinished();
                }
              }
            });
        }
        return new Promise(() => {});
      },

      formatNumber(number) {
        if (!number) return "0";
        return number.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
      },

      formatHeatMap(data) {
        return Object.assign(
          {},
          {
            type: "heatmap",
            categories: this.heatMapCategories,
            color: "#FDAB8D",
            series: data.series,
          }
        );
      },

      initHeatMap(data) {
        const [hours, days] = this.heatMapCategories;
        let processedData = new Array(hours.length).fill(
          new Array(days.length).fill(0)
        );
        let series = [];

        data.forEach((item) => {
          processedData[item.time] = [...processedData[item.time]];
          processedData[item.time][item.weekday] = parseInt(item.count, 10);
          series = [...series, [item.time, item.weekday, item.count]];
        });

        return {
          processedData: processedData.reduce(
            (acc, cur, i) => ({ ...acc, [hours[i]]: [...cur] }),
            {}
          ),
          days,
          series,
        };
      },

      showModalOptions(props) {
        if (props.length === 2) {
          const [key, title] = props;
          this.modalData = {
            showModal: true,
            key,
            options: this.chartData[key].sortOptions,
            currentSort: this.chartData[key].currentSort,
            title: title,
          };
        }
      },

      roundNumber(num) {
        return Math.round(num * 100) / 100;
      },

      applySorting(props) {
        const [key, sortBy, isAsc] = props;
        const { originalData, sortFunction, formatOptions, defaultValue } = {
          defaultValue: {},
          ...this.chartData[key],
        };
        this.chartData[key].currentSort = [sortBy, isAsc];
        this.chartData[key].options = defaultValue;
        this.chartData[key].options = formatOptions(
          sortFunction(originalData, sortBy, isAsc)
        );
      },
      sheetsConfig() {
        const heatMapCells = [
          { name: "", key: "days" },
          ...this.heatMapCategories[0].map((hour) => ({
            name: hour,
            key: `processedData.${hour}`,
          })),
        ];

        const formationGroupSheetsConfig = this.composeFormationGroupSheetsConfig();
        const demographyGroupSheetsConfig = this.composeDemographyGroupSheetsConfig();
        const activityGroupSheetsConfig = this.composeActivityGroupSheetsConfig(heatMapCells);
        const itineraryGroupSheetsConfig = this.composeItineraryGroupSheetsConfig();

        let groupsSheetsConfig = [{
          name: this.$t("FILTERS"),
          tables: [],
        }];

        if (demographyGroupSheetsConfig != null){
          groupsSheetsConfig.push(demographyGroupSheetsConfig);
        }          
        if (activityGroupSheetsConfig != null){
          groupsSheetsConfig.push(activityGroupSheetsConfig);
        }          
        if (formationGroupSheetsConfig != null){
          groupsSheetsConfig.push(formationGroupSheetsConfig);
        }          
        if (itineraryGroupSheetsConfig != null){
          groupsSheetsConfig.push(itineraryGroupSheetsConfig);
        }
        return groupsSheetsConfig;
      },
      sheetSourceData() {
        const {
          peopleWithCourses,
          formationHours,
          courseStartedAndFinished,
          requiredCourses,
          openedCourses,
          gamifiedPills,
          coursesByStars,
        } = this.chartData;


        const formationGroupGeneralSheetsData = this.composeFormationGroupGeneralSheetsData();
        const formationGroupCoursesSheetsData = this.composeFormationGroupCoursesSheetsData();
        const formationGroupPillsSheetsData = this.composeFormationGroupPillsSheetsData();
 


        return Object.keys(this.chartData).reduce(
          (acc, key) => ({ ...acc, [key]: this.chartData[key].originalData }),
          {
            formationGeneral: formationGroupGeneralSheetsData,
            formationCourses: formationGroupCoursesSheetsData,
            formationPills: formationGroupPillsSheetsData,
          }
        );
      },

      prepareExcelData() {
        this.excelOptions = Object.assign(
          {},
          {
            sheets: [...this.sheetsConfig()],
            sourceData: { ...this.sheetSourceData() },
            fileName: "Stats",
          }
        );
      },

      print() {
        const cssProps = Object.values(document.styleSheets || {}).reduce(
          (acc, cur) =>
            `${acc}${Object.values(cur.cssRules || {}).reduce(
              (cur2, rul2) => `${cur2} ${rul2.cssText}`,
              ""
            )}`,
          ""
        );

        const mywindow = window.open("", "PRINT", "height=1200,width=800");
        mywindow.document.write(
          '<html lang="es"><head><title>' + document.title + "</title>"
        );
        mywindow.document.write(`<style>${cssProps}</style>`);
        mywindow.document.write('</head><body id="print-content">');
        mywindow.document.write(
          document.getElementById("print-content").innerHTML
        );
        mywindow.document.write("</body></html>");

        mywindow.document.close();
        mywindow.focus();

        setTimeout(function () {
          mywindow.print();
          mywindow.close();
        }, 1000);

        return true;
      },
    },
  }).$mount("#general-stats");
}
