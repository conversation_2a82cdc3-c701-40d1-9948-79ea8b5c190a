<template>
  <div v-if="loading" class="d-flex align-items-center justify-content-center">
    <spinner />
  </div>
  <form class="CreateHelpText" id="form-create-help-text" name="form-create-help-text" v-else @submit.prevent="submit()">
    <div class="form-group required col-xs-12 col-md-7 d-flex flex-row flex-nowrap align-items-end">
      <div class="flex-grow-1">
        <label for="category">{{ $t('CATEGORY') }}</label>
        <select name="category" id="category" class="custom-select" v-model="categoryId">
          <option v-for="category in categories" :value="category.id">{{ category.name }}</option>
        </select>
      </div>
    </div>
    <div class="p-4">
      <Translation
          v-model="locale"
          :warning="warnings"
          v-for="t in translations"
          v-if="t.locale === locale"
      >
      <template v-slot:content>
        <div class="form-group required col-xs-12 col-md-5">
          <label class="form-label" for="" >{{ $t('TITLE') }}</label>
          <input type="text" class="form-control" required name="title" id="title" v-model="t.title">
        </div>
        <div class="col-12">
          <label class="form-label">{{ $t('TEXT') }}</label>
          <froala tag="textarea" :config="froalaConfig" v-model="t.text"></froala>
        </div>
      </template>
      </Translation>
    </div>
  </form>
</template>

<script>
import {get} from "vuex-pathify";
import Spinner from "../../admin/components/base/Spinner.vue";
import Translation from "../../common/components/Translation.vue";

export default {
  name: "CreateHelpText",
  components: {Translation, Spinner},
  data() {
    return {
      loading: true,
      helpValues: {},
      locale: null,
      categoryId: null,
    };
  },
  computed: {
    categories: get('helpTextModule/categories'),
    warnings: get('helpTextModule/warnings'),
    translations: get('helpTextModule/translations'),
    locales: get('localeModule/locales'),
    userLocale: get("localeModule/userLocale"),
    defaultLocale: get('localeModule/defaultLocale'),
    froalaConfig() {
      return {
        ...this.$store.getters['froalaEditorModule/getDefaultConfiguration'],
        height:300,
        pluginsEnabled: ['align', 'link', 'url', 'image', 'lists']
      }
    }
  },
  async created() {
    this.loading = true;
    const isUpdate = this.$route.name === 'UpdateHelpText';

    this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: isUpdate ? this.$t('HELP_TEXT.UPDATE_HELP_CONTENT') : this.$t('HELP_TEXT.CREATE_HELP_CONTENT'),
        params: {}
      }
    });

    this.$store.dispatch('contentTitleModule/setActions', { route: this.$route.name, actions: [
        {
          name: this.$t('SAVE'),
          event: 'onSave',
          class: 'btn btn-primary'
        }
      ]});

    await this.$store.dispatch('helpTextModule/getCategories');
    await this.$store.dispatch('helpTextModule/getTranslations');
    this.locale = this.userLocale

    if (isUpdate) {
      const result = await this.$store.dispatch('helpTextModule/getHelpText', this.$route.params.id);
      const { data } = result;
      this.helpValues = data;
      this.translations.forEach((translation, index, arr) => {
        if(this.helpValues[translation.locale]){
          arr[index] = { ...translation, ...this.helpValues[translation.locale] };
          this.categoryId = this.helpValues[translation.locale].categoryId
        }
      })
    }
    this.loading = false;
  },
  mounted() {
    this.$eventBus.$on('onSave', e => {
      this.submit();
    });
  },
  beforeDestroy() {
    this.$eventBus.$off('onSave');
  },
  methods: {
    submit() {
      let data = [{'translations': this.translations, 'categoryId': this.categoryId}];
      const formData = new FormData();
      formData.append('data', JSON.stringify(data));
      const isUpdate = this.$route.name === 'UpdateHelpText';

      this.$alertify.confirmWithTitle(
          this.$t(`HELP_TEXT.SAVE.CONFIRM.TITLE`),
          this.$t(`HELP_TEXT.SAVE.CONFIRM.DESCRIPTION`),
          () => {
            this.save(formData, isUpdate)
          },
          () => {},
      )
    },

    save(formData, update = false) {
      const self = this;
      function saveData() {
        if (update) return self.$store.dispatch('helpTextModule/updateHelpText', { id: self.$route.params.id, formData});
        else return self.$store.dispatch('helpTextModule/saveNewHelpText', formData);
      }

      saveData().then(res => {
        const { error, data } = res;
        if (error) this.$toast.error(this.$t('HELP_TEXT.SAVE.FAILED') + '')
        else {
          this.$toast.success(this.$t('HELP_TEXT.SAVE.SUCCESS') + '');
          this.$store.dispatch('routerModule/setDeleteLastRoute', true);
          this.$router.replace({ name: 'Home' })
        }
      })
    }
  }
}
</script>

 <style scoped lang="scss"> 
.CreateHelpText {
  padding: 1rem 0.5rem 0.5rem;
  @include nav-bar-style;
}
</style>
