<template>
  <div class="ChapterTypeContent pt-2">
    <div class="d-flex align-items-center justify-content-start mb-2">
      <button
        style="display: none"
        ref="openChapterContentModal"
        type="button"
        class="btn btn-primary ml-auto"
        data-bs-toggle="modal"
        data-bs-target="#chapter-type-content-modal"
      ></button>
      <button class="btn btn-primary ml-auto" @click="openModal()">
        {{ $t("CHAPTER.CONTENT.ADD") }}
      </button>
    </div>
    <div>
      <div
        class="d-flex align-items-center justify-content-center"
        v-if="loading"
      >
        <spinner />
      </div>
      <table
        class="table"
        :id="`chapter-content-type-table-${$route.params.id}`"
        v-show="!loading"
      >
        <thead>
          <tr>
            <th style="width: 50px"></th>
            <th style="width: 150px" class="text-center">
              {{ $t("POSITION") }}
            </th>
            <th>{{ $t("CHAPTER.CONTENT.SECTION") }}</th>
            <th style="width: 150px" class="text-center">
              {{ $t("ACTIONS") }}
            </th>
          </tr>
        </thead>
        <tbody ref="chapter-content-table-body">
          <tr v-for="content in contents" :key="content.id">
            <td>
              <div class="d-flex flex-column ml-2" style="width: 40px">
                <button
                  type="button"
                  class="btn btn-sm btn-info mb-1 btn-sm"
                  @click="increaseChapterContentPosition(content)"
                >
                  <i class="fas fa-arrow-up"></i>
                </button>
                <button
                  type="button"
                  class="btn btn-sm btn-info btn-sm"
                  @click="decreaseChapterContentPosition(content)"
                >
                  <i class="fas fa-arrow-down"></i>
                </button>
              </div>
            </td>
            <td class="text-center">
              <span>{{ content.position }}</span>
              <!--            <div class="d-flex flex-row align-items-center justify-content-center">-->
              <!--            </div>-->
            </td>
            <td>{{ content.title }}</td>
            <td class="text-center">
              <button
                type="button"
                class="btn btn-primary btn-sm"
                @click="editContent(content)"
              >
                <i class="fas fa-edit"></i>
              </button>
              <button
                type="button"
                class="btn btn-danger btn-sm"
                @click="deleteContent(content)"
              >
                <i class="fas fa-trash-alt"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div
      class="modal fade"
      id="chapter-type-content-modal"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabindex="-1"
      aria-labelledby="chapter-type-content-modalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
          <div class="modal-header">
            <h5 v-if=updating class="modal-title">{{ $t("CONTENT.CONFIGUREFIELD.TITLE_MODAL.UPDATE") }}</h5>
            <h5 v-else class="modal-title">{{ $t("CONTENT.CONFIGUREFIELD.TITLE_MODAL.ADD") }}</h5>
            <button
              ref="closeChapterContentModal"
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <type-content-form
              :loading="saving"
              v-model="content"
              @submit="saveContent"
            ></type-content-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import TypeContentForm from "../components/typeContent/TypeContentForm.vue";
import Spinner from "../../base/BaseSpinner.vue";

export default {
  name: "ChapterTypeContent",
  components: { Spinner, TypeContentForm },
  data() {
    return {
      content: {
        title: "",
        content: "",
        position: 0,
      },
      saving: false,
      updating: false,
    };
  },
  computed: {
    contents: get("chapterTypeContentModule/contents"),
    loading: get("chapterTypeContentModule/loading"),
  },
  created() {
    this.$store.dispatch(
      "chapterTypeContentModule/getChapterContents",
      this.$route.params.id
    );
  },
  methods: {
    openModal() {
      this.updating = false;
      this.content = {
        title: "",
        content: "",
        position: 0,
      };
      this.$refs["openChapterContentModal"].click();
    },
    /**
     *
     * @param formData
     */
    saveContent(formData) {
      const self = this;
      function save() {
        if (formData.get("id")) {
          return self.$store.dispatch(
            "chapterTypeContentModule/updateChapterContent",
            {
              chapterId: self.$route.params.id,
              chapterContentId: formData.get("id"),
              formData,
            }
          );
        } else {
          return self.$store.dispatch(
            "chapterTypeContentModule/createChapterContent",
            {
              chapterId: self.$route.params.id,
              formData,
            }
          );
        }
      }

      this.saving = true;
      save()
        .then((res) => {
          const { error } = res;
          if (error)
            this.$toast.error(this.$t("CHAPTER.CONTENT.SAVE.FAILED") + "");
          else {
            this.$toast.success(this.$t("CHAPTER.CONTENT.SAVE.SUCCESS") + "");
            this.$refs["closeChapterContentModal"].click();
            this.content = {
              title: "",
              content: "",
              position: 0,
            };
          }
        })
        .finally(() => {
          this.saving = false;
        });
    },
    editContent(content) {
      this.updating = true;
      this.content = structuredClone(content);
      this.$refs["openChapterContentModal"].click();
    },
    deleteContent(content) {
      this.$alertify.confirmWithTitle(
        this.$t("CHAPTER.CONTENT.CONFIRM_DELETE.TITLE"),
        this.$t("CHAPTER.CONTENT.CONFIRM_DELETE.DESCRIPTION"),
        () => {
          this.$store
            .dispatch("chapterTypeContentModule/deleteChapterContent", {
              chapterId: this.$route.params.id,
              contentId: content.id,
            })
            .then((res) => {
              const { error } = res;
              if (error) {
                this.$toast.error(
                    this.$t("CHAPTER.CONTENT.DELETE.FAILED") + ""
                );
              } else {
                this.$toast.success(
                    this.$t("CHAPTER.CONTENT.DELETE.SUCCESS") + ""
                );
                this.showChapterContentAlert();
              }
            });
        },

      );
    },
    showChapterContentAlert() {
      let chapterContentAlert = document.getElementById("chapter-content-alert");

      if(typeof(chapterContentAlert) != 'undefined' && chapterContentAlert != null && this.contents.length === 0) {
        return document.getElementById("chapter-content-alert").style.display = "block";
      }
    },
    increaseChapterContentPosition(content) {
      this.$store
        .dispatch(
          "chapterTypeContentModule/increaseChapterContentPosition",
          content.id
        )
        .then((res) => {});
    },

    decreaseChapterContentPosition(content) {
      this.$store
        .dispatch(
          "chapterTypeContentModule/decreaseChapterContentPosition",
          content.id
        )
        .then((res) => {});
    },
  },
};
</script>

 <style scoped lang="scss"> 
.ChapterTypeContent {
  width: 100%;
  background-color: #ffffff;
  //border: 1px solid #019cde;
  //padding: 1rem;
  .TypeContentForm {
    height: calc(100% - 2rem);
  }
}
</style>
