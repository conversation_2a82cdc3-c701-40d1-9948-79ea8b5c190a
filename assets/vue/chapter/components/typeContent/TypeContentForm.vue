<template>
  <div class="w-100 TypeContentFormContainer">
    <div
      class="col-12 d-flex align-items-center justify-content-center"
      v-if="loading"
    >
      <spinner />
    </div>
    <form
      id="form-type-content-form"
      class="TypeContentForm w-100"
      @submit.prevent="submit()"
      v-show="!loading"
    >
      <div class="form-group required w-100">
        <label for="title" class="form-label">{{ $t("CHAPTER.CONTENT.SECTION") }}</label>
        <input
          type="text"
          class="form-control"
          name="title"
          id="title"
          v-model="innerValue.title"
          required
        />
      </div>
      <div class="w-100 form-group">
        <label>{{ $t("CONTENT.CONFIGUREFIELD.CONTENT") }}</label>
        <froala
          :tag="`textarea`"
          v-model="innerValue.content"
          :config="froalaConfig"
        ></froala>
      </div>
      <div class="col-12 d-flex align-items-center justify-content-center p-1">
        <button type="submit" class="btn btn-primary">{{ $t("SAVE") }}</button>
      </div>
    </form>
  </div>
</template>

<script>
import Spinner from "../../../base/BaseSpinner.vue";

export default {
  name: "TypeContentForm",
  components: { Spinner },
  props: {
    value: {
      type: Object,
      default: () => ({
        title: "",
        content: "",
        position: 0,
      }),
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    innerValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit("input", newValue);
      },
    },
    froalaConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        //pluginsEnabled: ["align", "link", "url", "image", "lists"],
        height: 500,
      };
    },
  },
  methods: {
    submit() {
      const formData = new FormData(document.forms["form-type-content-form"]);
      if ("id" in this.innerValue) {
        formData.append("id", this.innerValue.id);
      }
      formData.append("content", this.innerValue.content);

      let chapterContentAlert = document.getElementById("chapter-content-alert");

      if(typeof(chapterContentAlert) != 'undefined' && chapterContentAlert != null && chapterContentAlert.style.display !== "none") {
        chapterContentAlert.style.display = "none";
      }

      this.$emit("submit", formData);
    },
  },
};
</script>

 <style scoped lang="scss"> 
:deep(.fr-sticky-on) {
  position: sticky;
}
</style>
