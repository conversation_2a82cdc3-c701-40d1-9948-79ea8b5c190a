<script>
import { sync, get } from "vuex-pathify";
import Spinner from "../../../base/BaseSpinner.vue";
import BaseSwitch from "../../../base/BaseSwitch.vue";

export default {
  name: "LdapRegister",
  components: {BaseSwitch, Spinner},
  data() {
    return {
      resultActiveTab: 'data',
      showGroupMappingValues: false
    };
  },
  computed: {
    groups: get('integrationMappingModule/groups'),

    connectionString: sync('ldapModule/configuration@connectionString'),
    baseDn: sync('ldapModule/configuration@baseDn'),
    adminUserBindDN: sync('ldapModule/configuration@adminUserBindDN'),
    adminUserPassword: sync('ldapModule/configuration@adminUserPassword'),
    searchDn: sync('ldapModule/configuration@searchDn'),
    searchQuery: sync('ldapModule/configuration@searchQuery'),
    mappingGroupId: sync('ldapModule/configuration@mappingGroupId'),

    userBaseDn: sync('ldapModule/configuration@userBaseDn'),
    userDn: sync('ldapModule/configuration@userDn'),

    userUsername: sync('ldapModule/userTest@username'),
    userPassword: sync('ldapModule/userTest@password'),

    // tests
    testing: get('ldapModule/testing'),
    testResults: sync('ldapModule/tests@testResults'),
  },
  watch: {
    baseDn: {
      immediate: true,
      handler: function () {
        if (this.userBaseDn == null || this.userBaseDn.length === 0) {
          this.userBaseDn = this.baseDn;
        }
      }
    }
  },

  methods: {
    testCron() {
      this.$store.dispatch('ldapModule/testCronConnection');
    },
    testUser() {
      this.$store.dispatch('ldapModule/testUserLogin');
    }
  }
}
</script>

<template>
  <div class="LdapRegister">
    <div class="col-12">
      <div class="w-100 d-flex flex-row flex-wrap">
        <h4 class="w-100">Configuracion base</h4>
        <div class="form-group col-xs-12 col-6">
          <label class="form-label">LDAP HOST (Connection String)</label>
          <input type="text" class="form-control" v-model="connectionString" placeholder="ldap://example.com:339">
        </div>
        <div class="form-group col-xs-12 col-6">
          <label class="form-label">BASE DN (Base DN for the directory)</label>
          <input type="text" class="form-control" v-model="baseDn" placeholder="dc=example,dc=com">
        </div>
      </div>

      <div class="w-100">
        <h4>Cron Configuracion</h4>
        <div class="col-12 d-flex flex-row flex-nowrap align-items-end gap-1">
          <div class="form-group mb-0 flex-grow-1">
            <label class="form-label">Seleccionar grupo de mapeo</label>
            <select class="form-select" v-model.number="mappingGroupId">
              <option v-for="group in groups" :key="group.id" :value="group.id">{{ group.name }}</option>
            </select>
          </div>
        </div>
        <div class="col-12 d-flex flex-row">
          <div class="form-group col-xs-12 col-6">
            <label class="form-label">Username (Read permissions) (cn=value)</label>
            <input type="text" class="form-control" v-model="adminUserBindDN" placeholder="cn=admin-read,dc=example,dc=com">
          </div>
          <div class="form-group col-xs-12 col-6">
            <label>Password</label>
            <input type="text" class="form-control" v-model="adminUserPassword">
          </div>
        </div>
        <div class="col-12">
          <div class="form-group col-12">
            <label>Search DN (If empty use base dn)</label>
            <input type="text" class="form-control" v-model="searchDn">
          </div>
          <div class="form-group col-12">
            <label>Search Query(Como obtener todos los usuarios: ejemplo: (objectclass=*))</label>
            <textarea class="form-control" v-model="searchQuery" placeholder="(objectclass=*)"></textarea>
          </div>
        </div>
      </div>

      <div class="w-100">
        <h4>Usuarios</h4>
        <div class="col-12 d-flex flex-row flex-wrap">
          <div class="form-group col-12">
            <label>User DN String</label>
            <div style="display: grid; grid-template-columns: 50px 10px 100px 1fr; gap: 5px;">
              <input type="text" class="form-control" v-model="userDn">
              <strong>=</strong>
              <span>{{ userUsername.length === 0 ? '&lt;username>&gt;' : userUsername }}</span>
              <input type="text" class="form-control" v-model="userBaseDn" placeholder="uid={user_identifier},dc=example,dc=com">
            </div>
          </div>
        </div>

        <div class="col-12">
          <h5 class="w-100">Usuario prueba (No se guarda)</h5>
          <div class="form-group col-xs-12 col-md-6">
            <label>Username</label>
            <input type="text" class="form-control" v-model="userUsername">
          </div>
          <div class="form-group col-xs-12 col-md-6">
            <label>Password</label>
            <input type="text" class="form-control" v-model="userPassword">
          </div>
        </div>
      </div>
    </div>

    <div class="col-12 Tests">
      <div class="col-12">
        <button type="button" class="btn btn-primary" @click="testCron()">Test Cron Connection</button>
        <button type="button" class="btn btn-primary" @click="testUser()">Test User Login</button>
      </div>
      <div class="Tests--result">
        <spinner v-if="testing"/>
<!--        <pre v-else>{{ JSON.stringify(testResults, null, 2) }}</pre>-->
        <div v-else>
          <ul class="nav nav-tabs ps-4">
            <li class="nav-item" role="presentation">
              <button
                  class="nav-link"
                  :class="resultActiveTab === 'data' ? 'active' : ''"
                  id="info-tab"
                  @click="resultActiveTab = 'data'"
              >
                <i class="fa fa-file"></i> Data
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                  class="nav-link"
                  :class="resultActiveTab === 'headers' ? 'active' : ''"
                  id="info-tab"
                  @click="resultActiveTab = 'headers'"
              >
                <i class="fa fa-file"></i> Headers
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                  class="nav-link"
                  :class="resultActiveTab === 'all' ? 'active' : ''"
                  id="info-tab"
                  @click="resultActiveTab = 'all'"
              >
                <i class="fa fa-file"></i> Info completa
              </button>
            </li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane active">
              <pre v-if="resultActiveTab === 'all'">{{ JSON.stringify(testResults, null, 2) }}</pre>
              <pre v-else>{{ JSON.stringify(testResults[resultActiveTab], null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.LdapRegister {
  .Tests {
    display: grid;
    align-items: flex-start;

    &--result {
      width: 100%;
      border: 1px dashed #212121;
      border-radius: 5px;
      padding: 10px;
      max-height: 215px;
      overflow-y: auto;
    }
  }
}
</style>
