<template>
  <div class="modalTutorProfile">
    <BaseModalInspector
      :identifier="tag + 'tutorProfile'"
      :title="$t('ANNOUNCEMENT.MODALS.TUTOR_PROFILE') || ''"
      padding="2rem"
      size="modal-md"
    >
      <div class="d-flex flex-column align-items-center">
        <div class="d-flex align-items-center">
          <img class="userAvatar" :src="tutor.avatar || ''" alt=" " />
        </div>
        <h3 class="font-weight-bold title font-weight-bold title my-3 line-break-anywhere">
          {{ tutor.fullName }}
        </h3>
      </div>
      <div class="container my-3">
        <div class="row">
          <div class="col-md-6 col-xs-12 font-weight-bold">
            <i class="fa fa-id-card"></i> DNI:
          </div>
          <div class="col-md-6 col-xs-12">{{ tutor.dni }}</div>
        </div>
        <div class="row mt-2 mb-2">
          <div class="col-md-6 col-xs-12 font-weight-bold">
            <i class="fa fa-envelope"></i> {{ $t('ANNOUNCEMENT.FORM.ENTITY.CONTACT_PERSON_EMAIL') }}:
          </div>
          <div class="col-md-6 col-xs-12">
            <a v-if="tutor.email && tutor.email !== '--'"
               :href="'mailto:' + tutor.email" class="link line-break-anywhere">{{ tutor.email }}</a>
            <span v-else>{{ tutor.email }}</span>
          </div>
        </div>
        <div class="row mt-2 mb-2">
          <div class="col-md-6 col-xs-12 font-weight-bold">
            <i class="fa fa-envelope"></i> {{ $t('ANNOUNCEMENT.TRAINING_CENTER_PHONE') }}:
          </div>
          <div class="col-md-6 col-xs-12">
            <a v-if="tutor.telephone && tutor.telephone !== '--'"
               :href="'tel:' + tutor.telephone"
               class="link line-break-anywhere">{{ tutor.telephone }}</a>
            <span v-else>{{ tutor.telephone }}</span>
          </div>
        </div>
        <div class="row mt-2 mb-2" v-show="tutor.cv">
          <div class="col-md-6 col-xs-12 font-weight-bold">
            <i class="fa fa-envelope"></i> {{ $t('ANNOUNCEMENT.FORM.GROUP.PROFESSIONAL_PROFILE') }}:
          </div>
          <div class="col-md-6 col-xs-12">
            <a :href="tutor.cv" :download="`CV ${tutor.fullName}${extension}`" target="_blank" class="link cursor-pointer">Ver/Imprimir</a>
          </div>
        </div>
        <div class="row mt-2 mb-2">
          <div class="col-md-6 col-xs-12 font-weight-bold">
            <i class="fa fa-clock"></i> {{ $t('ANNOUNCEMENT.FORM.GROUP.ENTITY.TUTORING_TIME') }}:
          </div>
          <div class="col-12 overflow-auto mt-2" v-html="tutor.tutoringTime"/>
        </div>
      </div>
    </BaseModalInspector>
  </div>
</template>

<script>
import {UtilsMixin}       from "../../announcement/mixins/utilsMixin";
import BaseModalInspector from "./BaseModalInspector";
import {get}              from "vuex-pathify";
export default {
  name: "modalTutorProfile",
  components: {BaseModalInspector},
  mixins: [UtilsMixin],
  props: {
    tag: { type: String, default: "" },
  },
  computed: {
    tutor: get('announcementModule/tutorSelected'),
    extension() {
      if (!(this.tutor?.cv || '').length) return '';
      const data = this.tutor.cv.split('.')
      return `.${data[data.length - 1]}`
    }
  }
};
</script>

 <style scoped lang="scss"> 
.modalTutorProfile {
  .userAvatar {
    width: 5rem;
    height: 5rem;
  }

  .container {
    padding: 1rem;
    border-radius: 7px;
    background-color: white;
  }

  .alertCounter {
    span {
      font-size: 0.8rem;
      vertical-align: super;
      font-weight: bold;
    }
  }

  ::v-deep {
    .modal-body {
      background-color: var(--color-neutral-lighter);
    }
  }
}
</style>
