<template>
  <div class="resumeTag d-table-row">
    <span class="question font-weight-bold d-table-cell" :class="'pr-' + padding">
      <i class="fa mr-2" :class="icon"></i>{{title}}
    </span>
    <span class="answer" :class="{clickable: clickable}" @click="emitClick">{{answer}}</span>
  </div>
</template>

<script>
export default {
  name: "resumeTag",
  props: {
    title: { type: String, default: '--' },
    answer: { type: String|Number, default: '--' },
    icon: { type: String, default: '' },
    clickable: { type: Boolean, default: false },
    padding: { type: Number, default: 2 }
  },
  methods: {
    emitClick() {
      if (this.clickable) this.$emit('clicked')
    }
  }
}
</script>

 <style scoped lang="scss"> 
.resumeTag {
  font-size: 1rem;

  i {
    width: 1.1rem;
    text-align: center;
  }

  .question {
    white-space: nowrap;
  }
  .answer {
    font-size: 0.9rem;
    color: var(--color-neutral-mid-darker);

    &.clickable {
      color: var(--color-primary);
      text-decoration: underline;
      cursor: pointer;
    }
  }
}
</style>
