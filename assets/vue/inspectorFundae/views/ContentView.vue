<template>
<div class="ContentView">
  <GroupContent
      v-for="(group, index) in groupStudents"
      :group="group"
      :group-number="index + 1"
      :opened="!index"
      :announcement-id="announcementId"
  />
  <ModalTutorProfile/>
</div>
</template>

<script>
import GroupContent      from "../components/groupContent";
import 'bootstrap'
import {get}             from "vuex-pathify";
import ModalTutorProfile from "../components/modalTutorProfile";
export default {
  name: "ContentView",
  components: {ModalTutorProfile, GroupContent},
  props: {
    announcementId: { type: Number, default: undefined },
  },
  computed: {
    groupStudents: get('announcementModule/calledUsers'),
  }
}
</script>

 <style scoped lang="scss"> 
.ContentView {

}
</style>
