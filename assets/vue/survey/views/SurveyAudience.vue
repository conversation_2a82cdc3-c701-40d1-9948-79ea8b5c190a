<template>
  <div class="SurveyAudience">
    <div class="SurveyAudience--selector">
      <button-with-description
        title="SURVEY.APPLY.COURSES.TITLE"
        description="SURVEY.APPLY.COURSES.DESCRIPTION"
        v-model="applyToCourses"
        name="applyToCourses"
        icon="fa fa-university"
      />
      <button-with-description
        title="SURVEY.APPLY.ANNOUNCEMENTS.TITLE"
        description="SURVEY.APPLY.ANNOUNCEMENTS.DESCRIPTION"
        v-model="applyToAnnouncements"
        name="applyToAnnouncements"
        icon="fa fa-calendar-alt"
      />
    </div>

    <add-remove
      v-if="applyToCourses"
      :source-items="srcCourses"
      v-model="courses"
      :name="manualSelected"
      :realtime="false"
      :enable-all="false"
      :loading-source="loadingSource"
      :loading-selected="false"
    />

    <add-remove
      v-if="applyToAnnouncements"
      :source-items="srcAnnouncements"
      v-model="announcements"
      :name="manualSelected"
      :realtime="false"
      :enable-all="false"
      :loading-source="loadingSource"
      :loading-selected="false"
    />
  </div>
</template>

<script>
import { get, sync } from "vuex-pathify";
import ButtonWithDescription from "../../common/components/ButtonWithDescription.vue";
import AddRemove from "../../common/components/select/AddRemove.vue";

export default {
  name: "SurveyAudience",
  components: { AddRemove, ButtonWithDescription },
  props: {
    realtime: {
      type: Boolean,
      default: false,
    },

    value: {
      type: Object | Array,
      default: () => [],
    },
  },
  data() {
    return {
      applyToAll: false,
      applyToCourses: false,
      applyToAnnouncements: false,
      manual: false,
      loadingSource: true,
      manualSelected: "course",
    };
  },
  computed: {
    innerValue: {
      get() {
        return this.value ?? {};
      },
      set(newValue) {
        this.$emit("input", newValue);
      },
    },
    srcCourses: get("surveyModule/manualData@courses"),
    srcAnnouncements: get("surveyModule/manualData@announcements"),
    courses: {
      get() {
        return this.innerValue.courses ?? [];
      },
      set(newValue) {
        const inner = structuredClone(this.innerValue);
        inner.courses = newValue;
        this.innerValue = inner;
      },
    },
    announcements: {
      get() {
        return this.innerValue.announcements ?? [];
      },
      set(newValue) {
        const inner = structuredClone(this.innerValue);
        inner.announcements = newValue;
        this.innerValue = inner;
      },
    },
  },
  watch: {
    applyToAll: {
      handler: function (newValue) {
        if (newValue) {
          this.applyToCourses = false;
          this.applyToAnnouncements = false;
          this.manual = false;
          this.updateContent(1);
          this.checkManualData();
        }
      },
      immediate: true,
    },
    applyToCourses: {
      handler: function (newValue) {
        if (newValue) {
          this.applyToAll = false;
          this.applyToAnnouncements = false;
          this.manual = false;
          this.updateContent(2);
          this.checkManualData();
        }
      },
      immediate: true,
    },
    applyToAnnouncements: {
      handler: function (val) {
        if (val) {
          this.applyToAll = false;
          this.applyToCourses = false;
          this.manual = false;
          this.updateContent(3);
          this.checkManualData();
        }
      },
      immediate: true,
    },
    manual: {
      handler: function (newValue) {
        if (newValue) {
          this.applyToAll = false;
          this.applyToCourses = false;
          this.applyToAnnouncements = false;
          this.updateContent(4);
          this.checkManualData();
        }
      },
      immediate: true,
    },
    innerValue: {
      handler: function (val) {
        switch (parseInt(this.innerValue.applyTo)) {
          case 1:
            this.applyToAll = true;
            break;
          case 2:
            this.applyToCourses = true;
            break;
          case 3:
            this.applyToAnnouncements = true;
            break;
          case 4:
            this.manual = true;
            break;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.courses = this.value?.courses ?? [];
    this.announcements = this.value?.announcements ?? [];
  },
  methods: {
    updateContent(applyTo) {
      const inner = structuredClone(this.innerValue);
      inner.applyTo = applyTo;
      this.innerValue = inner;
    },
    async checkManualData() {
      this.loadingSource = true;
      try {
        if (this.srcCourses.length === 0 || this.srcAnnouncements.length === 0)
          await this.$store.dispatch("surveyModule/getManualData");
      } finally {
        this.loadingSource = false;
      }
    },
  },
};
</script>

 <style scoped lang="scss"> 
.SurveyAudience {
  &--selector {
    width: 100%;
    display: flex;
    flex-flow: row wrap;

    .ButtonWithDescription {
      width: 100%;
    }

    @media #{min-medium-screen()} {
      display: grid;
      grid-template-columns: repeat(auto-fit, 50%);
    }

    @media #{min-large-screen()} {
      display: grid;
      grid-template-columns: repeat(auto-fit, 25%);
    }
  }
}
</style>
