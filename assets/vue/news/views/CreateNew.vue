<template>
  <div class="d-flex align-items-center justify-content-center" v-if="loading">
    <spinner />
  </div>
  <form @submit.prevent="submit()" class="CreateNew" v-else>
    <ul class="nav nav-tabs">
      <li class="nav-item" role="presentation" v-for="(item, locale) in news" :key="locale">
        <button
            type="button"
            class="nav-link"
            :class="activePane === locale ? 'active' : ''"
            :id="`${locale}-tab`"
            @click="activePane = locale"
        >
          <i class="fa fa-comments"></i> {{ locale }}
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button
            type="button"
            class="nav-link"
            id="audience-tab"
            @click="addNewLocale()"
        >
          <i class="fa fa-plus"></i> {{ $t('ADD_LOCALE') }}
        </button>
      </li>
    </ul>

    <div class="tab-content">
      <div class="tab-pane fade"
           v-for="(item, locale) in news" :key="locale"
           :class="activePane ===  locale ? 'active show' : ''"
           :id="locale"
           role="tabpanel"
           :aria-labelledby="`${locale}-tab`"
      >
        <add-new v-model="news[locale]" @input="updateModel(locale, $event)" >
          <template v-slot:file-selector>
            <FileSelector :name="`file-selector-${locale}`" v-model="thumbnail"
                          :use-model="true"
                          :preview-default-class="`full-size ${thumbnail === null && imageSrc && imageSrc.length > 0 ? 'has-image' : ''}`"
                          :default-image="thumbnail != null || imageSrc == null || imageSrc.length === 0 ? '/assets/common/add_image_file.svg' : `${filePath}/${imageSrc}`"
            />
          </template>
        </add-new>
      </div>
    </div>
  </form>
</template>

<script>
import {get} from "vuex-pathify";
import Spinner from "../../admin/components/base/Spinner.vue";
import AddNew from "../components/AddNew.vue";
import FileSelector from "../../common/components/FileSelector.vue";

export default {
  name: "CreateNew",
  components: {FileSelector, AddNew, Spinner},
  data() {
    return {
      thumbnail: null,
      news: {},
      loading: true,
      activePane: null,
      imageSrc: null
    };
  },
  computed: {
    locales: get('localeModule/locales'),
    defaultLocale: get('localeModule/defaultLocale'),
    filePath: get('configModule/config@filePath'),
  },
  async created() {
    this.loading = true;
    const isUpdate = this.$route.name === 'UpdateNew';

    await this.$store.dispatch('contentTitleModule/setActions', { route: this.$route.name, actions: [
        {
          name: this.$t('SAVE'),
          event: 'onSave',
          class: 'btn btn-primary'
        }
      ]});

    await this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: isUpdate ? this.$t('NEWS.UPDATE_NEW') : this.$t('NEWS.CREATE_NEW'),
        params: {}
      }
    });

    try {
      this.activePane = this.defaultLocale;
      if (isUpdate) {
        // load data
        const result = await this.$store.dispatch('newsModule/getNew', this.$route.params.id);
        const { data, error } = result;
        this.imageSrc = data.image;
        this.news = data.data;
      } else {
        this.news[this.defaultLocale] = {
          title: '',
          description: ''
        };
      }
    } finally {
      this.loading = false;
    }
  },
  mounted() {
    this.$eventBus.$on('onSave', e => {
      this.submit();
    });
  },
  beforeDestroy() {
    this.$eventBus.$off('onSave');
  },
  methods: {
    addNewLocale() {
      const locales = Object.keys(this.locales);
      const used = Object.keys(this.news);
      const available = locales.filter(locale => {
        const index = used.findIndex(u => u === locale);
        return index < 0;
      });
      if (available.length > 0) {
        const values = structuredClone(this.news);
        values[available[0]] = {
          thumbnail: this.news[used[0]].thumbnail,
          title: '',
          description: ''
        };
        this.news = values;
      } else {
        this.$toast.warning(this.$t('NO_AVAILABLE_LOCALES') + '');
      }
    },
    updateModel(locale, data) {
      const values = structuredClone(this.news);
      values[locale] = data;
      if (data.thumbnail) {
        const keys = Object.keys(values);
        keys.forEach(key => {
          values[key].thumbnail = data.thumbnail;
        });
      }
      this.news = values;
    },
    submit() {
      const formData = new FormData();
      if (this.thumbnail instanceof FileList && this.thumbnail.length > 0) {
        formData.append('thumbnail', this.thumbnail[0]);
      }
      formData.append('data', JSON.stringify(this.news));

      const isUpdate = this.$route.name === 'UpdateNew';

      this.$alertify.confirmWithTitle(
          this.$t('NEWS.SAVE.CONFIRM.TITLE'),
          this.$t('NEWS.SAVE.CONFIRM.DESCRIPTION'),
          () => {
            this.saveNews(formData, isUpdate)
          },
          () => {},
      )
    },

    saveNews(formData, isUpdate = false) {
      const self = this;
      function save() {
        if (isUpdate) return self.$store.dispatch('newsModule/updateNews', { id: self.$route.params.id, formData});
        else return self.$store.dispatch('newsModule/saveNews', formData);
      }

      this.$store.dispatch('loaderModule/setLoading', { loading: true, message: this.$t('SAVING')});
      save().then(res => {
        const { error } = res;
        if (error) this.$toast.error(this.$t('NEWS.SAVE.FAILED') + '')
        else {
          this.$toast.success(this.$t('NEWS.SAVE.SUCCESS') + '');
          this.$store.dispatch('routerModule/setDeleteLastRoute', true);
          this.$router.replace({ name: 'Home' })
        }
      }).finally(() => {
        this.$store.dispatch('loaderModule/setLoading', { loading: false});
      });
    }
  }
}
</script>

 <style scoped lang="scss"> 
.CreateNew {
  @include nav-bar-style;
}
</style>
