<template>
  <div class="home">
    <home
      title="CATALOG.HOME.TITLE"
      description="CATALOG.HOME.DESCRIPTION"
      src-thumbnail="/assets/imgs/survey_app.svg"
    >
      <template v-slot:content-main>
        <div>       
          <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation" aria-selected="true">
              <button
                class="nav-link active"
                id="tablePlataform-tab"
                data-bs-toggle="tab"
                data-bs-target="#tablePlataform"
                type="button"
                role="tab"
                aria-controls="tablePlataform"
                aria-selected="true"
              >
                Plataforma
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                id="tableConvocatoria-tab"
                data-bs-toggle="tab"
                data-bs-target="#tableConvocatoria"
                type="button"
                role="tab"
                aria-controls="tableConvocatoria"
                aria-selected="false"
              >
                Convocatorias
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button
                class="nav-link"
                id="tableFundae-tab"
                data-bs-toggle="tab"
                data-bs-target="#tableFundae"
                type="button"
                role="tab"
                aria-controls="tableFundae"
                aria-selected="false"
              >
                Fundae
              </button>
            </li>
          </ul>
          <div class="tab-content" id="myTabContent">
            <div
              class="tab-pane fade show active"
              id="tablePlataform"
              role="tabpanel"
              aria-labelledby="tablePlataform-tab"
            >
              <table class="table">
                <thead>
                  <tr>
                    <th
                      scope="col"
                      @click="sortByColumn('name')"
                      :class="{
                        'sort-asc': sortBy === 'name' && !sortDesc,
                        'sort-desc': sortBy === 'name' && sortDesc,
                      }"
                    >
                      {{ $t("NAME") }}
                    </th>
                    <th scope="col"></th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="catalog in sortedTablasPlataform"
                    :key="catalog.id"
                  >
                    <td>{{ catalog.name }}</td>
                    <td class="text-right">
                      <button
                        class="btn btn-primary btn-sm"
                        @click="goToCatalog(catalog)"
                      >
                        <i class="fas fa-eye"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div
              class="tab-pane fade"
              id="tableConvocatoria"
              role="tabpanel"
              aria-labelledby="tableConvocatoria-tab"
            >
              <table class="table">
                <thead>
                  <tr>
                    <th
                      scope="col"
                      @click="sortByColumn('name')"
                      :class="{
                        'sort-asc': sortBy === 'name' && !sortDesc,
                        'sort-desc': sortBy === 'name' && sortDesc,
                      }"
                    >
                      {{ $t("NAME") }}
                    </th>
                    <th scope="col"></th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="catalog in sortedTablasConvocatoria"
                    :key="catalog.id"
                  >
                    <td>{{ catalog.name }}</td>
                    <td class="text-right">
                      <button
                        class="btn btn-primary btn-sm"
                        @click="goToCatalog(catalog)"
                      >
                        <i class="fas fa-eye"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div
              class="tab-pane fade"
              id="tableFundae"
              role="tabpanel"
              aria-labelledby="tableFundae-tab"
            >
              <table class="table">
                <thead>
                  <tr>
                    <th
                      scope="col"
                      @click="sortByColumn('name')"
                      :class="{
                        'sort-asc': sortBy === 'name' && !sortDesc,
                        'sort-desc': sortBy === 'name' && sortDesc,
                      }"
                    >
                      {{ $t("NAME") }}
                    </th>
                    <th scope="col"></th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="catalog in sortedTablasFundae" :key="catalog.id">
                    <td>{{ catalog.name }}</td>
                    <td class="text-right">
                      <button
                        class="btn btn-primary btn-sm"
                        @click="goToCatalog(catalog)"
                      >
                        <i class="fas fa-eye"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </template>
    </home>
  </div>
</template>

<script>
const RELATION_PLATAFORM = "PLATFORM";
const RELATION_ANNOUNCEMENT = "ANNOUNCEMENT";
const RELATION_FUNDAE = "FUNDAE";

import { get } from "vuex-pathify";

import Home from "../../base/Home.vue";
import Spinner from "../../admin/components/base/Spinner.vue";

export default {
  name: "Home",
  components: { home: Home, Spinner },
  data() {
    return {
      sortBy: "name",
      sortDesc: false,
      tablasFundae: [],
      tablasPlataform: [],
      tablasConvocatoria: [],
      general: [],
    };
  },

  computed: {
    ...get("catalogModule", ["getCatalogsFromBd"]),

    catalogsFromBd(){
      return this.getCatalogsFromBd();
    },

    sortedTablasFundae() {
      if (this.sortBy) {
        return this.tablasFundae.slice().sort((a, b) => {
          const fieldA = a[this.sortBy];
          const fieldB = b[this.sortBy];
          return this.sortDesc
            ? fieldB.localeCompare(fieldA)
            : fieldA.localeCompare(fieldB);
        });
      } else {
        return this.catalogs;
      }
    },
    sortedTablasPlataform() {
      if (this.sortBy) {
        return this.tablasPlataform.slice().sort((a, b) => {
          const fieldA = a[this.sortBy];
          const fieldB = b[this.sortBy];
          return this.sortDesc
            ? fieldB.localeCompare(fieldA)
            : fieldA.localeCompare(fieldB);
        });
      } else {
        return this.catalogs;
      }
    },
    sortedTablasConvocatoria() {
      if (this.sortBy) {
        return this.tablasConvocatoria.slice().sort((a, b) => {
          const fieldA = a[this.sortBy];
          const fieldB = b[this.sortBy];
          return this.sortDesc
            ? fieldB.localeCompare(fieldA)
            : fieldA.localeCompare(fieldB);
        });
      } else {
        return this.catalogs;
      }
    },
  },

  async created() {
    await this.$store.dispatch(
      "catalogModule/loadCatalogsFromBd",
      "/admin/catalog/all"
    );

    await this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: "Catálogos",
        params: this.$route.params,
      },
    });

   
    this.catalogs = this.catalogsFromBd;    

    this.catalogs.forEach((item) => {
      if (item.relation === RELATION_PLATAFORM) this.tablasPlataform.push(item);
      else this.general.push(item);
    });

    this.catalogs.forEach((item) => {
      if (item.relation === RELATION_ANNOUNCEMENT) this.tablasConvocatoria.push(item);
      else this.general.push(item);
    });

    this.catalogs.forEach((item) => {
      if (item.relation === RELATION_FUNDAE) this.tablasFundae.push(item);
      else this.general.push(item);
    });
  },

  methods: {
    sortByColumn(column) {
      if (this.sortBy === column) {
        this.sortDesc = !this.sortDesc;
      } else {
        this.sortBy = column;
        this.sortDesc = false;
      }
    },

    goToCatalog(catalog) {
          this.$router.push({
        name: catalog?.routeName,
        params: { catalog: catalog?.id },
      });
    },
  },

  mounted() {},
};
</script>

 <style scoped lang="scss"> 
.home {
  .sort-asc::after,
  .sort-desc::after {
    content: " ▼";
    font-size: 10px;
    opacity: 0.5;
    margin-left: 5px;
  }

  .sort-desc::after {
    content: " ▲";
  }

  th {
    cursor: pointer;
  }

  th.sort-asc:hover::after,
  th.sort-desc:hover::after {
    opacity: 1;
    cursor: pointer;
  }
}
</style>
