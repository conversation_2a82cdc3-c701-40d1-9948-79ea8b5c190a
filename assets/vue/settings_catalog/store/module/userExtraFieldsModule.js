import { make } from "vuex-pathify";
import axios from "axios";

const state = {
  loading: true,
  userExtraFields: [],
};


export const getters = {
    ...make.getters(state),
  getUserExtraFields: (state) => () => state.userExtraFields,
};

export const mutations = {
  ...make.mutations(state),
};

export const actions = {
    ...make.actions(state),
    async load({ commit }, endpoint) {
      commit("SET_LOADING", true);
      return await axios
        .get(endpoint)
        .then((r) => {
          const { data } = r.data;
  
          commit("SET_USER_EXTRA_FIELDS", data);
  
          return r.data;
        })
        .catch((e) => ({
          error: true,
          data: e,
        }))
        .finally(() => {
          commit("SET_LOADING", false);
        });
    },

    async changeSave({ getters }) {
      const { userExtraFields } = getters;
      const result = await axios.put(`/admin/userExtraFields/update`, { userExtraFields });
    },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};