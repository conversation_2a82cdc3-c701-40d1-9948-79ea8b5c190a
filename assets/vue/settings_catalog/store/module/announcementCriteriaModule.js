import {make} from "vuex-pathify";
import axios from "axios";

const state = {
    loading: true,
    catalogs: []
};

const mutations = {
    ...make.mutations(state)
};

export const getters = {
    ...make.getters(state)
};

export const actions = {
    ...make.actions(state),
    getCatalogs({ commit }) {
        commit('SET_LOADING', true);
        axios.get('/admin/announcement-criteria/all').then(r => {
            const { data } = r.data;
            commit('SET_CATALOGS', data);
        }).finally(() => {
            commit('SET_LOADING', false);
        })
    },
    async changeActiveStatus({ getters, commit }, { id, active = true }) {
        const result = await axios.post(`/admin/announcement-criteria/${id}/active`, { active });
        const { error } = r.data;
        if (!error) {
            const { catalogs } = getters;
            const index = catalogs.findIndex(c => c.id === id);
            if (index >= 0) {
                catalogs[index].active = active;
                commit('SET_CATALOGS', catalogs);
            }
        }
        return { error };
    },

    async createAnnouncementCriteria({}, data) {
        return await axios.post('/admin/announcement-criteria/create', data)
            .then(r => (r.data))
            .catch(e => ({
                error: true,
                data: e
            }));
    },

    async updateAnnouncementCriteria({}, data) {
        return await axios.post('/admin/announcement-criteria/update', data)
            .then(r => (r.data))
            .catch(e => ({
                error: true,
                data: e
            }));
    }
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
}
