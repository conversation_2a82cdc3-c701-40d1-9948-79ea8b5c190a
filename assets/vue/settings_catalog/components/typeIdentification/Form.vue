<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";

export default {
  name: "Form",
  components: {BaseSwitch, BaseForm},
  data() {
    return {
      locale: 'es',
      typeIdentification: {
        id: -1,
        name: '',
        description: '',
        active: true,
        main: false,
        mask: '',
        translations: []
      },
    };
  },
  computed: {
    catalogs: get('catalogModule/catalogs'),
    locales: get('localeModule/locales'),
  },
  created() {
    if (this.catalogs.length === 0) {
      this.returnToList();
      return;
    }

    let typeIdentification = {
      id: -1,
      name: '',
      description: '',
      active: true,
      main: false,
      mask: '',
      translations: []
    };
    
    if (this.$route.name === 'TypeIdentificationUpdate') {
      typeIdentification = this.catalogs.find(c => c.id === this.$route.params.id);

      if (typeIdentification === undefined) {
        this.returnToList();
        return;
      }
    }

    let translations = [];

    const keys = Object.keys(this.locales);
    keys.forEach((k) => {
      const translated = typeIdentification.translations.find(e => e.locale === k);
      translations.push({
        locale: k,
        name: translated?.name ?? '',
        description: translated?.description ?? ''
      })
    });

    typeIdentification.translations = translations;

    this.typeIdentification = typeIdentification;
  },
  methods: {
    returnToList() {
      this.$router.push({name: 'TypeIdentification', params: this.$route.params});
    },
    submit() {
      const update = this.$route.name === 'TypeIdentificationUpdate';
      const endpoint = update ? '/admin/type-identification/update' : '/admin/type-identification/create';

      const save = () => {
        return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: this.typeIdentification });
      }

      save().then(r => {
        const { error, data } = r;
        if (error) this.$toast.error(data);
        else {
          this.$toast.success(this.$t('CATALOG.SAVED') + '');
          this.returnToList();
        }
      })
    }
  }
}
</script>

<template>
  <base-form v-model="locale" @cancel="returnToList()" @submit="submit()">
    <template v-slot:form>
      <div class="form-group col-12">
        <label>{{ $t('NAME') }}</label>
        <input type="text" class="form-control" v-model="typeIdentification.name">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('DESCRIPTION') }}</label>
        <textarea class="form-control" v-model="typeIdentification.description" rows="5"></textarea>
      </div>

      <div class="form-group col-12 d-flex align-items-center justify-content-start">
        <BaseSwitch :tag="`switcher-type-identificaction-form-active-${typeIdentification.id}`"
                    v-model="typeIdentification.active"/>
        <label class="ml-1">{{ $t('ACTIVE') }}</label>
      </div>

      <div class="form-group col-12">
        <label>{{ $t('CATALOGO.TYPE_IDENTIFICATION.MASK') }}</label>
        <input type="text" class="form-control" v-model="typeIdentification.mask">
      </div>

      <div class="form-group col-12 d-flex align-items-center justify-content-start">    
        <BaseSwitch :tag="`switcher-type-identification-main-${typeIdentification.id}`" 
                    v-model="typeIdentification.main" :disabled="typeIdentification.main == 1"/>           
        <label class="ml-1">{{ $t('CATALOGO.TYPE_IDENTIFICATION.MAIN') }}</label>
      </div>
    </template>
    <template v-slot:translations>
      <div v-for="t in typeIdentification.translations" :key="t.locale" v-if="t.locale === locale">
        <div class="form-group col-12">
          <label>{{ $t('NAME') }}</label>
          <input type="text" class="form-control" v-model="t.name">
        </div>

        <div class="form-group col-12">
          <label>{{ $t('DESCRIPTION') }}</label>
          <textarea class="form-control" v-model="t.description" rows="5"></textarea>
        </div>
      </div>
    </template>
  </base-form>
</template>

<style scoped lang="scss">

</style>
