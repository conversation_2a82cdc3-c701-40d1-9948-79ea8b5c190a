<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";

export default {
  name: "Form",
  components: {BaseForm},
  data() {
    return {
      locale: 'es',
      userWorkDepartment: {
        id: -1,
        name: '',
        description: '',
        state: true,
      },
    };
  },
  computed: {
    catalogs: get('catalogModule/catalogs'),
    locales: get('localeModule/locales'),
  },
  created() {
    // if (this.catalogs.length === 0) {
    //   this.returnToList();
    //   return;
    // }

    let userWorkDepartment = {
        id: -1,
        name: '',
        description: '',
        state: true, 
      };

    if (this.$route.name === 'UserWorkDepartmentUpdate') {
      userWorkDepartment = this.catalogs.find(c => c.id === this.$route.params.id);

      if (userWorkDepartment === undefined) {
        this.returnToList();
        return;
      }
    }

    this.userWorkDepartment = userWorkDepartment;
  },
  methods: {
    returnToList() {
      this.$router.push({name: 'UserWorkDepartment', params: this.$route.params});
    },
    submit() {
      const update = this.$route.name === 'UserWorkDepartmentUpdate';
      const endpoint = update ? '/admin/userWorkDepartment/update' : '/admin/userWorkDepartment/create';
      const save = () => {
        return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: this.userWorkDepartment });
      }

      save().then(r => {
        const { error, data } = r;
        if (error) this.$toast.error(data);
        else {
          this.$toast.success(this.$t('CATALOG.SAVED') + '');
          this.returnToList();
        }
      })
    }
  }
}
</script>

<template>
  <base-form v-model="locale" @cancel="returnToList()" @submit="submit()">
    <template v-slot:form>

      <div class="form-group col-12">
        <label>{{ $t('NAME') }}</label>
        <input type="text" class="form-control" v-model="userWorkDepartment.name">
      </div>

      <div class="form-group col-12">
        <label>{{ $t('DESCRIPTION') }}</label>      
        <textarea
            type="text"
            class="form-control"
            name="description"
            v-model="userWorkDepartment.description"
            rows="5"
          />
      </div>

      <div class="form-group col-12">
        <label>{{ $t('STATE') }}</label>
        <BaseSwitch v-model="userWorkDepartment.state" />
      </div>

    </template>
  </base-form>
</template>

<style scoped lang="scss">

</style>
