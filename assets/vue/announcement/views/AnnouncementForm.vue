<script>
import { get, sync } from "vuex-pathify";
import Spinner from "../../admin/components/base/Spinner.vue";
import StepForm from "../../common/views/StepForm.vue";
import AnnouncementAlerts from "../components/announcementForm/AnnouncementAlerts.vue";
import AnnouncementBonus from "../components/announcementForm/AnnouncementBonus.vue";
import AnnouncementCertificate from "../components/announcementForm/AnnouncementCertificate.vue";
import AnnouncementCommunication from "../components/announcementForm/AnnouncementCommunication.vue";
import AnnouncementCourse from "../components/announcementForm/AnnouncementCourse.vue";
import AnnouncementGeneralInfo from "../components/announcementForm/AnnouncementGeneralInfo.vue";
import AnnouncementGeneralInfoExtern from "../components/announcementForm/AnnouncementGeneralInfoExtern.vue";
import AnnouncementGroups from "../components/announcementForm/AnnouncementGroups.vue";
import AnnouncementPlace from "../components/announcementForm/AnnouncementPlace.vue";
import AnnouncementStudents from "../components/announcementForm/AnnouncementStudents.vue";
import AnnouncementSurvey from "../components/announcementForm/AnnouncementSurvey.vue";
import Loader from "../../admin/components/Loader.vue";

export default {
  name: "AnnouncementForm",
  components: {
    Loader,
    AnnouncementAlerts,
    AnnouncementSurvey,
    AnnouncementCertificate,
    AnnouncementCommunication,
    AnnouncementBonus,
    AnnouncementPlace,
    AnnouncementGroups,
    AnnouncementStudents,
    AnnouncementGeneralInfo,
    AnnouncementGeneralInfoExtern,
    AnnouncementCourse,
    StepForm,
    Spinner,
  },

  data() {
    return {
      warningSteps: {},
      displayErrors: []
    };
  },

  computed: {
    loading: get("announcementFormModule/loading"),
    announcement: sync("announcementFormModule/announcement"), // Main announcement object
    totalSteps: sync("announcementFormModule/steps@total"),
    currentStep: sync("announcementFormModule/steps@current"),
    errors: sync("announcementFormModule/errors"),
    type: get("announcementFormModule/announcement@type"),
    stepsConfigurations: get("announcementFormModule/stepsConfigurations"),
    stepsInformationValidation: sync("announcementFormModule/stepsInformation"),
    stepsInformation() {
      const steps = this.stepsConfigurations[this.type] ?? {
        titles: new Map(),
        steps: []
      };
      this.stepsInformationValidation = steps;
      this.totalSteps = steps.steps.length;
      return steps;
    },
    saving: get("announcementFormModule/saving"),
    course: get("announcementFormModule/announcement@course"),
    isConfigurable: get("announcementFormModule/isConfigurable"),
  },

  watch: {
    errors: {
      deep: true,
      immediate: true,
      handler: function () {
        this.checkErrorStatus();
      },
    },
    // type: {
    //   immediate: true,
    //   handler: function () {
    //     this.$store.dispatch("announcementFormModule/calculateSteps");
    //   },
    // },

    currentStep() {
      this.checkErrorStatus();
    }
  },

  async created() {
    const isUpdate = this.$route.name === 'UpdateAnnouncement';
    await this.$store.dispatch('announcementFormModule/setUpdate', isUpdate);
    let enableCourseFinder = !isUpdate;

    // Called the create form from other section
    const params = this.$route.params;
    if (params.origin) {
      await this.$store.dispatch('contentTitleModule/addRoute', {
        routeName: 'Home',
        params: {
          linkName: this.$t('ANNOUNCEMENTS'),
          params: {}
        }
      });
      if (params.origin === 'course') {
        enableCourseFinder = false;
        await this.$store.dispatch('announcementFormModule/loadPreSelectedCourse', params.id);
      }
    }
    await this.$store.dispatch('announcementFormModule/setEnableCourseFinder', enableCourseFinder);

    //Title
    await this.$store.dispatch('contentTitleModule/addRoute', {
      routeName: this.$route.name,
      params: {
        linkName: isUpdate ? this.$t('ANNOUNCEMENT.UPDATE') : this.$t('ANNOUNCEMENT.CREATE'),
        params: this.$route.params
      }
    });

    this.loadPreData(isUpdate);
  },

  methods: {
    cleanErrors() {
      this.$store.dispatch('')
    },
    checkErrorStatus() {
      this.displayErrors = [];
      const errors = structuredClone(this.errors);
      const keys = Object.keys(errors);

      if (keys.length === 0) return;
      const warningSteps = {};
      keys.forEach(key => {
        const stepInfo = this.stepsInformation.steps.find(step => step.type === key);
        const error = errors[key];

        if (stepInfo && stepInfo.step === this.currentStep && error.error && error.message && error.message.length > 0) {
          this.$toast.clear();
          if (error.i18n) this.$toast.error(this.$t(error.message) + "");
          else this.$toast.error(error.message);
          delete errors[key];
          this.errors = errors;
        }

        if (error.data) {
          if ('request' in error) {
            if (typeof error.data === 'string' || error.data instanceof String) this.displayErrors = [error.data];
            else {
              const listOfE = [];
              error.data.forEach(e => {
                listOfE.push(this.$t(e) + '');
              })
              this.displayErrors = listOfE;
            }
          }
          else {
            if ('i18n' in error.data) {
              const values = [];
              error.data.i18n.forEach(e => {
                values.push(this.$t(e) + '');
              });
              this.displayErrors = values;

              delete error.data.i18n;
            }

            const ids = Object.keys(error.data);
            ids.forEach(id => {
              const element = document.getElementById(id);
              if (element) {
                if (error.data[id]) element.classList.add('warning');
                else element.classList.remove('warning');
              }
            });
          }
        }

        warningSteps[stepInfo.step] = error.error;
      });

      this.warningSteps = warningSteps;
    },
    async next() {
      await this.$store.dispatch("announcementFormModule/nextStep");
    },
    async prev() {
      await this.$store.dispatch("announcementFormModule/prevStep");
    },
    async submit() {
      const isSuccess = await this.$store.dispatch('announcementFormModule/submitForm');
      if (isSuccess) {
        await this.$store.dispatch('contentTitleModule/removeRouteFromContentTitle', this.$route.name);
        await this.$store.dispatch('routerModule/setDeleteLastRoute', true);
        await this.$store.dispatch('announcementModule/reset');
        this.$router.replace({ name: 'ViewAnnouncement', params: { id: this.announcement.id } });
      }
    },
    async onStepChange(step) {
      await this.$store.dispatch("announcementFormModule/onStepChange", step);
    },

    loadPreData(isUpdate = false) {
      const source = this.$route.params['source'] ?? 'intern';

      this.$store.dispatch("announcementFormModule/loadPreData", {id: isUpdate ? this.$route.params.id : null, source});
    },
  },
};
</script>

<template>
  <div class="d-flex align-items-center justify-content-center" v-if="loading">
    <spinner />
  </div>
  <div class="AnnouncementForm" v-else>
    <step-form
      id="form-announcement"
      :number-of-steps="totalSteps"
      :current-step="currentStep"
      header-prefix="ANNOUNCEMENT"
      :steps-custom-titles="stepsInformation.titles"
      @next="next()"
      @prev="prev()"
      @on-step-change="onStepChange"
      :warning="warningSteps"
      btn-save-text="COMPLETE"
      @submit="submit()"
    >
      <template v-slot:form-content>
        <div>
          <div class="col-12 d-flex align-content-center justify-content-center" v-if="saving">
            <loader :is-loaded="true" style="padding: 0 !important;"/>
          </div>
          <div v-if="!isConfigurable" class="col-12 form--error">
            {{ $t('ANNOUNCEMENT.WARNING.ANNOUNCEMENT_NOT_CONFIGURABLE', [$t('ANNOUNCEMENT.STATUS.' + announcement.status )]) }}
          </div>
          <div class="col-12 form--error" v-if="displayErrors.length > 0">
            <i class="close" @click="displayErrors = [];">&times;</i>
            <h4 v-for="(e, index) in displayErrors" :key="index">{{ e }}</h4>
          </div>
          <div
              :class="`StepForm--${step.type}`"
              v-for="step in stepsInformation.steps"
              :key="step.step"
              v-show="currentStep === step.step"
          >
            <component :is="step.type"/>
          </div>
        </div>
      </template>
    </step-form>
  </div>
</template>

<style scoped lang="scss">
:deep(.form-group) {
  margin-bottom: 0;
  label {
    color: var(--color-neutral-darkest);
    font-size: 18px;
  }
}

:deep(textarea) {
  resize: none;
}

:deep(.modal-header) {
  background-color: #32383E;
  color: #ffffff;
  * {
    color: #ffffff;
  }
}

.form--error {
  border: 1px solid var(--color-secondary);
  background-color: var(--color-secondary-lightest);
  color: var(--color-neutral-darkest);
  padding: 1rem;
  text-align: center;
  position: relative;

  h4 {
    font-size: 16px;
  }

  .close {
    position: absolute;
    right: 5px;
    top: 0;
    cursor: pointer;
  }
}

:deep(.form-group.warning) {
  label {
    position: relative;
    &:before {
      content: '!';
      position: absolute;
      top: 0;
      left: -.5rem;
      color: var(--color-secondary-light);
    }
  }
  input.form-control {
    border-color: var(--color-secondary-light);
    background-color: var(--color-secondary-lightest);
  }
}

.form-group {

  &.info-icon {
    label {
      position: relative;
      i {
        margin-left: .5rem;
        color: var(--color-primary);
        margin-right: 1rem;
        font-size: 18px !important;
      }
    }
  }

  i.info-icon {
    margin-left: .5rem;
    color: var(--color-primary);
    margin-right: 1rem;
    font-size: 18px !important;
  }
}

div.errors {
  border: 1px solid var(--color-secondary);
  background-color: var(--color-secondary-lightest);
  color: var(--color-neutral-darkest);
  padding: 1rem;
  text-align: center;
}
</style>
