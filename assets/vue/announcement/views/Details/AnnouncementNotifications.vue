<template>
  <div class="AnnouncementNotifications">
    <div
      class="w-100 d-flex align-items-center justify-content-center flex-column"
      v-if="loading"
    >
      <spinner />
    </div>
    <div v-show="!loading">
      <div class="text-right mt-3 mb-3">
        <button
          class="btn btn-primary"
          @click="newNotification"
          data-bs-toggle="modal"
          data-bs-target="#notifications"
        >
          <i class="fa fa-clock mr-2"></i
          >{{ $t("ANNOUNCEMENT.NOTIFICATIONTAB.NEW") }}
        </button>
      </div>
      <div class="w-100 mb-3 pb-3">
        <DataNotFound
          :hide-on="!notificationList.length"
          :text="$t('ANNOUNCEMENT.NOTIFICATIONTAB.NOT_FOUND') || ''"
          icon="fa-bell"
          :banner="true"
        />
        <table
          class="table table-condensed w-100"
          v-show="notificationList.length"
        >
          <thead>
            <tr>
              <th class="font-weight-bold">
                {{ $t("ANNOUNCEMENT.FORM.ENTITY.START_AT") }}
              </th>
              <th class="font-weight-bold">{{ $t("HOUR_TEXT") }}</th>
              <th class="font-weight-bold" style="min-width: 250px">
                {{ $t("TEXT") }}
              </th>
              <th class="font-weight-bold text-center">
                {{ $t("ANNOUNCEMENT.GROUP") }}
              </th>
              <th class="font-weight-bold text-center">{{ $t("STATUS") }}</th>
              <th class="font-weight-bold text-center">{{ $t("ACTIONS") }}</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(notification, index) in notificationList"
              :key="notification.id"
            >
              <td>{{ getDate(notification.starAt) }}</td>
              <td>{{ getTime(notification.starAt) }}</td>
              <td><div v-html="notification.text" /></td>
              <td>{{ notification.groupsName }}</td>
              <td class="text-center">
                <span :class="statusClass[notification.state]">{{
                  statusList[notification.state]
                }}</span>
              </td>
              <td class="text-center">
                <div class="dropdown m-auto">
                  <button
                    class="btn btn-default"
                    type="button"
                    :id="`dropdown-menu-${tag}`"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                  >
                    <i class="fa fa-ellipsis-h"></i>
                  </button>
                  <ul
                    class="dropdown-menu text-center"
                    :aria-labelledby="`dropdown-menu-${tag}`"
                  >
                    <li
                      @click="modifyNotification(index)"
                      data-bs-toggle="modal"
                      data-bs-target="#notifications"
                    >
                      <span class="dropdown-item d-block cursor-pointer">{{
                        $t("COMMON.EDIT")
                      }}</span>
                    </li>
                    <li class="bg-danger" @click="deleteNotification(index)">
                      <span
                        class="dropdown-item d-block cursor-pointer text-white"
                        >{{ $t("COMMON.CLEAR") }}</span
                      >
                    </li>
                  </ul>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <ModalNotifications
      :announcement="announcement"
      :item="itemSelected"
      @reload="loadNotifications(true)"
    />
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import ModalNotifications from "../../components/details/modals/modalNotifications";
import { UtilsMixin } from "../../mixins/utilsMixin";
import DataNotFound from "../../components/details/DataNotFound";
import Spinner from "../../../admin/components/base/Spinner";

export default {
  name: "AnnouncementNotifications",
  components: { Spinner, DataNotFound, ModalNotifications },
  mixins: [UtilsMixin],
  props: {
    tag: {
      type: String,
      default: "announcementNotifications",
    },
  },
  data() {
    return {
      itemSelected: {},
      statusList: {
        PENDING: this.$t("OBSERVATION_INVOICE_STATUS.2"),
        SENT: this.$t("SENT"),
        ERROR: this.$t("ERROR"),
      },
      statusClass: {
        PENDING: "text-success",
        SENT: "text-gray",
        ERROR: "text-danger",
      },
      notificationList: [],
      loading: false,
    };
  },
  computed: {
    announcement: get("announcementModule/announcement"),
  },
  mounted() {
    this.loadNotifications();
  },
  methods: {
    newNotification() {
      this.itemSelected = {};
    },
    modifyNotification(index) {
      this.itemSelected = Object.assign({
        ...this.notificationList[index],
        sentAt: (this.notificationList[index].starAt || "").slice(0, 16),
      });
    },
    loadNotifications(force = false) {
      if (this.loading && !force) return;

      this.loading = true;
      this.notificationList = [];
      this.$store
        .dispatch("notificationModule/loadNotifications", this.announcement.id)
        .then((response) => {
          this.notificationList = response.data;
        })
        .finally(() => (this.loading = false));
    },
    deleteNotification(index) {
      this.$alertify.confirmWithTitle(
        this.$t("NOTIFICATIONS.DELETE.CONFIRM_TITLE"),
        this.$t("NOTIFICATIONS.DELETE.CONFIRM_DESCRIPTION"),
        () => {
          this.$store
            .dispatch(
              "notificationModule/deleteNotification",
              this.notificationList[index].id
            )
            .then((r) => {
              const { error } = r;
              this.$toast.clear();
              if (error) {
                const { data } = r;
                this.$toast.error(this.$t(data) + "");
              } else {
                this.$toast.success(
                  this.$t("NOTIFICATIONS.DELETE.SUCCESS") + ""
                );
                this.notificationList.splice(index, 1);
              }
            });
        },
        () => {}
      );
    },
  },
};
</script>

 <style scoped lang="scss"> 
.AnnouncementNotifications {
  width: 100%;

  .table-container {
    overflow-x: auto;
  }

  tr > th {
    white-space: nowrap;
  }

  .bg-danger {
    .dropdown-item:hover,
    .dropdown-item:focus {
      background-color: var(--color-dashboard-4) !important;
    }
  }
}
</style>
