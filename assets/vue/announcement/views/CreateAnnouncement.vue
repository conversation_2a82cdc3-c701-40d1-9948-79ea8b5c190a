<template>
  <div class="d-flex align-items-center justify-content-center" v-if="loading">
    <spinner />
  </div>
  <div class="CreateAnnouncement" v-else>
    <step-form id="form-announcement"
               :number-of-steps="steps.total"
               :current-step="steps.current"
               :header-prefix="headerPrefix"
               :steps-custom-titles="stepsCustomTitles"
               @next="next()"
               @prev="prev()"
               @on-step-change="onCurrentStepChange"
               @submit="submit()"
               :current-step-icon="getCurrentStepIcon()"
    >
      <template v-slot:form-content>
        <div class="d-flex flex-column">
          <div class="steps--container w-100" v-show="steps.current === 1">
            <announcement-step1 :announcement="announcement"
                                @selected="announcement.selectedCourse = $event;"
                                :edit="$route.name === 'CreateAnnouncement'"
            />
          </div>
          <div class="steps--container w-100" v-show="steps.current === 2">
            <announcement-step2  :announcement="announcement"
                                 :subsidizer="subsidizer"
                                 :subsidizer-entity="subsidizerEntity"
                                 :tutors="tutors"
                                 @updated="updateContent($event)"
            />
          </div>

          <div class="steps--container w-100" v-show="steps.current === 3">
            <announcement-step3 :announcement="announcement"
                                :formative-action-type="formativeActionType"
                                :format="format"
                                @updated="updateContent($event)"></announcement-step3>
          </div>

          <div class="steps--container w-100" v-for="(client, index) in config.clientsFields" :key="client" v-show="steps.current === (3 + index + 1)">
            <component
                :announcement="announcement"
                :is="getClientComponentName(client)"
                :name="getClientComponentName(client)"
                @updated="updateContent($event)"
            />
          </div>

          <div class="steps--container w-100" v-show="steps.current === steps.total">
            <announcement-last-step
                @updated="updateContent($event)"/>
          </div>
        </div>
      </template>
    </step-form>
  </div>
</template>

<script>
import $ from 'jquery';
import {get} from "vuex-pathify";

import AnnouncementLastStep from "../components/announcementForm/AnnouncementLastStep.vue";
import AnnouncementStep1 from "../components/announcementForm/AnnouncementStep1.vue";
import AnnouncementStep2 from "../components/announcementForm/AnnouncementStep2.vue";
import AnnouncementStep3 from "../components/announcementForm/AnnouncementStep3.vue";
import ButtonWithDescription from "../../common/components/ButtonWithDescription.vue";
import ClientDefaultFields from "../components/announcementForm/fields/ClientDefaultFields.vue";
import ClientImqFields from "../components/announcementForm/fields/ClientImqFields.vue";
import ClientIberostarFields from "../components/announcementForm/fields/ClientIberostarFields.vue";
import Spinner from "../../admin/components/base/Spinner.vue";
import StepForm from "../../common/views/StepForm.vue";

export default {
  name: "CreateAnnouncement",
  $,
  components: {
    AnnouncementStep1, AnnouncementStep2, AnnouncementStep3, AnnouncementLastStep,
    ButtonWithDescription,
    ClientDefaultFields, ClientImqFields, ClientIberostarFields,
    Spinner,
    StepForm,
  },
  data() {
    return {
      loading: false,
      stepsCustomTitles: new Map(),

      steps: {
        current: 1,
        total: 4
      },

      tutors: [],
      format: [],
      formativeActionType: [],
      subsidizer: [],
      subsidizerEntity: [],

      announcement: {
        selectedCourse: null,
        startAt: null,
        finishAt: null,
        selectedTutors: [],


        subsidized: false,
        subsidizerEntity: null,
        subsidizer: null,
        maxUsers: 0,
        formativeActionType: '',
        format: '',
        totalHours: 0,
        place: null,
        trainingCenter: null,
        trainingCenterAddress: null,
        trainingCenterNif: null,
        trainingCenterPhone: null,
        trainingCenterEmail: null,
        trainingCenterTeacherDni: null,
        generalInformation: '',
      }
    };
  },
  computed: {
    config: get('configModule/config'),
    froalaKey() {
      return this.$store.getters['froalaEditorModule/getFroalaKey'];
    },
    useGlobalEventBus() {
      return this.$store.getters['contentTitleModule/getUseGlobalEventBus'];
    },
    headerPrefixAsTitle() {
      const current = this.steps.current;
      const lastStep = this.steps.total;
      return !(current === 1 || current === 2 || current === 3);

    },
    headerPrefix() {
      // const current = this.steps.current;
      // const lastStep = this.steps.total;
      return 'ANNOUNCEMENT';
    }
  },

  watch: {
    config(newValue) {
      this.setMaxSteps();
    }
  },

  async created() {
    this.handleRouteParams();

    const isUpdate = this.$route.name === 'UpdateAnnouncement';
    if (isUpdate) {

    }
    this.loading = true;
    try {
      this.setMaxSteps();
      if (isUpdate) await this.loadAnnouncement()
      await this.loadTutors();
      await this.loadSubsidizedData();
    } finally {
      this.loading = false;
    }
  },

  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on('onAnnouncementSave', e => {
        this.submit();
      });
    }
  },

  beforeDestroy() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$off('onAnnouncementSave');
    }
  },

  methods: {
    getCurrentStepIcon() {
      switch (this.steps.current) {
        case 1: return 'fas fa-university';
        case 2: return 'fa fa-users'
        default: return 'fa fa-file'
      }
    },
    async loadAnnouncement() {
      const { data, error } = await this.$store.dispatch('announcementModule/loadAnnouncementFullData', this.$route.params.id);
      const announcement = data.announcement;
      announcement.startAt = announcement.startAt.slice(0, 16);
      announcement.finishAt = announcement.finishAt.slice(0, 16);
      announcement.selectedCourse = data.course;
      announcement.selectedTutors = data.tutors;

      this.announcement = announcement;
    },

    updateContent(data) {
      this.announcement = Object.assign({}, this.announcement, data);
    },
    setMaxSteps() {
      this.steps.total += this.config.clientsFields.length;
      if (this.steps.total > 3) {
        const customSteps = new Map();
        const extraSteps = this.steps.total - 4;// First 3 steps and last step
        let counter = 'II';
        for (let i = 0; i < extraSteps; i++) {
          const client = this.config.clientsFields[i];
          const step = i + 4;// Because steps start at number 1
          // customSteps.set(step, `ANNOUNCEMENT.FORM.${client.toUpperCase()}`)
          customSteps.set(step, this.$t(`ANNOUNCEMENT.FORM.ANNEX`, [counter]))
          counter += 'I';
        }
        customSteps.set(this.steps.total, `ANNOUNCEMENT.FORM.LAST_STEP`)
        this.stepsCustomTitles = customSteps;
      }
    },
    getClientComponentName(clientName) {
      return `Client${clientName[0].toUpperCase() + clientName.substring(1)}Fields`;
    },
    handleRouteParams() {
      if (this.$route.params.origin != undefined) {
        this.$store.dispatch('contentTitleModule/addRoute', {
          routeName: 'Home',
          params: {
            linkName: this.$t('ANNOUNCEMENTS'),
            params: {}
          }
        });
      }
      this.$store.dispatch('contentTitleModule/addRoute', {
        routeName: this.$route.name,
        params: {
          linkName: this.$route.name === 'UpdateAnnouncement' ? this.$t('ANNOUNCEMENT.UPDATE') : this.$t('ANNOUNCEMENT.CREATE'),
          params: this.$route.params
        }
      });

      this.$store.dispatch('contentTitleModule/setActions', { route: this.$route.name, actions: [
          {
            name: this.$t('SAVE'),
            event: 'onAnnouncementSave',
            class: 'btn btn-primary'
          }
        ]});
    },

    isStepValid() {
      if (this.steps.current === 1 && !this.validateFirstStep()) return false;
      return !(this.steps.current === 2 && !this.validateSecondStep());
    },

    onCurrentStepChange(step) {
      if (step > this.steps.current)
        if (!this.isStepValid()) return;
      this.steps.current = step;
    },

    next() {
      if (!this.isStepValid()) return;
      this.steps.current++;
    },

    prev() {
      this.steps.current--;
    },

    async loadTutors() {
      const { data } = await this.$store.dispatch('announcementModule/loadTutors');
      this.tutors = data;
    },

    async loadSubsidizedData() {
      const { data } = await this.$store.dispatch('announcementModule/loadSubsidizedData');
      this.format = data.format;
      this.formativeActionType = data.formativeActionType;
      this.subsidizer = data.subsidizer;
      this.subsidizerEntity = data.subsidizerEntity;
    },

    // Validations
    validateFirstStep() {
      if (!this.announcement.selectedCourse) {
        this.$toast.error(this.$t('ANNOUNCEMENT.COURSE_REQUIRED') + '');
        return false;
      }
      return true;
    },

    validateSecondStep() {
      if (!this.announcement.startAt || this.announcement.startAt.length < 1) {
        this.$toast.error(this.$t('ANNOUNCEMENT.START_AT_REQUIRED') + '');
        return false;
      }
      if (!this.announcement.finishAt || this.announcement.finishAt.length < 1) {
        this.$toast.error(this.$t('ANNOUNCEMENT.FINISH_AT_REQUIRED') + '');
        return false;
      }
      return true;
    },

    submit() {
      if (!this.validateFirstStep() || !this.validateSecondStep()) return;

      const isUpdate = this.$route.name === 'UpdateAnnouncement';

      const currentForm = document.forms['form-announcement'];
      const formData = new FormData(currentForm);

      formData.append('course', JSON.stringify(this.announcement.selectedCourse));
      formData.append('tutors', JSON.stringify(this.announcement.selectedTutors));
      formData.append('subsidized', this.announcement.subsidized);
      formData.append('generalInformation', this.announcement.generalInformation);

      this.$alertify.confirmWithTitle(
          this.$t(`ANNOUNCEMENT.SAVE.CONFIRM.TITLE`),
          this.$t(`ANNOUNCEMENT.SAVE.CONFIRM.DESCRIPTION`),
          () => {
            this.saveAnnouncement(formData, isUpdate);
          },
          () => {},
      )
    },

    saveAnnouncement(formData, update = false) {
      const id = update ? this.$route.params.id : null;
      function save(store) {
        if (update) return store.dispatch('announcementModule/updateAnnouncement', { id, formData });
        else return store.dispatch('announcementModule/createAnnouncement', formData);
      }


      this.$store.dispatch('loaderModule/setLoading', { loading: true, message: this.$t('SAVING')});
      save(this.$store).then(res => {
        const { data, error } = res;
        if (error) {
          this.$toast.error(data);
        } else {
          this.$toast.success(data.message);
          this.$store.dispatch('contentTitleModule/removeRouteFromContentTitle', this.$route.name);
          this.$store.dispatch('routerModule/setDeleteLastRoute', true);
          this.$router.replace({ name: 'ViewAnnouncement', params: { id: data.id } });
        }
      }).finally(() => {
        this.$store.dispatch('loaderModule/setLoading', { loading: false});
      });
    }
  }
}
</script>

 <style scoped lang="scss"> 
.CreateAnnouncement {
  width: 100%;
}
</style>
