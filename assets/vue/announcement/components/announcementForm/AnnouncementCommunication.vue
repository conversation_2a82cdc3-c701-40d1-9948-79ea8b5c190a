<script>
import ButtonWithDescription from "../../../common/components/ButtonWithDescription.vue";
import {get, sync} from "vuex-pathify";
import { configurationClientAnnouncement } from "../../mixins/configurationClientAnnouncement";

export default {
  name: "AnnouncementCommunication",
  components: { ButtonWithDescription },
  mixins: [configurationClientAnnouncement],
  computed: {
    type: get("announcementFormModule/announcement@type"),// Announcement type
    stepsConfigurations: get("announcementFormModule/stepsConfigurations"),// All steps configurations
    stepInfo() {
      return this.stepsConfigurations[this.type].steps.find(s => s.type === 'AnnouncementCommunication') ?? {};
    },
    announcementConfigurationTypes() {
      return this.stepInfo?.configurations ?? [];
    },
    configAnnouncement: sync(
        "announcementFormModule/announcement@configAnnouncement"
    ),
  },
};
</script>

<template>
  <div class="AnnouncementCommunication">
    <button-with-description
      v-for="config in announcementConfigurationTypes"
      :key="config.id"
      :name="`configuration-${config.id}`"
      :title="config.name"
      :description="config.description"
      :image-url="config.image"
      v-model="configAnnouncement[`configuration-${config.id}`]"
    />
  </div>
</template>

<style scoped lang="scss">
.AnnouncementCommunication {
  display: grid;
  gap: 2rem;
  padding: 1rem 3rem;

  @media #{min-medium-screen()} {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
