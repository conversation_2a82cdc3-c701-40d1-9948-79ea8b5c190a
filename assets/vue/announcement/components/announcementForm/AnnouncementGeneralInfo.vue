<script>
import { get, sync } from "vuex-pathify";
import AnnouncementConfigurationType from "../../mixins/AnnouncementConfigurationType";
import { configurationClientAnnouncement } from "../../mixins/configurationClientAnnouncement";
import { COURSE_TYPE_ON_SITE, COURSE_TYPE_ONLINE, COURSE_TYPE_MIXED, COURSE_TYPE_VIRTUAL_CLASSROOM } from "../../../course/mixins/constants";

import ButtonWithDescription from "../../../common/components/ButtonWithDescription.vue";
import ChapterTiming from "../chapterTiming/ChapterTiming.vue";
import Spinner from "../../../base/BaseSpinner.vue";
import FileAsText from "../../../common/components/file/FileAsText.vue";
import LabelWithInfo from "../../../common/components/ui/LabelWithInfo.vue";
import Multiselect from "vue-multiselect";
import BaseModal from "../../../base/BaseModal.vue";
import BaseTimePicker from '../../../admin/components/base/BaseTimePicker.vue';


export default {
  name: "AnnouncementGeneralInfo",
  components: {
      BaseTimePicker,
      BaseModal,
    LabelWithInfo,
    FileAsText,
    Spinner,
    ChapterTiming,
    ButtonWithDescription,
    Multiselect,
  },

  mixins: [configurationClientAnnouncement],

  data() {
    return {
      loadingChapters: false,
      showChapters: false,
      test: {},
      trainingHours: 0,
      trainingMinutes: 0
    };
  },

  computed: {
    type: get("announcementFormModule/announcement@type"),// Announcement type
    stepsConfigurations: get("announcementFormModule/stepsConfigurations"),// All steps configurations
    stepInfo() {
      return this.stepsConfigurations[this.type].steps.find(s => s.type === 'AnnouncementGeneralInfo') ?? {};
    },

    announcementConfigurationTypes() {
      return this.stepInfo?.configurations ?? [];
    },

    timezones: get("announcementFormModule/timezones"),
    isNotified: get("announcementFormModule/isNotified"),
    course: get("announcementFormModule/announcement@course"),
    objectiveAndContent: sync(
      "announcementFormModule/announcement@objectiveAndContent"
    ),
    chapterTiming: get("announcementFormModule/announcement@chapterTiming"),
    chapters: get("announcementFormModule/chapters"),
    approvedCriteria: get("announcementFormModule/approvedCriteria"),

    configAnnouncement: sync(
      "announcementFormModule/announcement@configAnnouncement"
    ),

    code: sync("announcementFormModule/announcement@code"),
    usersPerGroup: sync("announcementFormModule/announcement@usersPerGroup"),
    timezone: sync("announcementFormModule/announcement@timezone"),
    startAt: sync("announcementFormModule/announcement@startAt"),
    finishAt: sync("announcementFormModule/announcement@finishAt"),
    totalHours: sync("announcementFormModule/announcement@totalHours"),
    didacticGuide: sync("announcementFormModule/announcement@didacticGuide"),
    approvedCriteriaValues: sync(
      "announcementFormModule/announcement@approvedCriteriaValues"
    ),
    extra: get("announcementFormModule/extra"),

    configurationsClientInThisPage()
    {
      const configurations = [];

      if(this.type === COURSE_TYPE_ONLINE|| this.type === COURSE_TYPE_MIXED)
      {
        configurations.push({
          type: "ACCESS_CONTENT",
        });
        configurations.push({
          type: "TEMPORALIZATION",
        });
        configurations.push({
          type: "COST",
        });

      }
      else if (this.type === COURSE_TYPE_ON_SITE)
      {
        configurations.push({
          type: "ACCESS_CONTENT",
        });

        configurations.push({
          type: "DIGITAL_SIGNATURE",
        });

        configurations.push({
          type: "COST",
        });
      }
      else{
        configurations.push({
          type: "COST",
        });
      }

      return this.fetchConfigurationByComponent(configurations);
    },

    froalaConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 400,
        pluginsEnabled: ["align", "lists", "paragraphStyle", "paragraphFormat"],
      };
    },

    configAnnouncementWatch() {
      return Object.assign({}, this.configAnnouncement);
    },

    computedApprovedCriteriaValues() {
      return structuredClone(this.approvedCriteriaValues);
    },
  },

  watch: {
    computedApprovedCriteriaValues: {
      deep: true,
      handler: function (val, oldVal) {
        if (this.type === COURSE_TYPE_ON_SITE) return;
        this.approvedCriteria.forEach((item) => {
          if (this.approvedCriteriaValues[item.id].enabled) {
            if (oldVal[item.id].enabled === false)
              this.approvedCriteriaValues[item.id].value = item.extra.value;
            if (this.approvedCriteriaValues[item.id].value < item.extra.min)
              this.approvedCriteriaValues[item.id].value = item.extra.min;
            else if (
              this.approvedCriteriaValues[item.id].value > item.extra.max
            )
              this.approvedCriteriaValues[item.id].value = item.extra.max;
          }
          this.approvedCriteriaValues[item.id].dataType = item.extra.dataType;
        });
      },
    },
    configAnnouncementWatch: {
      deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        if (!val) return;

        const timingConfig = this.announcementConfigurationTypes.find(c => c.id === AnnouncementConfigurationType.TEMPORALIZATION);
        if (timingConfig !== undefined) {
          // The current course type has chapter timing enabled
          const vModelName = AnnouncementConfigurationType.getVModelFromId(AnnouncementConfigurationType.TEMPORALIZATION);
          if (!val[vModelName]) {
            this.showChapters = false;
            return;
          }

          if (
              this.chapters.length > 0 &&
              this.chapters[0].courseId === this.course.id
          ) {
            if (
                oldVal &&
                oldVal[vModelName] ===
                val[vModelName]
            )
              return;
          }
          this.showChapters = true;
          this.getCourseChaptersInfo();
        }
      },
    },

    startAt: {
      handler: function () {
        if (this.type === COURSE_TYPE_ON_SITE || this.type === COURSE_TYPE_VIRTUAL_CLASSROOM) return;
        const keys = Object.keys(this.chapterTiming);
        keys.forEach((key) => {
          const chapterStart = new Date(this.chapterTiming[key].start);
          const chapterEnd = new Date(this.chapterTiming[key].end);
          const announcementStart = new Date(this.startAt);
          const announcementEnd = new Date(this.finishAt);

          if (chapterStart < announcementStart)
            this.chapterTiming[key].start = this.startAt;
          if (chapterEnd > announcementEnd)
            this.chapterTiming[key].end = this.finishAt;
        });
      },
    },
  },

  mounted() {
    if(this.type === COURSE_TYPE_ONLINE || this.type === COURSE_TYPE_MIXED)
    {
      const approvedCriteria = structuredClone(this.approvedCriteriaValues);
      this.approvedCriteria.forEach(criteria => {
        const element = document.getElementById(`generalInfoSwitchCheckChecked-${criteria.id}`);
        if (element) {
          if (criteria.extra?.alwaysEnabled) {
            const item = approvedCriteria[criteria.id];

            item.enabled = true;
            if (item.value === 0) item.value = criteria.extra?.value ?? 0;
            if (item.value < criteria.extra?.min) item.value = criteria.extra?.min;
            if (item.value > criteria.extra?.max) item.value = criteria.extra?.max;
            item.dataType = criteria.extra?.dataType;
            element.checked = true;
            element.disabled = true;
            approvedCriteria[criteria.id] = item;
          }
        }
      });
      this.approvedCriteriaValues = approvedCriteria;
    }
  },

  methods: {
    async getCourseChaptersInfo() {
      try {
        this.loadingChapters = true;
        await this.$store.dispatch(
          "announcementFormModule/getCourseChaptersInfo"
        );
      } finally {
        this.loadingChapters = false;
      }
    },
    closeModal(closeButtonId) {
      document.getElementById(closeButtonId).click();
    },
    setTotalHours(hours){
      this.totalHours = hours;
    }
  },
};
</script>

<template>
  <div class="AnnouncementGeneralInfo">
    <div class="AnnouncementGeneralInfo--info d-flex flex-row flex-wrap">
      <div class="col-xs-12 col-md-4">
        <div
          id="AnnouncementGeneralInfo-code"
          class="form-group col-12 info-icon required"
        >
          <label-with-info
            :info="$t('ANNOUNCEMENT.FORM.ENTITY.CODE_INFO') + ''"
          >
            {{ $t("ANNOUNCEMENT.FORM.ENTITY.ANNOUNCEMENT_NAME") }}
          </label-with-info>
          <input
            type="text"
            class="form-control"
            v-model="code"
            :disabled="isNotified"
          />
        </div>
        <div class="form-group col-12 required" id="AnnouncementGeneralInfo-timezone">
          <label>{{ $t("TIMEZONE") }}</label>
          <Multiselect
              v-model="timezone"
              :options="timezones"
              :searchable="true"
              :placeholder="$t('MULTISELECT.PLACEHOLDER')"
              :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
              :disabled="isNotified"
          ></Multiselect>
        </div>
        <div id="AnnouncementGeneralInfo-startAt" class="form-group col-12 required">
          <label>{{ $t("ANNOUNCEMENT.FORM.ENTITY.START_AT") }}</label>
          <input
            type="datetime-local"
            class="form-control"
            v-model="startAt"
            :disabled="isNotified"
          />
        </div>
        <div id="AnnouncementGeneralInfo-finishAt" class="form-group col-12 required">
          <label>{{ $t("ANNOUNCEMENT.FORM.ENTITY.FINISH_AT") }}</label>
          <input
            type="datetime-local"
            class="form-control"
            v-model="finishAt"
            :disabled="isNotified"
          />
        </div>
      </div>
      <div class="col-xs-12 col-md-4">
        <div id="AnnouncementGeneralInfo-totalHours" class="form-group col-12 required">
          <label>{{ $t("ANNOUNCEMENT.FORM.ENTITY.FORMATION_TIME") }}</label>
          <div class="row align-items-start">
              <div class="col">
                  <BaseTimePicker
                      :current-time="totalHours"
                      :disabled="isNotified"
                      :max-hours="100"
                      :show-presets="true"
                      @change="setTotalHours"
                  />
              </div>
          </div>
        </div>

        <file-as-text
          id="AnnouncementGeneralInfo-didacticGuide"
          class="col-12"
          :label="$t('ANNOUNCEMENT.FORM.ENTITY.DIDACTIC_GUIDE') + ''"
          :label-info="$t('ANNOUNCEMENT.FORM.ENTITY.DIDACTIC_GUIDE_INFO') + ''"
          v-model="didacticGuide"
          :label-with-info="true"
          :disabled="isNotified"
          :delete-btn="didacticGuide != null"
        />
      </div>
      <div class="form-group col-xs-12 col-md-4">
        <div
          class="form-group info-icon required"
          id="AnnouncementGeneralInfo-usersPerGroup"
        >
          <label-with-info
            :info="$t('ANNOUNCEMENT.FORM.ENTITY.MAX_GROUP_SIZE_INFO') + ''"
          >
            {{ $t("ANNOUNCEMENT.FORM.ENTITY.MAX_GROUP_SIZE") }}
          </label-with-info>
          <input
            type="number"
            class="form-control"
            :disabled="isNotified"
            v-model.number="usersPerGroup"
            min="0"
          />
        </div>

        <label-with-info
          :info="$t('ANNOUNCEMENT.FORM.ENTITY.OBJECTIVE_AND_CONTENT_INFO') + ''"
          location="top"
        >
          {{ $t("ANNOUNCEMENT.FORM.ENTITY.OBJECTIVE_AND_CONTENT") }}
        </label-with-info>
        <div
          class="objetive-and-content"
          data-bs-toggle="modal"
          data-bs-target="#objective-and-content-modal"
          v-html="objectiveAndContent"
        />

        <BaseModal
          identifier="objective-and-content-modal"
          title="Objetivos y contenidos"
          size="modal-xl"
        >
          <template>
            <div style="padding: 1rem 3rem 0 3rem">
              <label class="w-100">{{
                $t("ANNOUNCEMENT.FORM.ENTITY.OBJECTIVE_AND_CONTENT")
              }}</label>
              <froala
                tag="textarea"
                v-model="objectiveAndContent"
                :config="froalaConfig"
                :disabled="isNotified"
              ></froala>
              <div
                class="w-100 d-flex align-items-center justify-content-end mt-3"
              >
                <button
                  type="button"
                  class="btn btn-primary pl-2 pr-2"
                  @click="closeModal('objective-and-content-modal_close')"
                >
                  {{ $t("SAVE") }}
                </button>
              </div>
            </div>
          </template>
        </BaseModal>
      </div>
      <div class="col-12">
        <div
          v-if="extra && extra.length > 0"
          id="AnnouncementGeneralInfo-extra"
          class="form-group col-12 required row"
        >
          <div
            v-for="item in extra"
            :key="item.id"
            :id="`AnnouncementGeneralInfo-${item.id}`"
            class="form-group col-6"
          >
            <label>{{ item.name }}</label>
            <input
              type="text"
              class="form-control"
              v-model="item.value"
              :name="`extra[${item.id}]`"
              :disabled="isNotified"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="AnnouncementGeneralInfo--criteria d-flex flex-row flex-wrap p-3"
      v-if="type === 'online' || type === 'mixed'"
    >
      <h1 class="w-100 text-center">
        {{ $t("ANNOUNCEMENT.FORM.ENTITY.APPROVED_CRITERIA") }}
      </h1>
      <div
        class="form-group with-switch col-xs-12 col-md-4"
        v-for="typeCriteria in approvedCriteria"
        :key="typeCriteria.id"
      >
        <label
          >{{ typeCriteria.name }} ({{
            $t(typeCriteria.extra?.dataType ?? "%")
          }})</label
        >
        <div class="form-check form-switch">
          <input
            class="form-check-input"
            type="checkbox"
            :id="`generalInfoSwitchCheckChecked-${typeCriteria.id}`"
            v-model="approvedCriteriaValues[typeCriteria.id].enabled"
            :disabled="isNotified"
          />
          <label-with-info
            :id="`generalInfoSwitchCheckChecked-${typeCriteria.id}--label`"
            :info="typeCriteria.description"
            :class="
              approvedCriteriaValues[typeCriteria.id].enabled
                ? 'criteria-enabled'
                : ''
            "
            :for="`generalInfoSwitchCheckChecked-${typeCriteria.id}`"
            location="top"
          >
            <input
                :id="`generalInfoSwitchCheckChecked-${typeCriteria.id}-input-value`"
                :type="typeCriteria.extra?.type ?? 'number'"
                :min="typeCriteria.extra?.min ?? 0"
                :max="typeCriteria.extra?.max ?? 100"
                :step="typeCriteria.extra?.step ?? 0.01"
                class="form-control"
                :disabled="!approvedCriteriaValues[typeCriteria.id].enabled || isNotified"
                v-model.number="approvedCriteriaValues[typeCriteria.id].value"
            />
          </label-with-info>
        </div>
      </div>
    </div>
    <div
      class="AnnouncementGeneralInfo--chapter--timing"
    >
      <div class="col-12 actions">
        <button-with-description
            v-for="config in announcementConfigurationTypes"
            :key="config.id"
            :name="`configuration-${config.id}`"
            :title="config.name"
            :description="config.description"
            :image-url="config.image"
            v-model="configAnnouncement[`configuration-${config.id}`]"
            :disabled="isNotified"
        />
      </div>

      <div
        class="col-12 d-flex align-items-center justify-content-center"
        v-if="showChapters && loadingChapters"
      >
        <spinner />
      </div>
      <div
        class="col-12 ChapterTimingContainer"
        v-if="showChapters && !loadingChapters"
      >
        <chapter-timing
          v-for="chapter in chapters"
          :key="chapter.id"
          :finished-at="true"
          :time="true"
          v-model="chapterTiming[chapter.id]"
          :disabled="isNotified"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.custom-file-with-button {
  label {
    width: 100%;
  }

  input {
    display: none;
  }
}

.AnnouncementGeneralInfo {
  &--info {
    padding: 0 0 1rem 0;

    .objetive-and-content {
      border: 1px solid var(--color-neutral-mid-darker);
      padding: 0.5rem;
      border-radius: 5px;
      min-height: 239px;
      max-height: 240px;
      overflow-y: auto;
    }
  }

  &--criteria {
    background-color: #d8eef8;
    margin: 0 -1rem;

    h1 {
      font-weight: bold;
      font-size: 18px;
      color: var(--color-neutral-darkest);
    }

    .form-group {
      label {
        font-size: 16px;
      }
      &.with-switch {
        .form-check.form-switch {
          display: flex;
          align-items: center;
          padding-left: 0;

          & > label {
            flex-grow: 1;
            margin-left: 0.25rem;
          }
        }
      }

      .criteria-enabled {
        opacity: 1 !important;
      }
    }
  }

  &--chapter--timing {
    padding-top: 1rem;
    .actions {
      display: flex;
      flex-flow: column;

      @media #{min-small-screen()} {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
      }
    }

    .ChapterTimingContainer {
      display: flex;
      flex-flow: column;
      width: 100%;
      margin-top: 2rem;
      gap: 0.5rem;
      justify-content: center;

      @media #{min-small-screen()} {
        display: grid;
        grid-template-columns: repeat(auto-fit, 271px);
        align-items: flex-start;
      }
    }
  }
}
</style>
