<template>
  <BaseModal
    identifier="user-email-validation-account"
    size="modal-md"
    padding="2rem"
    :title="$t('ANNOUNCEMENT.EMAIL.MESSAGE_TITLE')"
  >
    <p>
      <strong>{{ selectedUserProfile?.name }}</strong><br>
      {{ selectedUserProfile?.email }}
    </p>

    <p>
     {{ $t('ANNOUNCEMENT.EMAIL.MESSAGE_TO_SEND') }}
    </p>

    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
      <button
        type="button"
        class="btn btn-secondary"
        data-bs-dismiss="modal"
        aria-label="Close"
        ref="close-modal"
      >
      {{ $t("ALERTIFY.CANCEL")  }}
      </button>

      <button type="button" class="btn btn-primary" @click="sendEmail">
        {{ $t("ANNOUNCEMENT.EMAIL.SEND.MESSAGE")  }}
      </button>
    </div>
  </BaseModal>
</template>

<script>
import { sync } from "vuex-pathify";

export default {
  computed: {
    selectedUserProfile: sync("announcementModule/selectedUserProfile"),
  },

  methods: {
    async sendEmail() {
      const data = await this.$store.dispatch("announcementModule/sendEmailForValidationUser", this.selectedUserProfile.id);

      if(!data?.error){
        this.$toast.success(this.$t("ANNOUNCEMENT.EMAIL.SEND.SUCCES"));
        this.closeModal();      
      }
      else{
        this.$toast.error(this.$t("ANNOUNCEMENT.EMAIL.SEND.FAIL"));
        this.closeModal();
      }
      
    },
    closeModal() {
      this.$refs["close-modal"].click();
    },
  },
};
</script>

 <style scoped lang="scss"> 
</style>