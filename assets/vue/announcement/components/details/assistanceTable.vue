<template>
  <div class="assistanceTable">
    <table class="table table-condensed">
      <thead>
        <tr>
          <th>
            {{
              $t(
                "ANNOUNCEMENT.SESSIONSTAB." +
                  (isVirtual ? "VIRTUAL" : "PRESENTIAL")
              )
            }}
          </th>
          <th>{{ $t("ANNOUNCEMENT.SESSIONSTAB.START") }}</th>
          <th v-if="!minView">{{ $t("ANNOUNCEMENT.SESSIONSTAB.END") }}</th>
          <th>{{ $t("STATUS") }}</th>
          <th class="text-center" v-if="!minView">
            {{ $t("ANNOUNCEMENT.SESSIONSTAB.DOWNLOAD_ASSISTANCE") }}
          </th>
          <th class="text-center" v-if="!isVirtual && tutorView && !minView">
            {{ $t("ANNOUNCEMENT.SESSIONSTAB.QR_ENTRY") }}
          </th>
          <th class="text-center" v-if="!isVirtual && tutorView && !minView">
            {{ $t("ANNOUNCEMENT.SESSIONSTAB.QR_EXIT") }}
          </th>
          <th class="text-center">{{ $t("ANNOUNCEMENT.ASSISTANCE") }}</th>
          <th class="text-center" v-if="isVirtual && !minView">
            {{ $t("ANNOUNCEMENT.SESSIONSTAB.VIRTUALCLASSROOM") }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(session, index) in paginatedList"
          @click="setUserList(index)"
          :key="index"
        >
          <td>
            <span
              class="underline cursor-pointer text-primary"
              data-bs-toggle="modal"
              data-bs-target="#userAssistance"
            >
              {{
                $t(
                  "ANNOUNCEMENT.SESSIONSTAB." +
                    (isVirtual ? "VIRTUAL" : "PRESENTIAL")
                ) +
                (index + 1)
              }}
            </span>
          </td>
          <td>
            <DateTimeAndLocal
              v-model="session.startAt"
              :timezone="session.timezone"
            ></DateTimeAndLocal>
          </td>
          <td v-if="!minView">
            <DateTimeAndLocal
              v-model="session.finishAt"
              :timezone="session.timezone"
            ></DateTimeAndLocal>
          </td>
          <td :class="className[session.state]">{{ state[session.state] }}</td>
          <td class="text-center" v-if="!isVirtual && !minView">
            <a
              class="underline underline-color-primary cursor-pointer text-primary"
              data-bs-toggle="modal"
              data-bs-target="#ModalSessionAssistanceFiles-modal"
              >{{ $t("ANNOUNCEMENT-SESSIONSTAB-VIEWDOCUMENT") }}</a
            >
          </td>
          <td
            class="text-center p-1"
            v-if="!isVirtual && tutorView && !minView"
          >
            <QrGenerator
              class="cursor-pointer"
              :value="session.entryQrToken"
              :size="64"
              data-bs-toggle="modal"
              :data-bs-target="`#entry-token-${session.id}qrPreview`"
            />
            <ModalQrPreview
              :tag="`entry-token-${session.id}`"
              v-model="session.entryQrToken"
              :title="$t('ANNOUNCEMENT.SESSIONSTAB.QR_ENTRY') + ''"
            />
          </td>
          <td
            class="text-center p-1"
            v-if="!isVirtual && tutorView && !minView"
          >
            <QrGenerator
              class="cursor-pointer"
              :value="session.exitQrToken"
              :size="64"
              data-bs-toggle="modal"
              :data-bs-target="`#exit-token-${session.id}qrPreview`"
            />
            <ModalQrPreview
              :tag="`exit-token-${session.id}`"
              v-model="session.exitQrToken"
              :title="$t('ANNOUNCEMENT.SESSIONSTAB.QR_EXIT') + ''"
            />
          </td>
          <td class="text-center">
            <span class="badge badge-primary">{{ getAssistence(index) }}</span>
          </td>
          <td class="text-center" v-if="isVirtual && !minView">
            <a class="btn btn-sm btn-primary" :href="session.url">
              <i class="fa fa-external-link-square-alt"></i>
            </a>
          </td>
        </tr>
      </tbody>
    </table>

    <pagination
      v-show="pageSize < sessionList.length"
      :items="sessionList"
      :page-size="pageSize"
      @items-page="paginatedList = $event"
    />
  </div>
</template>

<script>
import { sync } from "vuex-pathify";
import DateTimeTag from "./dateTimeTag";
import Pagination from "../../../admin/components/Pagination";
import QrGenerator from "../../../base/QrGenerator";
import ModalQrPreview from "./modals/modalQrPreview.vue";
import DateTimeAndLocal from "../../../common/components/DateTimeAndLocal.vue";
export default {
  name: "assistanceTable",
  components: {
    DateTimeAndLocal,
    ModalQrPreview,
    QrGenerator,
    Pagination,
    DateTimeTag,
  },
  props: {
    tag: {
      type: String,
      default: "group0",
    },
    isVirtual: {
      type: Boolean,
      default: false,
    },
    minView: {
      type: Boolean,
      default: false,
    },
    groupId: {
      type: Number,
      default: 0,
    },
    sessionList: {
      type: Array,
      default: () => [],
    },

    tutorView: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      state: {
        NOT_STARTED: this.$t("OBSERVATION_INVOICE_STATUS.2"),
        IN_PROCESS: this.$t("ANNOUNCEMENT.SESSIONSTAB.IN_PROGRESS"),
        FINISHED: this.$t("FINISHED"),
      },
      className: {
        NOT_STARTED: "text-success",
        IN_PROCESS: "text-warning",
        FINISHED: "text-gray",
      },
      pageSize: 10,
      paginatedList: [],
    };
  },
  computed: {
    showSessionSelectedFiles: sync(
      "announcementModule/showSessionSelectedFiles"
    ),
  },
  mounted() {
    this.paginatedList = this.sessionList.slice(0, this.pageSize);
  },
  methods: {
    getAssistence(index) {
      const { assistance } = {
        assistance: this.paginatedList[index].assistance || [],
      };
      const assistanceFiltered = assistance.filter(
        (student) => student.assistance
      );
      return `${assistanceFiltered.length} / ${assistance.length}`;
    },
    setUserList(index) {
      this.$store.dispatch("announcementModule/setSessionSelected", {
        title:
          this.$t(
            "ANNOUNCEMENT.SESSIONSTAB." +
              (this.isVirtual ? "VIRTUAL" : "PRESENTIAL")
          ) +
          (index + 1),
        groupId: this.groupId,
        ...(this.paginatedList[index] || {}),
      });
    },

    dowloadTemplateAssistance(sessionId) {
      this.$store.dispatch(
        "announcementModule/downloadAnnouncementGroupSessionAssistance",
        { sessionId: sessionId }
      );
    },
  },
};
</script>

 <style scoped lang="scss"> 
.assistanceTable {
  .badge-primary,
  .btn-primary {
    color: white !important;
    background-color: var(--color-primary) !important;
  }
}
</style>
