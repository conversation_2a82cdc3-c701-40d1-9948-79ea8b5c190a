<template>
<div class="chapterDetails">
  <div>
    <p class="title font-weight-bold">{{title}}</p>
    <p>Inicio: <span class="font-weight-bold">{{ dateFrom }}</span></p>
    <p>Final: <span class="font-weight-bold">{{ dateTo }}</span></p>
  </div>
</div>
</template>

<script>
export default {
  name: "chapterDetails",
  props: {
    title: { type: String, default: '' },
    dateFrom: { type: String, default: '' },
    dateTo: { type: String, default: '' }
  }
}
</script>

 <style scoped lang="scss"> 
.chapterDetails {
  div {
    background-color: var(--color-neutral-mid-light);
    padding: 0.5rem;

    p:not(.title) {
      margin: 0.2rem auto;
    }
  }
  text-align: center;
}
</style>
