<template>
  <div v-if="hideOn" class="DataNotFound" :class="{'banner': banner}">
    <p class="text" :class="'text-' + align"><i class="fa mr-2" :class="icon"></i>{{text || $t('NO_DATA')}}</p>
  </div>
</template>

<script>
export default {
  name: "DataNotFound",
  props: {
    hideOn: { type: Boolean, default: false },
    text: { type: String, default: undefined },
    align: { type: String, default: 'center'},
    icon: { type: String, default: 'fa-exclamation-circle'},
    banner: { type: <PERSON><PERSON><PERSON>, default: false },
  }
}
</script>

 <style scoped lang="scss"> 
.DataNotFound {
  .text {
    margin: 0 auto;
  }

  &:not(&.banner) {
    .text {
      font-size: 0.9rem;
      color: var(--color-neutral-mid-darker);
    }
  }

  &.banner {
    padding: 2rem;
    display: grid;
    place-items: center;
    font-size: 1.5rem;
    background-color: var(--color-neutral-light);
    color: var(--color-neutral-mid-darker);
    border-radius: 5px;
  }
}
</style>
