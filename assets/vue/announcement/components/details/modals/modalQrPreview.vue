<template>
  <div class="modalQrPreview">
<!--    :title="$t('ANNOUNCEMENT.SESSIONSTAB.QR_DESC') || ''"-->
    <BaseModal
      :identifier="tag + 'qrPreview'"
      :title="title"
      padding="2rem"
      size="modal-md"
    >
      <div class="d-flex flex-column gap-3 align-items-center justify-content-center">
        <QrGenerator :value="value" @update="getDataURL" :size="450"/>
        <a v-show="qrData.length"
           class="btn btn-sm btn-primary align-self-end mt-3"
           :href="qrData"
           download="QR_SESSION_CODE"
           target="_blank">
          <i class="fa fa-download mr-2"></i> {{ $t('ANNOUNCEMENT.INFOTAB.DOWNLOAD_TEXT') }}
        </a>
      </div>
    </BaseModal>
  </div>
</template>

<script>

import { get }        from "vuex-pathify";
import QrGenerator    from "../../../../base/QrGenerator";
export default {
  name: "modalQrPreview",
  components: {QrGenerator},
  props: {
    tag: { type: String, default: "" },
    title: {
      type: String,
      default: ''
    },
    value: {
      type: Number|String|Object,
      default: null
    }
  },
  data() {
    return {
      qrData: ''
    }
  },
  methods: {
    getDataURL(data) {
      this.qrData = data;
    }
  }
};
</script>

 <style scoped lang="scss"> 
.modalQrPreview {
  .QrGenerator {
    margin: auto;
  }
}
</style>
