export default {
  data() {
    return {
      SELECTOR_INPUT_SEARCH:
        ".wrapper .main-header .form-action-search .form-widget input",
      searchInputElement: null,
      searchInputValue: "",
      searchTermCommitted: "",
    };
  },
  mounted() {
    this.searchInputElement = document.querySelector(
      this.SELECTOR_INPUT_SEARCH
    );

    if (this.searchInputElement) {
      this.searchInputValue = this.searchInputElement.value || "";
      this.searchInputElement.addEventListener("keyup", this.handleSearchKeyUp);
    }
  },
  beforeDestroy() {
    if (this.searchInputElement) {
      this.searchInputElement.removeEventListener(
        "keyup",
        this.handleSearchKeyUp
      );
    }
  },
  methods: {
    handleSearchKeyUp(event) {
      this.searchInputValue = event.target.value;
      event.target.setAttribute("value", this.searchInputValue);

      if (this.searchInputValue.trim().length > 0) {
        event.target.classList.remove("is-blank");
      }

      if (event.key === "Enter") {
        this.createResetButton(event);
        this.searchTermCommitted = this.searchInputValue;
        this.onSearch(this.searchInputValue);
      }
    },

    createResetButton(event) {
      const containerInput = event.target.parentNode.parentNode;
      const existingButton = containerInput.querySelector(
        ".content-search-reset"
      );

      if (!existingButton) {
        const containerButton = document.createElement("div");
        containerButton.classList.add("content-search-reset");

        const iconButton = document.createElement("i");
        iconButton.classList.add("fas", "fa-fw", "fa-times");
        containerButton.appendChild(iconButton);

        containerInput.appendChild(containerButton);

        containerButton.addEventListener("click", () => {
          this.searchInputValue = "";
          this.searchTermCommitted = "";
          event.target.value = "";
          event.target.setAttribute("value", "");
          event.target.classList.add("is-blank");

          this.onSearch(this.searchInputValue);
          containerButton.remove();
        });
      }
    },

    onSearch(searchValue) { },

    highlightText(text) {
      if (!this.searchTermCommitted) {
        return text;
      }
      const escapedSearch = this.searchTermCommitted.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
      const pattern = new RegExp(`(${escapedSearch})`, "gi");
      return text.replace(pattern, "<mark>$1</mark>");
    },
  },
};
