import HomeView from "../views/HomeView.vue";
import CourseCategoryView from "../views/CourseCategoryView.vue";
import InformationView from "../components/CourseCategoryView/InformationView.vue";
import OrderView from "../components/CourseCategoryView/OrderView.vue";
import TranslationsView from "../components/CourseCategoryView/TranslationsView.vue";

import FormView from "../views/FormView.vue";
import FormInformation from "../components/Form/Information.vue";
import Order from "../components/Form/Order.vue";

export default [
    {
        path: '/admin/apps/course-category',
        component: HomeView,
        name: 'Home'
    },
    {
        path: '/admin/apps/course-category/:id',
        component: CourseCategoryView,
        name: "View",
        children: [
            {
                path: "/",
                component: InformationView,
                name: "View"
            },
            {
                path: "/translations",
                component: TranslationsView,
                name: "ViewTranslations"
            },
            {
                path: "/order",
                component: OrderView,
                name: "ViewOrder"
            },
        ]
    },
    {
        path: '/admin/apps/course-category/:id/form',// if id < 0, create
        component: FormView,
        // name: "CreateView",
        children: [
            {
                path: "/",
                component: FormInformation,
                name: "CreateView"
            },
            {
                path: "/order",
                component: Order,
                name: "CreateViewOrder"
            },
        ]
    }
];
