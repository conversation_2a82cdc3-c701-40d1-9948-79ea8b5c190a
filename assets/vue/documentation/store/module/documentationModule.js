import {make} from "vuex-pathify";
import axios from "axios";

const state = {
    loading: false,
    items: [],
    pagination: {
        page: 1,
        totalItems: 0
    },
    filters: {
        locale: ''
    }
};
const mutations = {
    ...make.mutations(state)
};
const getters = {
    ...make.getters(state)
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions: {
        async saveNewDocumentation({ commit }, formData) {
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const result = await axios.post('/admin/documentation', formData, { headers });
            return result.data;
        },

        async updateDocumentation({ commit }, {id, formData}) {
            const headers = {
                'Content-Type': 'multipart/form-data'
            };
            const result = await axios.post(`/admin/documentation/${id}`, formData, { headers });
            return result.data;
        },

        async getDocumentationItems({ commit, getters }) {
            commit('SET_LOADING', true);
            try {
                const { page } = getters['pagination'];
                const { locale } = getters['filters'];
                const url = new URL(window.location.origin + `/admin/documentation/documents/${page}`);
                if (locale) {
                    url.searchParams.set('locale', locale);
                }
                const result = await axios.get(url.toString());
                const { data, error } = result.data;
                commit('SET_ITEMS', data.items);
                commit('SET_PAGINATION', {
                    page,
                    totalItems: data['total-items']
                });
            } finally {
                commit('SET_LOADING', false);
            }
        },

        async deleteDocumentation({ commit, dispatch }, id) {
            const result = await axios.delete(`/admin/documentation/${id}`);
            const { error } = result.data;
            if (!error) dispatch('getDocumentationItems')
            return result.data;
        },

        async getDocumentation({ commit }, id) {
            const result = await axios.get(`/admin/documentation/${id}`);
            
            return result.data;
        }
    }
}
