export class Translation {
  constructor({
    id,
    name,
    typeCourse,
    category,
    open,
    totalChapters,
    thumbnailUrl,
    locale,
    code,
    active,
    createdAt,
  }) {
    this.id = id ?? null;
    this.name = name ?? null;
    this.typeCourse = typeCourse ?? null;
    this.category = category ?? null;
    this.open = open ?? false;
    this.totalChapters = totalChapters ?? 0;
    this.thumbnailUrl = thumbnailUrl ?? null;
    this.locale = locale ?? null;
    this.code = code ?? null;
    this.active = active ?? null;
    this.createdAt = createdAt ?? null;
  }
  static fromArray(arr) {
    if (!Array.isArray(arr)) return [];
    return arr.map((item) => new Translation(item));
  }
}
