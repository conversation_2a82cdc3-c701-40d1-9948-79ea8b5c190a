<template>
  <div class="stat-box">
    <div class="icon" :style="{ backgroundColor: color }">
      <i :class="icon"></i>
    </div>
    <div class="content">
      <div class="number" :style="{ color: color }">{{ value }}</div>
      <div class="description">{{ description }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "StatBox",
  props: {
    icon: {
      type: String,
      required: true, 
    },
    value: {
      type: [String, Number],
      required: true,
      default: 0,
    },
    description: {
      type: String,
      required: true,
    },
    color: {
      type: String,
      default: "#ccc",
    },
  },
};
</script>

<style scoped lang="scss">
.stat-box {
  display: flex;
  align-items: stretch;
  height: 100%;
}

.icon {
  width: 70px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  color: #fff;
  flex-shrink: 0;
  i {
    font-size: 2rem;
  }
}

.content {
  display: flex;
  flex-direction: column;
  width: 100%;
  justify-content: space-between;
}

.number {
  font-size: 2rem;
  font-weight: bold;
  padding: 0.25rem 0.5rem;
  word-break: break-word;
  text-align: right;
  height: 100%;
}

.description {
  font-size: 1rem;
  background: var(--color-neutral-lighter);
  padding: 0.5rem 0.75rem;
  text-align: right;
  font-weight: 600;
  border-radius: 0 0 4px 0;
  text-wrap: balance;
  height: 100%;
  max-height: 4rem;
}
</style>
