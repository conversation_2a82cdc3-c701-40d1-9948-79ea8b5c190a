import axios from "axios";
const REFRESH_TOKEN_ENDPOINT = '/api/token/refresh';

export function setToken(token) {
    document.cookie = `token=${token};Secure`;
}

export function deleteToken() {
    document.cookie = `token=`;
}

export function setRefreshToken(token) {
    document.cookie = `refresh-token=${token};Secure`;
}

export function deleteRefreshToken() {
    document.cookie = `refresh-token=`;
}

export function getToken(name = 'token') {
    return getCookie(name)
}

export function getRefreshToken(name = 'refresh-token') {
    return getCookie(name);
}

export function getCookie(name) {
    let cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].split('=');
        if (name === cookie[0].trim()) {
            const result = cookie[cookie.length - 1];
            return result.length > 0 ? result : null;
        }
    }
    return null;
}

export function setup({
    onRefreshFailed = () => {}
}) {
    var listeners = [];
    var isFetchingRefreshToken = false;

    function addListener(callback) {
        listeners.push(callback);
    }

    function onAccessTokenFetched(accessToken = null) {
        listeners = listeners.filter(callback => callback(accessToken));
    }

    axios.interceptors.request.use(
        async ( config ) => {
            let t = getToken();
            if (t && t.length > 0) config.headers['Authorization'] = `Bearer ${t}`;
            return config;
        },
        (error) => {
            return Promise.reject(error);
        }
    )

    axios.interceptors.response.use(
        (res) => (res),
        async (err) => {
            const { config, response: { status }} = err;
            const originalRequest = config;
            const currentUrl = window.location.origin;

            if (status !== 401 ) return Promise.reject(err);
            if (originalRequest.url.includes('login')) return Promise.reject(err);
            if (originalRequest.url.includes(REFRESH_TOKEN_ENDPOINT)) {
                deleteToken();
                deleteRefreshToken();
                onAccessTokenFetched(null);
                onRefreshFailed();
                return Promise.reject(err);
            }

            if (!isFetchingRefreshToken) {
                isFetchingRefreshToken = true;
                const refreshToken = getRefreshToken();
                if (!refreshToken) {
                    deleteToken();
                    deleteRefreshToken();
                    isFetchingRefreshToken = false;
                    return Promise.reject();
                }

                axios.post(REFRESH_TOKEN_ENDPOINT, {
                    refresh_token: refreshToken
                }).then(res => {
                    const t = res.token;
                    const rt = res.refresh_token;
                    setToken(t);
                    setRefreshToken(rt);
                    isFetchingRefreshToken = false;
                    onAccessTokenFetched(t);
                })
            }

            return new Promise((resolve) => {
                addListener((accessToken = null) => {
                    if (accessToken != null && accessToken.length > 0) {
                        originalRequest.headers.Authorization = `Bearer ${accessToken}`;
                    } else {
                        delete originalRequest.headers.Authorization;
                    }
                    resolve(axios(originalRequest));
                })
            })
        }
    )
}
