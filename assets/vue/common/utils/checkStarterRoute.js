function getUrlObject() {
    return new URL(window.location.href);
}

/**
 *
 * @param url
 */
function refreshUrl(url) {
    window.history.replaceState({}, null, url)
}
function parseParams(params = {}) {
    return window.btoa(encodeURI(JSON.stringify(params)))
}

function getParams(params = '') {
    return JSON.parse(decodeURI(window.atob(params)))
}

export function setBrowserHistory(history = [], refresh = true) {
    const url = getUrlObject();
    url.searchParams.set('routeHistory', parseParams(history))
    if (refresh) refreshUrl(url);
}

export function setBrowserStatus(status = [], refresh = true) {
    const url = getUrlObject();
    url.searchParams.set('routeStatus', parseParams(status));
    if (refresh) refreshUrl(url);
}

export function setActiveRoute(route, refresh = true) {
    const url = getUrlObject();
    url.searchParams.set('activeRoute', route.name);
    url.searchParams.set('activeParams', parseParams(route.params));
    if (refresh) refreshUrl(url);
}

/**
 * Go back in router history
 * @param router
 * @param store
 */
export function goBack(router, store) {
    const history = store.getters['routerModule/getHistory'];
    const destination = history.pop();
    store.dispatch('routerModule/setHistory', history);
    if (destination !== undefined) {
        router.replace(destination);
    }
}

/**
 * Only called when the application is created
 * @param el
 * @param router
 * @param store
 * @param defaultRouteName
 */
export function initRoute(el, router, store, defaultRouteName = 'Home') {
    const url = getUrlObject();
    let name = defaultRouteName;
    let params = [];
    let vueRoute = null;

    if (el.hasAttribute('activeRoute')) {
        name = el.attributes['activeRoute'].value;
    } else if (url.searchParams.has('activeRoute')) {
        name = url.searchParams.get('activeRoute')
    }

    if (el.hasAttribute('activeParams')) {
        const value = el.attributes['activeParams'].value;
        if (value !== undefined && value.trim().length > 0) {
            params = getParams(el.attributes['activeParams'].value);
            if ('vueRoute' in params) {
                vueRoute = params.vueRoute;
                delete params.vueRoute;
            }
        }
    }
    if ((params == null || params.length === 0) && url.searchParams.has('activeParams')) {// Make sure to check whether the url contains the params
        params = getParams(url.searchParams.get('activeParams'));
    }


    let routeStatus = [];
    if (el.hasAttribute('routeStatus')) {
        const value = el.attributes['routeStatus'].value;
        if (value !== undefined && value.trim().length > 0) {
            try {
                routeStatus = getParams(el.attributes['routeStatus'].value);
            } catch (e) {
                console.log('Failed to decode. Reset route status');
                routeStatus = [];
            }
        }
    }
    if ((routeStatus == null || routeStatus.length === 0) && url.searchParams.has('routeStatus')) {
        routeStatus = getParams(url.searchParams.get('routeStatus'));
    }

    // Update route
    url.searchParams.set('activeRoute', name);
    url.searchParams.set('activeParams', parseParams(params));
    url.searchParams.set('routeStatus', parseParams(routeStatus));
    refreshUrl(url);

    // Load start route
    if (vueRoute !== null && vueRoute.length > 0) router.replace(vueRoute);
    else router.replace({ name, params });

    // Load route status
    store.dispatch('contentTitleModule/setContentTitle', routeStatus);

    let routeHistory = [];
    if (el.hasAttribute('routeHistory')) {
        const value = el.attributes['routeHistory'].value
        if (value !== undefined && value.trim().length > 0) {
            routeHistory = getParams(value);
        }
    }

    if ((routeHistory == null || routeHistory.length === 0) && url.searchParams.has('routeHistory')) {
        routeHistory = getParams(url.searchParams.get('routeHistory'));
    }
    store.dispatch('routerModule/setHistory', routeHistory);
}

/**
 * Get current browser link and add current active route from vue-router.
 * Convert params to JSON and encode to base64 and append the data to the url.
 * Is required for the back to read and decode those params for the app usage.
 * @param route
 * @param status
 * @param from
 * @deprecated
 */
export function setBrowserUrl(route, status = [], from = null) {
    const url = new URL(window.location.href);
    url.searchParams.set('starterRoute', route.name);
    const params = route.params;

    // Convert params to JSON and encode to base64 then append params to url
    url.searchParams.set('encodedParams', parseParams(params));

    // Set current route status
    url.searchParams.set('routeStatus', parseParams(status))

    if (from != null && from.name != null) {
        let history = [];
        if (url.searchParams.has('routeHistory')) {
            history = getParams(url.searchParams.get('routeHistory'));
        }
        history.push({
            name: from.name,
            params: from.params
        });

        url.searchParams.set('routeHistory', parseParams(history));
    }

    window.history.replaceState({}, null, url)
}

/**
 * Read previously encoded params passed to the main element as attributes
 * @param el
 * @param router
 * @param store
 * @param defaultRouteName Default route where the app starts, the name between apps can be different
 * @deprecated
 */
function checkStarterRoute(el, router, store, defaultRouteName = 'Home') {
    // Get current url
    const url = new URL(window.location.href);

    let params = null;
    let routeName = defaultRouteName;

    if (el.hasAttribute('starterRoute')) {
        routeName = el.attributes['starterRoute'].value;
    } else if (url.searchParams.has('starterRoute')) {
        // If no parameter has been passed to main object
        routeName = url.searchParams.get('starterRoute');
    }

    if (el.hasAttribute('routeParams')) {
        params = JSON.parse(el.attributes['routeParams'].value);
    } else if (url.searchParams.has('encodedParams')) {
        params = getParams(url.searchParams.get('encodedParams'));
    }

    router.replace({ name: routeName, params });

    if (el.hasAttribute('routeStatus')) {
        const status = JSON.parse(el.attributes['routeStatus'].value);
        store.dispatch('contentTitleModule/setContentTitle', status);
    } else if (url.searchParams.has('routeStatus')) {
        // If status is not passed by parameters, find values in url
        const status = getParams(url.searchParams.get('routeStatus'));
        store.dispatch('contentTitleModule/setContentTitle', status);
    }
}

export default checkStarterRoute;
