<template>
  <div class="ButtonWithDescription">
    <div class="action">
      <input type="hidden" :value="innerValue" :name="name" />
      <button
        type="button"
        :class="
          (innerValue && !disabled ? 'enabled' : '') +
          ` ${extraClass}` +
          ` size--${size}`
        "
        @click="buttonClick()"
        :disabled="disabled"
      >
        <img v-if="imageUrl" :src="imageUrl" />
        <i :class="icon" v-else></i>
      </button>
    </div>
    <div class="description">
      <div class="description--title">
        <div class="custom-control custom-switch">
          <input
            type="checkbox"
            class="custom-control-input"
            :id="'switch_' + name"
            v-model="innerValue"
            :disabled="disabled"
          />
          <label class="custom-control-label" :for="'switch_' + name"></label>
        </div>
        <span>{{ $t(title) }}</span>
      </div>
      <p>{{ $t(description) }}</p>
    </div>
    <span v-if="disabled && disabledMessage.length" class="tooltipMessage">{{ disabledMessage }}</span>
  </div>
</template>

<script>
export default {
  name: "ButtonWithDescription",
  props: {
    name: {
      type: String,
      default: null,
    },
    icon: {
      type: String,
      default: "fa fa-thumbs-up",
    },
    title: {
      type: String,
      default: "ACTION_TITLE",
    },
    description: {
      type: String,
      default: "ACTION_DESCRIPTION",
    },

    extraClass: {
      type: String,
      default: "",
    },

    value: {
      type: Boolean,
    },

    imageUrl: {
      type: String,
      default: null,
    },

    disabled: {
      type: Boolean,
      default: false,
    },

    size: {
      type: String,
      default: "m",
      validator: (size) => ["xs", "s", "m", "l"].includes(size),
    },
    
    disabledMessage: {
      type: String,
      default: ''
    }
  },
  computed: {
    innerValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit("input", newValue);
      },
    },
  },
  methods: {
    buttonClick() {
      if (!this.disabled) {
        this.$emit("input", !this.value);
      }
    },
  },
};
</script>

 <style scoped lang="scss"> 
.ButtonWithDescription {
  width: 100%;
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 1rem;
  padding: 0.3rem;
  position: relative;

  .action {
    button {
      background-color: #cbd5e1;
      border: none;
      border-radius: 5px;
      font-size: 30px;
      color: #ffffff;
      &.enabled {
        background-color: var(--color-primary);
      }
      img {
        width: 80%;
        height: 80%;
      }

      &.size {
        &--xs {
          width: 30px;
          height: 30px;
          font-size: 15px;
        }
        &--s {
          width: 50px;
          height: 50px;
          font-size: 20px;
        }
        &--m {
          width: 80px;
          height: 80px;
          font-size: 30px;
        }
        &--l {
          width: 100px;
          height: 100px;
          font-size: 40px;
        }
      }
    }
  }
  .description {
    display: flex;
    flex-flow: column;
    flex-grow: 1;

    &--title {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: flex-start;
      font-weight: bold;

      input {
        cursor: pointer;

        &:checked + label::before {
          background-color: var(--color-primary);
          border: #cbd5e1;
        }
      }

      span {
        font-size: 16px;
        text-align: left;
      }
    }
  }
  
  .tooltipMessage {
    display: none;
    font-size: 0.8rem;
    background-color: $color-primary-lighter;
    position: absolute;
    padding: 0.25rem 0.75rem;
    border-radius: 0.3rem;
    top: -1.5rem;
    z-index: 1;
  }
  
  .description:hover + .tooltipMessage {
    display: initial;
  }
}
</style>
