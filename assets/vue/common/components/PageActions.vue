<template>
  <div class="PageActions">
    <button v-for="action in actions.actions"
            v-bind="action.bind ?? {}"
            :type="action.type ?? 'button'"
            :class="action.class ?? 'btn btn-default'"
            @click="click(action)"
    ><i v-if="action.icon" :class="action.icon"></i> {{ action.name }}</button>
  </div>
</template>

<script>
export default {
  name: "PageActions",
  computed: {
    actions() {
      return this.$store.getters['contentTitleModule/getActions'];
    },
    useGlobalEventBus() {
      return this.$store.getters['contentTitleModule/getUseGlobalEventBus'];
    }
  },

  created() {
    this.handleRouteParams();
  },

  watch: {
    $route(to, from) {
      this.handleRouteParams();
    }
  },
  methods: {
    handleRouteParams() {
      if (this.actions.route !== this.$route.name) {
        this.$store.dispatch('contentTitleModule/clearActions');
      }
    },

    click(action) {
      if (this.useGlobalEventBus) {
        this.$eventBus.$emit(action.event)
      } else {
        this.$emit(action.event);
      }
    }
  }
}
</script>

 <style scoped lang="scss"> 

</style>
