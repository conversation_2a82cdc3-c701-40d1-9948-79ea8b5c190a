<script>

import $ from "j<PERSON>y";
import LabelWithInfo from "../ui/LabelWithInfo.vue";

export default {
  name: "FileAsText",
  components: {LabelWithInfo},
  props: {
    id: {
      type: String|Number,
      required: true
    },
    name: {
      type: String,
      default: 'file'
    },
    label: {
      type: String,
      default: null
    },
    labelInfo: {
      type: String,
      default: null
    },
    required: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object|File,
      default: null
    },
    multiple: {
      type: Boolean,
      default: false
    },
    labelWithInfo: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    withInput: {
      type: Boolean,
      default: true
    },
    labelDescriptionPosition: {
      type: String,
      default: 'right'
    },
    deleteBtn: {
      type: Boolean,
      default: false
    },
    errorMsg: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      error: ''
    };
  },
  computed: {
    innerValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('input', newValue);
      }
    },
    hasUrl() {
      return this.value?.url != null;
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler: function () {
        this.setNameIntoInput();
      }
    },
    errorMsg(val, oldVal) {
      if (val !== oldVal) {
        this.error = this.errorMsg;
      }
    }
  },
  mounted() {
    if (this.value) {
      this.setNameIntoInput();
    }
  },
  methods: {
    setNameIntoInput() {
      if( this.value && 'name' in this.value) $(`#${this.id} input[type="text"]`).val(this.value.name);
      else $(`#${this.id} input[type="text"]`).val("");
    },
    handleFileChange(input) {
      if (input.files.length > 0) {
        if (this.multiple) this.innerValue = input.files;
        else {
          this.innerValue = input.files[0];
        }
      }
    },
    openFileSelector() {
      if (this.disabled) return;
      if (!this.withInput) {
        this.$emit('click');
        return;
      }
      this.error = null;
      $(`#${this.id}-file`).click();
    },
    resetFileSelector() {
      $(`#${this.id}-file`).val('');
      $(`#${this.id} input[type="text"]`).val('')
      this.innerValue = null;
      this.error = null;
    }
  }
}
</script>

<template>
  <div class="form-group FileAsText" :id="id">
    <label-with-info v-if="labelWithInfo" :info="labelInfo" :location="labelDescriptionPosition">
      {{ label }}
    </label-with-info>
    <label v-else-if="label"><slot name="label-left"></slot>{{ label }}<slot name="label-right"></slot></label>
    <input :disabled="disabled"
           v-if="withInput"
           type="file" :id="`${id}-file`" :name="name"  :required="required"
           class="form-control" @change="handleFileChange($event.target)" accept="application/pdf">
    <div class="file-selector-display-name-container" :class="deleteBtn ? 'delete' : ''">
      <input type="text" class="form-control" :disabled="disabled" readonly :id="`FileAsText-${id}-text`" @click="openFileSelector()">
      <i class="fa fa-upload" @click="openFileSelector()"></i>
      <button v-if="deleteBtn" type="button" class="btn btn-sm btn-danger" @click="resetFileSelector()"><i class="fa fa-trash"></i></button>
    </div>
    <div v-if="this.error" class="errorMsg"><i class="fa fa-exclamation"></i>{{ errorMsg }}</div>
    <a v-if="hasUrl" target="_blank" :href="value.url">{{ value.name }}</a>
  </div>
</template>

<style scoped lang="scss">
.FileAsText {
  position: relative;

  .errorMsg {
    border: 1px solid #212121;
    padding: 5px;
    border-radius: 5px;
    position: absolute;
    background-color: #FFFFFF;
    left: 40%;
    bottom: -43%;
    z-index: 20;
    i {
      margin-right: 5px;
      color: #FFFFFF;
      background-color: var(--color-warning);
      padding: 5px 10px 5px 10px;
      font-size: 14px;
    }

    &:after, &:before {
      content: "";
      position: absolute;
      width: 0;
      height: 0;
      border: 1px solid #212121;
    }

    &:after, &:before {
      left: 10%;
      bottom: 100%;
    }
    &:after {
      border-color: transparent transparent #ffffff transparent ;
      margin-left: -5px;
      border-width: 9px;
    }
    &:before {
      border-color: transparent transparent #212121 transparent ;
      margin-left: -6px;
      border-width: 10px;
    }
  }
  input[type="file"] {
    display: none;
  }

  & > div {
  }

  .file-selector-display-name-container {
    position: relative;

    input {
      padding-right: 20px;
    }

    i.fa-upload {
      position: absolute;
      right: 4px;
      top: 25%;
      cursor: pointer;
    }

    button {
      display: none;
      width: 30px;
    }

    &.delete {
      display: grid;
      grid-template-columns: 1fr 30px;
      gap: .3rem;
      button {
        display: block;
      }

      i.fa-upload {
        right: 36px;
      }
    }

  }
}
</style>
