<template>
  <div class="OfficeViewer">
    <vue-doc-preview :url="url" type="office"/>
  </div>
</template>

<script>
import VueDocPreview from 'vue-doc-preview';
import ViewerModal from "./ViewerModal.vue";

export default {
  name: "OfficeViewer",
  components: {ViewerModal, VueDocPreview},
  props: {
    id: {
      type: String|Number,
      default: 'office'
    },

    src: {
      type: String,
      required: true
    }
  },
  computed: {
    url() {
      const domain = window.location.origin;
      const url = `${domain}/${this.src}`;
      console.log(url)
      return url;
    }
  },
  watch: {
    src: {
      handler:function (val, oldVal) {
        console.log(this.src)
      },
      immediate: true
    }
  }
}
</script>

 <style scoped lang="scss"> 
.OfficeViewer {

}
</style>
