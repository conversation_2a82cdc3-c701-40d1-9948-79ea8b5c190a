<template>
  <div class="Forum" :class="showThreads ? 'threads-enabled' : 'threads-disabled'">
    <forum-threads :threads="threads"
                   :show-actions="showThreadActions"
                   :loading-messages="loadingMessages"
                   :loading-threads="loadingThreads"
                   @active="onActive"
                   @update="$emit('update-thread', $event)"
                   @delete="$emit('delete-thread', $event)"
                   @create="$emit('create')"
                   v-if="showThreads"
    />
    <forum-content :thread="active"
                   :loading-messages="loadingMessages"
                   :show-thread-actions="showThreadActions"
                   :show-message-actions="showMessageActions"
                   :show-header="showHeader"
                   :show-footer="showFooter"
                   @update="$emit('update-thread', $event)"
                   @delete="$emit('delete-thread', $event)"
                   @reply="$emit('reply', $event)"
                   v-model="value"
    >
      <template v-slot:top-content>
        <slot name="top-content" />
      </template>
    </forum-content>
  </div>
</template>

<script>
import ForumContent from "./ForumContent.vue";
import ForumThreads from "./ForumThreads.vue";

export default {
  name: "Forum",
  components: {ForumContent, ForumThreads},
  props: {
    showHeader: {
      type: Boolean,
      default: true
    },

    showFooter: {
      type: Boolean,
      default: true
    },

    showThreads: {
      type: Boolean,
      default: true
    },

    showThreadActions: {
      type: Boolean,
      default: true
    },

    showMessageActions: {
      type: Boolean,
      default: true
    },

    /**
     * [{title: '', id: ''}]
     */
    active: {
      type: Object,
      default: () => ({})
    },
    threads: {
      type: Object|Array,
      default: () => []
    },

    messages: {
      type: Object|Array,
      default: () => []
    },

    loadingMessages: {
      type: Boolean,
      default: false
    },

    loadingThreads: {
      type: Boolean,
      default: false
    },

    value: {
      type: Array|Object,
      default: () => ([])
    }
  },
  methods: {
    onActive(active) {
      this.$emit('active', active)
    }
  },
}
</script>

 <style scoped lang="scss"> 
.Forum {
  display: flex;
  width: 100%;
  padding: 1rem;

  &.threads-enabled {
    display: grid;
    grid-template-columns: [threads] 300px [content] auto;

    @media (max-width: 950px) {
      grid-template-columns: auto;
    }
  }
}
</style>
