import {make} from "vuex-pathify";
import axios from "axios";

const state = {
    /** @var globalRoute Api endpoint where the time will be sent. Null when the global time is not required to be saved */
    globalRoute: null,
    /** @var localRoute Api endpoint where the time will be sent. Null when the time is not required to be saved */
    localRoute: null,

    /** by basic, we can use the payload to see differences between one route/component and others **/
    globalPayload: null,
    localPayload: null,
    intervalId: null,
    sendIntervalId: null,
    /** Keep time in global manner **/
    globalTime: 0,
    /** Keep time in local manner: example: a component **/
    localTime: 0,
};

export const getters = {
    ...make.getters(state)
};

const mutations = {
    ...make.mutations(state),

    increaseLocalTime(state) {
        state.localTime += 1;
        state.globalTime += 1;
    },

    RESET_GLOBAL(state) {
        state.globalPayload = null;
        state.globalTime = 0;
    },

    RESET_LOCAL(state) {
        state.localPayload = null;
        state.localTime = 0;
    },
};

export const actions = {
    setRoutes({ commit }, { global, local }) {
        commit('SET_GLOBAL_ROUTE', global);
        commit('SET_LOCAL_ROUTE', local);
    },

    reset({ commit, getters, dispatch }) {
        const { intervalId, sendIntervalId } = getters;
        if (intervalId != null) clearInterval(intervalId);
        if (sendIntervalId != null) clearInterval(sendIntervalId);

        dispatch('sendLocalTime');// When saving per component information
        dispatch('sendGlobalTime');// When saving global information

        commit('SET_INTERVAL_ID', null);
        commit('SET_SEND_INTERVAL_ID', null);
        commit('RESET_LOCAL');
        commit('RESET_GLOBAL');
    },

    setGlobalPayload({ commit, getters, dispatch }, payload) {
        const { globalPayload } = getters;
        if (globalPayload != null) {
            const p1 = JSON.stringify(payload);
            const p2 = JSON.stringify(globalPayload);
            if (p1 !== p2) {
                /** Global has changed, save and replace **/
                dispatch('sendLocalTime');// When saving per component information
                dispatch('sendGlobalTime');// When saving global information

                commit('RESET_LOCAL');
                commit('RESET_GLOBAL');
            }
        }
        commit('SET_GLOBAL_PAYLOAD', payload);

        dispatch('startSendInterval');
    },

    setLocalPayload({ commit, getters, dispatch }, payload ) {
        const { localPayload, intervalId } = getters;
        if (localPayload == null) commit('RESET_LOCAL');
        else {
            const p1 = JSON.stringify(localPayload);
            const p2 = JSON.stringify(payload);
            if (p1 !== p2) {
                /**
                 * Increase global counter and reset local counter
                 */
                dispatch('sendLocalTime');
            }
        }

        if (intervalId != null) clearInterval(intervalId);

        commit('SET_LOCAL_PAYLOAD', payload);

        let i1 = setInterval(function () {
            commit('increaseLocalTime');
        }, 1000);

        commit('SET_INTERVAL_ID', i1);
    },

    startSendInterval({ commit, getters, dispatch }) {
        const { sendIntervalId } = getters;
        if (sendIntervalId != null) clearInterval(sendIntervalId);

        const repeatTime = 1000 * 60 * 5;// 5 minutes
        const i1 = setInterval(function () {
            dispatch('sendLocalTime');
            dispatch('sendGlobalTime');
        }, repeatTime)

        commit('SET_SEND_INTERVAL_ID', i1);
    },

    async sendLocalTime({ commit, getters }) {
        const { localRoute, localTime, localPayload } = getters;
        if (localPayload == null || localRoute == null) return;

        if ('send' in localPayload && localPayload.send === false) return;

        try {
            await axios.post(localRoute, { ...localPayload, time: localTime });
            commit('SET_LOCAL_TIME', 0);
        } finally {

        }
    },

    async sendGlobalTime({ commit, getters}) {
        const { globalRoute, globalTime, globalPayload } = getters;
        if (globalPayload == null || globalRoute == null) return;
        if ('send' in globalPayload && globalPayload.send === false) return;// Keep the counter but avoid sending to backend

        try {
            await axios.post(globalRoute, { ...globalPayload, time: globalTime });
            commit('SET_GLOBAL_TIME', 0);
        } finally {

        }
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
}
