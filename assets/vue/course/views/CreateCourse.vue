<template>
  <div
    class="d-flex flex-column align-items-center justify-content-center"
    v-if="loading || loadingCourse"
  >
    <loader :is-loaded="loading || loadingCourse"></loader>
    <span>{{ $t("LOADING") }}</span>
  </div>
  <div class="CreateCourse" v-else>
    <step-form
      id="new-course-form"
      :number-of-steps="numberOfSteps"
      :current-step="currentStepNumber"
      :steps-custom-titles="customTitles"
      @next="next()"
      @prev="prev()"
      @on-step-change="onCurrentStepChange"
      @submit="submit()"
    >
      <template v-slot:form-content>
        <div
          class="form-step basic-info"
          :class="currentStep === 'basic-info' ? 'd-flex' : ''"
          v-show="currentStep === 'basic-info'"
        >
          <div class="basic-info--data">
            <file-selector
              name="thumbnail"
              :defaultImage="imageUrl"
              preview-default-class="limited-size centered"
            ></file-selector>
            <div
              class="form__basic-info__data d-flex flex-row flex-wrap align-items-start"
            >
              <div class="w-100 form-group required">
                <label for="code">{{ $t("CODE") }}</label>
                <input
                  id="code"
                  name="code"
                  type="text"
                  class="form-control"
                  required
                  v-model="code"
                />
              </div>
              <div class="w-100 form-group required">
                <label for="name">{{ $t("NAME") }}</label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  class="form-control"
                  required
                  v-model="name"
                />
              </div>
              <div class="w-100 locale-selector" v-if="multilingual">
                <locale-selector
                  :locale="locale"
                  @set-locale="locale = $event"
                ></locale-selector>
              </div>

              <div class="form-group col-xs-12 col-md-6">
                <label for="typeCourse">{{ $t("COURSE.TYPE_COURSE") }}</label>
                <select
                  class="custom-select"
                  id="typeCourse"
                  name="typeCourse"
                  v-model="typeCourse_id"
                  v-bind="{ disabled: $route.name === 'UpdateCourse' }"
                >
                  <option disabled :value="null">{{ $t("SELECT") }}</option>
                  <option
                    v-for="typeCourse in typesCourse"
                    :key="typeCourse.id"
                    :value="typeCourse.id"
                  >
                    {{ typeCourse.name }}
                  </option>
                </select>
              </div>

              <div class="form-group col-xs-12 col-md-6 required">
                <label for="category">{{ $t("CATEGORY") }}</label>
                <select
                  class="custom-select"
                  id="category"
                  name="category"
                  v-model="category_id"
                  required
                >
                  <option disabled :value="null">{{ $t("SELECT") }}</option>
                  <option
                    v-for="category in categoriesByTypeCourse"
                    :key="category.id"
                    :value="category.id"
                  >
                    {{ category.name }}
                  </option>
                </select>
              </div>

              <div
                class="form-group col-xs-12 col-md-6"
                v-if="typeCourse_id === 1 && surveysCourse.length > 1"
              >
                <label for="surverCourse">{{
                  $t("ANNOUNCEMENT.FORM.STEPS.SURVEY")
                }}</label>
                <select
                  class="custom-select"
                  id="survey"
                  name="survey"
                  v-model="survey_id"
                >
                  <option disabled :value="null">{{ $t("SELECT") }}</option>
                  <option
                    v-for="surveyCourse in surveysCourse"
                    :key="surveyCourse.id"
                    :value="surveyCourse.id"
                  >
                    {{ surveyCourse.name }}
                  </option>
                </select>
              </div>

              <div
                class="form-group col-xs-12 col-md-6"
                v-if="typeCourse_id === 1 && typeDiplomas.length > 1"
              >
                <label for="typeDiploma">{{
                  $t("ANNOUNCEMENT.FORM.STEPS.CERTIFICATE")
                }}</label>
                <select
                  class="custom-select"
                  id="TypeDiploma"
                  name="TypeDiploma"
                  v-model="typeDiploma_id"
                >
                  <option disabled :value="null">
                    {{ $t("ANNOUNCEMENT.FORM.ENTITY.CERTIFICATE_SELECT") }}
                  </option>
                  <option
                    v-for="typeDiploma in typeDiplomas"
                    :key="typeDiploma.id"
                    :value="typeDiploma.id"
                  >
                    {{ typeDiploma.name }}
                  </option>
                </select>
              </div>

              <div class="form-group col-xs-12 col-md-6" v-if="setCourseLevel">
                <label for="category">{{ $t("COURSE.LEVEL") }}</label>
                <select
                  class="custom-select"
                  id="level"
                  name="level"
                  v-model="level_id"
                >
                  <option value="">{{ $t("SELECT") }}</option>
                  <option
                    v-for="level in levels"
                    :key="level.id"
                    :value="level.id"
                  >
                    {{ level.name }}
                  </option>
                </select>
              </div>
              <div
                class="form-group col-xs-12 col-md-6"
                v-if="setCoursePoints && typeCourse_id == 1"
              >
                <label for="points">{{ $t("COURSE.SCORE") }}</label>
                <input
                  id="points"
                  class="form-control"
                  name="points"
                  type="number"
                  min="100"
                  v-model="points"
                  @input="validatePoints"
                />
              </div>
            </div>
          </div>
          <div class="diploma-data" v-if="diplomaIndexAllow && typeCourse_id == 1">
            <h5 class="text-left"><strong>{{ $t("ANNOUNCEMENT.DIPLOMAS_STUDENT") }}</strong></h5>
            <div class="row">
              <div class="col-1">
                <div class="box-icon">
                    <i class="fas fa-file"></i>
                  </div>
              </div>
              <div class="col-5">
                <div class="custom-control custom-switch">
                  <input type="checkbox" class="custom-control-input" id="isContentDiploma"
                     v-model="isContentDiploma">
                  <label class="custom-control-label" for="isContentDiploma"><strong>{{ $t("INCLUDE_COURSE_DIPLOMA_INDEX") }}</strong></label>
                </div>
                <div>
                  <p>{{ $t("INCLUDE_COURSE_DIPLOMA_INDEX_DESCRIPTION") }}</p>
                </div>
              </div>
              <div class="col-6" v-show="isContentDiploma">
                <label for="category"><strong>{{ $t("COURSE_DIPLOMA_INDEX") }}</strong></label>
                <select
                  class="custom-select"
                  id="diploma_select"
                  name="diploma_select"
                  v-model="diploma_index"
                >
                  <option :value="typeIndexDiploma[0]">{{ $t("REPORTS_DIPLOMA_SELECT_DEFAULT") }}</option>
                  <option :value="typeIndexDiploma[1]">{{ $t("REPORTS_DIPLOMA_SELECT_MANUAL") }}</option>
                </select>
              </div>
            </div>
            <div style="margin-left: 8%; margin-top: 4%;">
              <h6><strong>{{ $t("COURSE_DIPLOMA_INDEX_DEFAULT_TITLE") }}</strong></h6>
              <p>{{ $t("COURSE_DIPLOMA_INDEX_DEFAULT_DESCRIPTION") }}</p>
            </div>
          </div>
          <div class="basic-info--description" v-show="diploma_index == 'MANUAL' && diplomaIndexAllow && typeCourse_id == 1">
            <div class="form-group w-100">
              <label>{{ $t("DESCRIPTION_MANUAL_INDEX") }}</label>
              <froala
                :tag="`textarea`"
                v-model="descriptionContentDiploma"
                :config="froalaDescriptionConfigIndexDiploma"
              ></froala>
            </div>
          </div>
          <div class="basic-info--tags">
            <tags
              @tags-updated="tags = $event"
              :prop-tags="tags"
              auto-complete-url="/admin/courses/available-tags"
            ></tags>
          </div>
          <div class="basic-info--segments" v-if="useSegment">
            <add-remove
              :source-items="segments"
              :realtime="false"
              :title="$t('COURSE.SEGMENT') + ''"
              :enable-all="true"
              v-model="selectedSegments"
              :loading-source="false"
              :loading-selected="false"
            />
          </div>
          <div class="basic-info--description">
            <div class="form-group w-100">
              <label>{{ $t("COURSE.DESCRIPTION") }}</label>
              <froala
                :tag="`textarea`"
                v-model="description"
                :config="froalaDescriptionConfig"
              ></froala>
            </div>
          </div>
        </div>
        <div
          class="form-step documentation"
          :class="currentStep === 'documentation' ? 'd-flex' : ''"
          v-show="currentStep === 'documentation'"
        >
          <div class="col-12 form-group">
            <froala
              :tag="`textarea`"
              v-model="documentation"
              :config="froalaDocumentationConfig"
            ></froala>
          </div>
        </div>
        <div
          class="form-step general-info"
          :class="currentStep === 'general-info' ? 'd-flex' : ''"
          v-show="currentStep === 'general-info'"
        >
          <div class="col-12 form-group" v-if="courseDocumentation">
            <froala
              :tag="`textarea`"
              v-model="generalInformation"
              :config="froalaGeneralDocumentationConfig"
            ></froala>
          </div>
          <div class="enableCourseStatus">
            <button-with-description
              title="COURSE.BTN_ACTIVE.TITLE"
              description="COURSE.BTN_ACTIVE.DESCRIPTION"
              name="active"
              v-model="active"
              :disabled="!canPublish"
            ></button-with-description>

            <button-with-description
              name="isNew"
              title="COURSE.BTN_IS_NEW.TITLE"
              description="COURSE.BTN_IS_NEW.DESCRIPTION"
              v-model="isNew"
            ></button-with-description>

            <button-with-description
              title="COURSE.BTN_OPEN.TITLE"
              description="COURSE.BTN_OPEN.DESCRIPTION"
              v-model="open"
              name="open"
            ></button-with-description>

            <button-with-description
              name="open_visible"
              title="COURSE.BTN_OPEN_CAMPUS.TITLE"
              description="COURSE.BTN_OPEN_CAMPUS.DESCRIPTION"
              v-model="open_visible"
            ></button-with-description>
          </div>
          <div class="col-12 mt-3 audience" v-if="open && filtersEnabled">
            <div class="col-12 audience-header">
              <h1>{{ $t("COURSE.AUDIENCE.TITLE") }}</h1>
              <p v-html="$t('COURSE.AUDIENCE.DESCRIPTION')" />
            </div>
            <category-filter
              v-model="filters"
              :show-category-warning-status="true"
              :category-warning-status-text="
                $t('COURSE.AUDIENCE_WARNING_TEXT') + ''
              "
              :show-titles="true"
              :allow-all="true"
            />
          </div>

          <div class="col-12 mt-3 audience">
            <add-remove
              :source-items="availableManagers"
              :realtime="false"
              :title="$t('COURSE.MANAGERS') + ''"
              :enable-all="false"
              v-model="selectedManagers"
              :loading-source="false"
              :loading-selected="false"
              :fields="{ id: 'id', name: 'fullName' }"
            />
          </div>
        </div>
      </template>
    </step-form>
  </div>
</template>

<script>
import $ from "jquery";
import { get } from "vuex-pathify";

import AddRemoveItems from "../../common/components/AddRemoveItems.vue";
import ButtonWithDescription from "../../common/components/ButtonWithDescription.vue";
import CategoryFilter from "../../common/components/filter/CategoryFilter.vue";
import FileSelector from "../../common/components/FileSelector.vue";
import FormProgress from "../../common/components/FormProgress.vue";
import Loader from "../../admin/components/Loader.vue";
import LocaleSelector from "../../common/components/LocaleSelector.vue";
import Options from "../../admin/components/roulette-word/Options.vue";
import Tags from "../../common/components/Tags.vue";
import StepForm from "../../common/views/StepForm.vue";

import {
  getFiltersForRequest,
  generateCompatibleData,
} from "../../common/components/filter/CategoryFilter.vue";
import AddRemove from "../../common/components/select/AddRemove.vue";

export default {
  name: "CreateCourse",
  components: {
    AddRemove,
    StepForm,
    AddRemoveItems,
    ButtonWithDescription,
    CategoryFilter,
    FileSelector,
    FormProgress,
    Loader,
    LocaleSelector,
    Options,
    Tags,
  },
  $,
  data() {
    return {
      testFilters: null,
      name: "",
      code: "",
      category_id: "",
      selectedSegments: [],
      tags: [],
      description: "",
      generalInformation: "",
      documentation: "",
      active: false,
      open: false,
      open_visible: false, // Open Campus
      isNew: false,
      filters: [], //filters
      locale: "en",
      points: 0,
      level_id: "",
      selectedManagers: [],
      typesCourse: [],
      typeCourse_id: null,
      surveysCourse: [],
      survey_id: null,
      typeDiplomas: [],
      typeDiploma_id: null,

      currentStep: "basic-info",
      currentStepNumber: 1,
      categories: [],
      segments: [],
      levels: [],
      availableManagers: [],

      // Configuration parameters
      courseDocumentation: false,
      multilingual: false,
      setCourseLevel: false,
      setCoursePoints: false,
      useSegment: false,
      loadingCourse: false,
      imageUrl: "/assets/common/add_image_file.svg",

      managerCanPublish: false,
      isContentDiploma: false,
      typeIndexDiploma: [],
      diploma_index: 'DEFAULT',
      descriptionContentDiploma: '',
      diplomaIndexAllow: true
    };
  },
  computed: {
    loading: get("courseModule/loading"),
    config: get("configModule/config"),
    filtersEnabled: get("configModule/config@filtersEnabled"),
    defaultLocale() {
      return this.$store.getters["localeModule/getDefaultLocale"];
    },
    froalaKey() {
      return this.$store.getters["froalaEditorModule/getFroalaKey"];
    },

    froalaDescriptionConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],

        height: 150,
        pastePlain: true,
        pluginsEnabled: ["align", "charCounter"],
        toolbarButtons: {
          moreText: {
            buttons: ["bold", "italic", "underline"],
          },
        },
        charCounterCount: true,
        charCounterMax: 500,
      };
    },

    froalaDescriptionConfigIndexDiploma() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],

        height: 150,
        pastePlain: true,
        pluginsEnabled: ["align", "charCounter"],
        toolbarButtons: {
          moreText: {
            buttons: ["bold", "italic", "underline"],
          },
        },
        charCounterCount: true,
      };
    },

    froalaGeneralDocumentationConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 250,
        pluginsEnabled: [
          "align",
          "link",
          "url",
          "image",
          "lists",
          "file",
          "paragraphStyle",
          "paragraphFormat",
          "filesManager",
          "imageTUI",
          "video",
        ],
        toolbarButtons: {
          moreText: {
            buttons: ["bold", "italic", "underline"],
          },
          moreParagraph: {
            buttons: [
              "alignLeft",
              "alignCenter",
              "formatOLSimple",
              "alignRight",
              "alignJustify",
              "formatOL",
              "formatUL",
              "paragraphFormat",
              "paragraphStyle",
              "lineHeight",
              "outdent",
              "indent",
              "quote",
            ],
          },
          moreRich: {
            buttons: ["insertLink", "insertImage", "insertFile", "insertVideo"],
            buttonsVisible: 4,
          },
          moreMisc: {
            buttons: [],
          },
        },
      };
    },

    froalaDocumentationConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 500,
        pluginsEnabled: [
          "align",
          "link",
          "url",
          "image",
          "lists",
          "file",
          "paragraphStyle",
          "paragraphFormat",
          "imageTUI",
          "filesManager",
          "video",
        ],
      };
    },
    customTitles() {
      const map = new Map();
      map.set(1, "COURSE.FORM.STEP-1");
      map.set(2, "COURSE.FORM.LAST_STEP");

      return map;
    },

    numberOfSteps() {
      if (this.typeCourse_id === 2 || this.typeCourse_id === 4) return 1;
      if (this.typeCourse_id === 1 || this.typeCourse_id === 3) return 2;
      return 1;
    },

    routeName() {
      return this.$route.name;
    },

    canPublish() {
      const isAdmin = this.$store.getters["userModule/isAdmin"];
      const isManager = this.$store.getters["userModule/isManager"];
      const isManagerEditor = this.$store.getters["userModule/isManagerEditor"];

      if (isAdmin || isManagerEditor) return true;
      else return !!(isManager && this.managerCanPublish);
    },

    categoriesByTypeCourse() {
      return this.categories.filter((item) =>
        item.idTypesCourse.includes(this.typeCourse_id)
      );
    },
  },

  watch: {
    $route(to, from) {
      this.handleRouteParams();
    },
    typeCourse_id: {
      handler: function (val, oldVal) {
        if (val !== oldVal && this.$route?.name === "CreateCourse") {
          this.category_id = null;
          this.survey_id =
            this.surveysCourse.length === 1 && val === 1
              ? this.surveysCourse[0].id
              : null;
          this.typeDiploma_id =
            this.typeDiplomas.length === 1 && val === 1
              ? this.typeDiplomas[0].id
              : null;
        }
      },
    },
  },
  created() {
    this.handleRouteParams();

    const managerPublishDomElement = document.getElementById("manager-publish");
    const diplomaIndexAllow = document.getElementById("diploma-index-allow");
    this.diplomaIndexAllow = diplomaIndexAllow.dataset.diplomaIndexAllow === "true";
    console.log(this.diplomaIndexAllow);
    if (managerPublishDomElement) {
      this.managerCanPublish =
        managerPublishDomElement.dataset.managerPublish === "true";
    }
  },
  methods: {
    validatePoints() {
      if (this.points.includes("-")) {
        this.points = "";
      }

      const value = parseInt(this.points, 10);

      if (isNaN(value) || value < 1) {
        this.points = "";
        this.points = value;
      }
    },

    async handleRouteParams() {
      const routeName = this.$route.name;
      if (routeName === "UpdateCourse") {
        this.loadingCourse = true;
        await this.loadRequiredData();
        this.$store
          .dispatch("courseModule/loadCourse", this.$route.params.id)
          .then((r) => {
            const { data, error } = r;
            const survey = r.survey;
            this.active = data.active;
            if (data.category) {
              this.category_id = data.category.id;
            }

            if (data.typeCourse) {
              this.typeCourse_id = data.typeCourse.id;
            }

            if (data.typeDiploma) {
              this.typeDiploma_id = data.typeDiploma.id;
            }

            if (survey) {
              this.survey_id = survey.id;
            }

            this.code = data.code;
            if (data.courseSegments) {
              // data.courseSegments.forEach(item => {
              //   this.selectedSegments.push(item.id);
              // })
              this.selectedSegments = data.courseSegments;
            }
            this.description = data.description;
            this.isContentDiploma = data.is_content_diploma;
            this.diploma_index = data.type_index_diploma;
            this.descriptionContentDiploma = data.description_content_diploma;
            this.documentation = data.documentation ?? "";
            this.generalInformation = data.generalInformation ?? "";
            this.locale = data.locale;
            this.name = data.name;
            this.open = data.open;
            this.open_visible = data.open_visible;
            this.isNew = data.isNew;
            this.points = data.points;
            if (data.tags && data.tags.length > 0) {
              data.tags.forEach((tag) => {
                this.tags.push(tag.name);
              });
            }
            // tags

            // image
            if (data.image) {
              this.imageUrl = `/uploads/images/course/${data.image}`;
            }
            this.loadingCourse = false;

            this.$store
              .dispatch("courseModule/getCourseFilters", data.id)
              .then((r) => {
                const { data } = r;
                this.filters = generateCompatibleData(data);
              });

            this.$store
              .dispatch("courseModule/getManagers", data.id)
              .then((r) => {
                const { data } = r;
                this.selectedManagers = data;
              });
          });
      } else {
        this.loadRequiredData();
      }
      this.loadManagers();
    },
    loadRequiredData() {
      this.locale = this.defaultLocale;
      this.$store.dispatch("courseModule/loadPreData").then((r) => {
        const { data, error } = r;
        this.categories = data.categories;

        // Configuration
        this.courseDocumentation = data.courseDocumentation;
        this.multilingual = data.multilingual;
        this.setCourseLevel = data.setCourseLevel;
        this.setCoursePoints = data.setCoursePoints;
        this.useSegment = data.useSegment;
        this.typesCourse = data.typesCourse;
        this.surveysCourse = data.surveysCourse;
        this.typeDiplomas = data.typeDiplomas;
        this.points = data.points;
        this.typeIndexDiploma = data.typeIndexDiploma;

        if (this.typeDiplomas.length === 1) {
          this.typeDiploma_id = this.typeDiplomas[0].id;
        }

        if (this.surveysCourse.length === 1) {
          this.survey_id = this.surveysCourse[0].id;
        }

        if (this.useSegment) this.segments = data.segments;
        if (this.setCourseLevel) this.levels = data.levels;
      });
    },
    validateFirstStep() {
      if (!this.code || this.code.length < 1) {
        this.$toast.error(this.$t("ERROR_CODE_FIELD_REQUIRED") + "");
        return false;
      }
      if (!this.name || this.name.length < 1) {
        this.$toast.error(this.$t("ERROR_NAME_FIELD_REQUIRED") + "");
        return false;
      }
      if (!this.category_id || this.name.length < 1) {
        this.$toast.error(this.$t("ERROR_CATEGORY_FIELD_REQUIRED") + "");
        return false;
      }
      return true;
    },

    // Step1 => basic-info
    // Step2 => documentation | general-info
    // Step3 => null | general-info
    onCurrentStepChange(step) {
      if (step > 1 && !this.validateFirstStep()) return;
      if (step === 1) this.currentStep = "basic-info";
      else if (step === 2) {
        if (!this.validateFirstStep()) return;
        // if (this.courseDocumentation) this.currentStep = "documentation";
        this.currentStep = "general-info";
      } else {
        this.currentStep = "general-info";
      }

      this.currentStepNumber = step;
    },

    next() {
      if (this.currentStepNumber === 1) {
        if (!this.validateFirstStep()) {
          return;
        }

        this.currentStep = "general-info";
      } else this.currentStep = "general-info";

      this.currentStepNumber++;
    },
    prev() {
      if (this.currentStepNumber === 3) this.currentStep = "documentation";
      else if (this.currentStepNumber === 2) this.currentStep = "basic-info";
      this.currentStepNumber--;
    },
    submit() {
      const currentForm = document.forms["new-course-form"];
      const formData = new FormData(currentForm);
      formData.append("description", this.description);
      formData.append("general-information", this.generalInformation);
      formData.append("active", this.active);
      formData.append("open", this.open);
      formData.append("open-visible", this.open_visible);
      formData.append("open-new", this.open_new);
      formData.append("is-content-diploma", this.isContentDiploma);
      formData.append("diploma-index", this.diploma_index);
      formData.append("description-content-diploma", this.descriptionContentDiploma);
      formData.append(
        "filters",
        JSON.stringify(getFiltersForRequest(this.filters))
      );
      formData.append("locale", this.locale);
      formData.append("tags", JSON.stringify(this.tags));
      if (this.useSegment) {
        formData.append("segments", JSON.stringify(this.selectedSegments));
      }
      formData.append("managers", JSON.stringify(this.selectedManagers));
      if (this.courseDocumentation)
        formData.append("documentation", this.documentation);
      formData.append("typeCourse", this.typeCourse_id);

      if (this.survey_id !== null) {
        formData.append("survey", this.survey_id);
      }

      if (this.typeDiploma_id !== null) {
        formData.append("typeDiploma", this.typeDiploma_id);
      }

      if (!this.validateFirstStep()) return;

      if (this.$route.name === "UpdateCourse") {
        this.saveCourse(formData, true, this.$route.params.id);
      } else {
        this.saveCourse(formData);
      }
    },

    saveCourse(formData, update = false, id = null) {
      function save(store) {
        if (update)
          return store.dispatch("courseModule/updateCourse", { id, formData });
        else return store.dispatch("courseModule/createCourse", formData);
      }

      this.$store.dispatch("loaderModule/setLoading", {
        loading: true,
        message: this.$t("SAVING"),
      });
      save(this.$store)
        .then((res) => {
          const { error, data } = res;
          if (error) {
            this.$toast.error(data);
          } else {
            const { message, redirect } = data;
            this.$toast.success(message);
            window.location.href = redirect;
          }
        })
        .catch((error) => {})
        .finally(() => {
          this.$store.dispatch("loaderModule/setLoading", { loading: false });
        });
    },

    loadManagers() {
      this.$store.dispatch("userModule/loadAllManagers").then((res) => {
        this.availableManagers = res;
      });
    },
  },
};
</script>

<style scoped lang="scss">
.CreateCourse {
  .form-group {
    margin: 0;
    &.col-xs-12.col-md-6 {
      padding: 0;

      @media screen AND (min-width: 768px) {
        padding: 12px 15px;
      }
    }
    &.required {
      label::after {
        content: "*";
        color: red;
      }
    }

    @media screen AND (min-width: 768px) {
      padding: 12px 15px;
    }
  }

  .form-step {
    width: 100%;
    justify-content: center;
    flex-flow: row wrap;
    &.basic-info {
      .basic-info--tags,
      .basic-info--segments,
      .basic-info--description,
      .basic-info--data {
        width: 100%;
        padding: 0.5rem 0.15rem;
      }

      .basic-info--data {
        @media screen AND (min-width: 768px) {
          display: grid;
          grid-template-columns: [thumbnail] 400px [data] auto;
          gap: 1rem;

          .locale-selector {
            padding: 12px 15px;
          }
        }
      }

      .basic-info--segments {
        overflow-x: auto;
      }
    }

    &.general-info {
      .enableCourseStatus {
        display: grid;
        grid-template-columns: repeat(2, 1fr);

        @media screen and (max-width: 768px) {
          grid-template-columns: 1fr;
        }
      }

      .audience {
        overflow-x: auto;

        .audience-header {
          text-align: center;
        }
      }
    }
  }

  .col-12.required {
    label::after {
      content: "*";
      color: red;
    }
  }

  .audience-header {
    h1 {
      font-size: 24px;
      text-align: center;
    }

    p {
      font-size: 18px;
    }
  }

  .diploma-data {
    background-color: #D9F0FA;
    width: 100%;
    padding: 0.5rem 0.8rem;
  }

  .box-icon{
    color: #fff;
    border-radius: 5px;
    align-self: stretch;
    align-items: center;
    display: grid;
    justify-content: center;
    grid-column: 1;
    grid-row: 1/4;
    font-size: 2rem;
    padding: $spacing-s;
    background: #cfd8e3;
    height: 80px;

    img{
        width: 100%;
        filter: brightness(2);
    }
  }
}
</style>
