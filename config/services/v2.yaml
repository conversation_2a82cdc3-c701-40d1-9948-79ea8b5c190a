services:
  _defaults:
    autowire: true      # Automatically injects dependencies in your services.
    autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
    bind:
      # Log
      $logPath: "%kernel.logs_dir%"

      # Database table names
      string $courseCreatorTableName: 'course_creator'

      ## LTI V2
      string $ltiKeysDir: '%kernel.project_dir%/config/secrets/lti1p3/'

  App\V2\:
    resource: '../../src/V2/'

  App\V2\Application\CommandHandler\:
    resource: '../../src/V2/Application/CommandHandler/'
    tags:
      - { name: 'tactician.handler', typehints: true }

  App\V2\Application\QueryHandler\Admin\:
    resource: '../../src/V2/Application/QueryHandler/Admin/'
    tags:
      - { name: 'tactician.handler', typehints: true }

  App\V2\Infrastructure\Controller\Admin\:
    resource: '../../src/V2/Infrastructure/Controller/Admin/'
    tags: [ 'controller.service_arguments' ]

  ########## V2 Domain repositories ##############
  App\V2\Domain\User\UserRepository:
    class: App\V2\Infrastructure\Persistence\User\DoctrineUserRepository
    arguments:
      - '@doctrine.orm.entity_manager'

  App\V2\Domain\Course\Creator\CourseCreatorRepository:
    alias: App\V2\Infrastructure\Course\Creator\DBALCourseCreatorRepository


  ######### Hydrators #####################
  App\V2\Application\Hydrator\Course\Creator\CourseCreatorUserHydrator:
  App\V2\Application\Hydrator\Course\Creator\CourseCreatorHydratorCollection:
    autowire: true
    arguments:
      $hydrators:
        - '@App\V2\Application\Hydrator\Course\Creator\CourseCreatorUserHydrator'
  
  ######## Services ###############
  App\V2\Application\Log\Logger:
    alias: 'App\V2\Infrastructure\Log\MonologLogger'

  App\V2\Infrastructure\Utils\MpdfFactory:
    arguments:
      $cacheDir: "%kernel.cache_dir%"

  ######### LTI V2 ############
  App\V2\Infrastructure\LTI\OpenSSLKeyProvider:
  App\V2\Domain\LTI\LtiKeyProvider:
    alias: App\V2\Infrastructure\LTI\OpenSSLKeyProvider