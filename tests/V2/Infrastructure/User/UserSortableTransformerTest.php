<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\User;

use App\V2\Domain\Shared\Criteria\InvalidSortException;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Infrastructure\User\UserSortableTransformer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class UserSortableTransformerTest extends TestCase
{
    /**
     * Test that getSortableFields returns the expected array of sortable fields.
     */
    public function testGetSortableFields(): void
    {
        // Act
        $result = UserSortableTransformer::getSortableFields();

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('first_name', $result);
        $this->assertArrayHasKey('email', $result);
        $this->assertSame('id', $result['id']);
        $this->assertSame('firstName', $result['first_name']);
        $this->assertSame('email', $result['email']);
    }

    /**
     * Test that toSortableField correctly transforms valid user sort fields.
     */
    #[DataProvider('validUserSortFieldProvider')]
    public function testToSortableFieldWithValidUserField(string $sortBy, string $expected): void
    {
        // Act
        $result = UserSortableTransformer::toSortableField($sortBy);

        // Assert
        $this->assertInstanceOf(SortableField::class, $result);
        $this->assertSame($expected, $result->value());
    }

    /**
     * Test that toSortableField correctly transforms valid lifecycle sort fields.
     */
    #[DataProvider('validLifecycleSortFieldProvider')]
    public function testToSortableFieldWithValidLifecycleField(string $sortBy, string $expected): void
    {
        // Act
        $result = UserSortableTransformer::toSortableField($sortBy);

        // Assert
        $this->assertInstanceOf(SortableField::class, $result);
        $this->assertSame($expected, $result->value());
    }

    /**
     * Test that toSortableField throws an exception for invalid sort fields.
     */
    public function testToSortableFieldWithInvalidField(): void
    {
        // Arrange
        $invalidField = 'invalid_field';

        // Assert
        $this->expectException(InvalidSortException::class);

        // Act
        UserSortableTransformer::toSortableField($invalidField);
    }

    /**
     * Test that toSortDirection correctly transforms sort direction strings.
     */
    #[DataProvider('sortDirectionProvider')]
    public function testToSortDirection(string $direction, SortDirection $expected): void
    {
        // Act
        $result = UserSortableTransformer::toSortDirection($direction);

        // Assert
        $this->assertSame($expected, $result);
    }

    /**
     * Data provider for testToSortableFieldWithValidUserField.
     */
    public static function validUserSortFieldProvider(): \Generator
    {
        yield 'id' => [
            'sortBy' => 'id',
            'expected' => 'id',
        ];

        yield 'first_name' => [
            'sortBy' => 'first_name',
            'expected' => 'firstName',
        ];

        yield 'email' => [
            'sortBy' => 'email',
            'expected' => 'email',
        ];
    }

    /**
     * Data provider for testToSortableFieldWithValidLifecycleField.
     */
    public static function validLifecycleSortFieldProvider(): \Generator
    {
        yield 'created_at' => [
            'sortBy' => 'created_at',
            'expected' => 'createdAt',
        ];

        yield 'updated_at' => [
            'sortBy' => 'updated_at',
            'expected' => 'updatedAt',
        ];
    }

    /**
     * Data provider for testToSortDirection.
     */
    public static function sortDirectionProvider(): \Generator
    {
        yield 'asc' => [
            'direction' => 'asc',
            'expected' => SortDirection::ASC,
        ];

        yield 'desc' => [
            'direction' => 'desc',
            'expected' => SortDirection::DESC,
        ];

        yield 'default' => [
            'direction' => 'invalid',
            'expected' => SortDirection::ASC,
        ];
    }
}
