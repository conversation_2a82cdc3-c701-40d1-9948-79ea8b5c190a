<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\LTI;

use App\Tests\V2\Domain\LTI\LtiDeploymentRepositoryTestCase;
use App\V2\Domain\LTI\LtiDeploymentRepository;
use App\V2\Infrastructure\LTI\InMemoryLtiDeploymentRepository;

class InMemoryLtiDeploymentRepositoryTest extends LtiDeploymentRepositoryTestCase
{
    protected function getRepository(): LtiDeploymentRepository
    {
        return new InMemoryLtiDeploymentRepository();
    }
}
