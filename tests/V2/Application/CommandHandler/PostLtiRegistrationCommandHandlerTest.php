<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Tests\V2\Mother\LTI\LtiRegistrationMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Command\PostLtiRegistrationCommand;
use App\V2\Application\CommandHandler\PostLtiRegistrationCommandHandler;
use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\LTI\Exceptions\PostLtiRegistrationException;
use App\V2\Domain\LTI\LtiRegistration;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidGenerator;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PostLtiRegistrationCommandHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?LtiRegistrationRepository $ltiRegistrationRepository = null,
        ?UuidGenerator $uuidGenerator = null,
    ): PostLtiRegistrationCommandHandler {
        return new PostLtiRegistrationCommandHandler(
            ltiRegistrationRepository: $ltiRegistrationRepository
                ?? $this->createMock(LtiRegistrationRepository::class),
            uuidGenerator: $uuidGenerator ?? $this->createMock(UuidGenerator::class),
        );
    }

    /**
     * @throws PostLtiRegistrationException
     * @throws InfrastructureException
     * @throws Exception
     * @throws InvalidUuidException
     */
    public function testHandle(): void
    {
        $command = new PostLtiRegistrationCommand(
            name: 'Registration 1',
            clientId: 'registration-1'
        );

        $registrationId = UuidMother::create();

        $uuidGenerator = $this->createMock(UuidGenerator::class);
        $uuidGenerator->expects($this->once())
            ->method('generate')
            ->willReturn($registrationId);

        $ltiRegistrationRepository = $this->createMock(LtiRegistrationRepository::class);
        $ltiRegistrationRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new LtiRegistrationNotFoundException());

        $ltiRegistrationRepository->expects($this->once())
            ->method('put')
            ->willReturnCallback(function (LtiRegistration $ltiRegistration) use ($registrationId) {
                $this->assertEquals($registrationId, $ltiRegistration->getId());
                $this->assertEquals('Registration 1', $ltiRegistration->getName());
                $this->assertEquals('registration-1', $ltiRegistration->getClientId());
            });

        $handler = $this->getHandler(
            ltiRegistrationRepository: $ltiRegistrationRepository,
            uuidGenerator: $uuidGenerator,
        );

        $handler->handle($command);
    }

    public function testEnsureClientIdIsUnique(): void
    {
        $repository = $this->createMock(LtiRegistrationRepository::class);
        $repository->expects($this->once())
            ->method('findOneBy')
            ->willReturn(LtiRegistrationMother::create());

        $handler = $this->getHandler(ltiRegistrationRepository: $repository);
        $command = new PostLtiRegistrationCommand(
            name: 'Registration 1',
            clientId: 'registration-1'
        );
        $this->expectExceptionObject(PostLtiRegistrationException::clientIdMustBeUnique('registration-1'));
        $handler->handle($command);
    }
}
