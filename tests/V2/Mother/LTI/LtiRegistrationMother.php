<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\LTI;

use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Domain\LTI\LtiRegistration;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;

class LtiRegistrationMother
{
    private const string DEFAULT_NAME = 'Registration';
    private const string DEFAULT_CLIENT_ID = 'client_id';

    /**
     * @throws InvalidUuidException
     */
    public static function create(
        ?Uuid $id = null,
        ?string $name = null,
        ?string $clientId = null,
    ): LtiRegistration {
        return new LtiRegistration(
            id: $id ?? UuidMother::create(),
            name: $name ?? self::DEFAULT_NAME,
            clientId: $clientId ?? self::DEFAULT_CLIENT_ID,
        );
    }
}
