<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\User;

class AnnouncementMother
{
    private const string DEFAULT_CODE = 'DemoAnnouncementCode-1';

    public static function create(
        ?int $id = null,
        ?Course $course = null,
        ?string $code = self::DEFAULT_CODE,
        ?\DateTimeImmutable $startAt = null,
        ?\DateTimeImmutable $finishAt = null,
        ?string $status = Announcement::STATUS_ACTIVE,
        ?float $totalHours = null,
        ?int $usersPerGroup = null,
        ?string $objectiveAndContents = null,
        ?bool $subsidized = null,
        ?string $actionType = null,
        ?string $actionCode = null,
        ?string $denomination = null,
        ?string $contactPerson = null,
        ?string $contactPersonEmail = null,
        ?string $contactPersonTelephone = null,
        ?\DateTimeImmutable $notifiedAt = null,
        ?int $maxUsers = null,
        ?string $formativeActionType = null,
        ?string $format = null,
        ?string $place = null,
        ?string $trainingCenter = null,
        ?string $trainingCenterAddress = null,
        ?string $trainingCenterNif = null,
        ?string $trainingCenterPhone = null,
        ?string $trainingCenterEmail = null,
        ?string $subsidizerEntity = null,
        ?string $timezone = null,
        ?User $createdBy = null
    ) {
        $announcement = new Announcement();

        if (null !== $id) {
            $announcement->setId($id);
        }

        $announcement->setCourse($course)
            ->setCode($code)
            ->setStatus($status)
            ->setStartAt($startAt ?? new \DateTimeImmutable())
            ->setFinishAt($finishAt ?? new \DateTimeImmutable())
            ->setTotalHours($totalHours ?? 0)
            ->setUsersPerGroup($usersPerGroup ?? 0)
            ->setObjectiveAndContents($objectiveAndContents)
            ->setSubsidized($subsidized ?? false)
            ->setActionType($actionType ?? '')
            ->setActionCode($actionCode)
            ->setDenomination($denomination)
            ->setContactPerson($contactPerson)
            ->setContactPersonEmail($contactPersonEmail)
            ->setContactPersonTelephone($contactPersonTelephone)
            ->setNotifiedAt($notifiedAt)
            ->setMaxUsers($maxUsers)
            ->setFormativeActionType($formativeActionType)
            ->setFormat($format)
            ->setPlace($place)
            ->setTrainingCenter($trainingCenter)
            ->setTrainingCenterAddress($trainingCenterAddress)
            ->setTrainingCenterNif($trainingCenterNif)
            ->setTrainingCenterPhone($trainingCenterPhone)
            ->setTrainingCenterEmail($trainingCenterEmail)
            ->setSubsidizerEntity($subsidizerEntity)
            ->setTimezone($timezone ?? 'Europe/Madrid');

        if (null !== $createdBy) {
            $announcement->setCreatedBy($createdBy);
        }
        return $announcement;
    }
}
