<?php

declare(strict_types=1);

namespace App\Tests\Repository\ExternalAnnouncement;

use App\Repository\ExternalAnnouncement\ExternalAnnouncementCriteria;
use App\Repository\ExternalAnnouncement\ExternalAnnouncementRepository;
use App\Repository\ExternalAnnouncement\ExternalAnnouncementRepositoryException;
use App\Repository\ExternalAnnouncement\IberostarExternalAnnouncementRepository;
use PHPUnit\Framework\TestCase;

class IberostarExternalAnnouncementRepositoryTest extends TestCase
{
    private const NAME = 'Iberostar';
    private const TYPE = 'file';

    public function testInterfaceImplementation(): void
    {
        $this->assertInstanceOf(
            ExternalAnnouncementRepository::class,
            new IberostarExternalAnnouncementRepository()
        );
    }

    public function testRepositoryConfiguration(): void
    {
        $repository = new IberostarExternalAnnouncementRepository();

        $this->assertSame(self::NAME, $repository->getClientName());
        $this->assertSame(self::TYPE, $repository->getType());
    }

    /**
     * @throws ExternalAnnouncementRepositoryException
     */
    public function testNotSupportedMethod(): void
    {
        $this->expectExceptionObject(
            ExternalAnnouncementRepositoryException::thisProviderDoesNotSupportThisMethod(self::NAME)
        );

        $repository = new IberostarExternalAnnouncementRepository();
        $repository->getAnnouncements(new ExternalAnnouncementCriteria());
    }
}
