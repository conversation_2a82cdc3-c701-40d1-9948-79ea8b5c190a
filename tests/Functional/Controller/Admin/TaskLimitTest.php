<?php

declare(strict_types=1);

namespace App\Tests\Functional\Controller\Admin;

use App\Entity\Export;
use App\Entity\Task;
use App\Service\TaskLimitService;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Backend\BackendTaskEndpoints;
use Symfony\Component\HttpFoundation\Response;

class TaskLimitTest extends FunctionalTestCase
{
    private ?string $token = null;

    protected function setUp(): void
    {
        parent::setUp();

        $this->truncateEntities([Task::class, Export::class]);
        $this->token = $this->loginAndGetToken();
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([Task::class, Export::class]);
        parent::tearDown();
    }

    public function testTaskLimitExceeded(): void
    {
        $user = $this->getDefaultUser();
        $em = $this->getEntityManager();
        $taskRepository = $em->getRepository(Task::class);
        $taskLimitService = $this->getService(TaskLimitService::class);
        $limit = $taskLimitService->getLimit();

        // Crear tareas hasta el límite menos uno
        for ($i = 0; $i < $limit - 1; ++$i) {
            $task = new Task();
            $task->setTask('export-file')
                ->setCreatedAt(new \DateTime())
                ->setStatus(Task::TASK_PENDING)
                ->setCreatedBy($user)
                ->setParams(['created_by' => $user->getId()]);

            $export = new Export();
            $export->setType('test-export')
                ->setCreate($user);

            $export->setUpdate($user);
            $export->setTask($task);

            $em->persist($task);
            $em->persist($export);
        }

        $em->flush();

        // Verificar que tenemos una tarea menos que el límite
        $taskCount = $taskRepository->countPendingTasksByUser($user);
        $this->assertEquals($limit - 1, $taskCount);

        // Hacer una solicitud API para crear una tarea más (debería tener éxito)
        $response = $this->makeRequest(
            'GET',
            BackendTaskEndpoints::createTaskExportCatalog(),
            [],
            [],
            ['CONTENT_TYPE' => 'application/json'],
            $this->token
        );

        // Verificar que la solicitud fue exitosa
        $this->assertEquals(Response::HTTP_ACCEPTED, $response->getStatusCode());

        // Verificar que hemos alcanzado el límite
        $em->clear(); // Limpiar entity manager para forzar recarga
        $taskCount = $taskRepository->countPendingTasksByUser($user);
        $this->assertEquals($limit, $taskCount);

        // Hacer una solicitud API más (debería fallar con 403)
        $response = $this->makeRequest(
            method: 'GET',
            uri: BackendTaskEndpoints::createTaskExportCatalog(),
            headers: ['CONTENT_TYPE' => 'application/json'],
            bearerToken: $this->token
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());

        $responseContent = $response->getContent();
        $responseData = json_decode($responseContent, true);

        $this->assertArrayHasKey('message', $responseData);
        $this->assertStringContainsString('ha alcanzado el límite', $responseData['message']);

        // Verificación adicional: comprobar que el número de tareas no ha aumentado
        $taskCount = $taskRepository->countPendingTasksByUser($user);
        $this->assertEquals($limit, $taskCount);
    }
}
