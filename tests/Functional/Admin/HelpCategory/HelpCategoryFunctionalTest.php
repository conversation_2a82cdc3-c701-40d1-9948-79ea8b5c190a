<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\HelpCategory;

use App\Entity\HelpCategory;
use App\Entity\HelpCategoryTranslation;
use App\Entity\HelpText;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminHelpCategoryEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class HelpCategoryFunctionalTest extends FunctionalTestCase
{

    private const DEFAULT_NON_EXISTENT_CATEGORY_ID = 99999;
    private const CATEGORY_NAME = 'Test Category for Deletion';


    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testUrlHelpCategory(): void
    {
        $helpCategory = $this->createAndGetHelpCategory();

        $response = $this->getHelpCategoryResponse($helpCategory);

        $this->assertEquals(200, $response->getStatusCode());
    }

    public function testHelpCategories(): void
    {
        $response = $this->getHelpCategoriesResponse();

        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * @dataProvider helpCategoryDeletionProvider
     */
    public function testDeleteHelpCategory(
        string $scenario,
        bool $categoryExists,
        bool $hasContent,
        int $expectedStatusCode
    ): void {
        $categoryName = self::CATEGORY_NAME;
        $helpCategory = null;
        $categoryId = self::DEFAULT_NON_EXISTENT_CATEGORY_ID; // Default non-existent ID

        // Create category if it should exist
        if ($categoryExists) {
            $helpCategory = $this->createAndGetHelpCategory(name: $categoryName);
            $categoryId = $helpCategory->getId();

            if ($hasContent) {
                $this->createAndGetHelpText(helpCategory: $helpCategory);
            }
        }

        $userToken = $this->loginAndGetToken();

        $response = $this->makeAdminApiRequest(
            method: 'DELETE',
            uri: AdminHelpCategoryEndpoints::helpCategoryDeleteEndpoint($categoryId),
            body: [],
            queryParams: [],
            headers: [],
            bearerToken: $userToken
        );

        $this->assertEquals(
            $expectedStatusCode,
            $response->getStatusCode(),
            "Expected status code {$expectedStatusCode} for scenario: {$scenario}"
        );

        // Verify response content based on scenario
        if ($expectedStatusCode === Response::HTTP_NO_CONTENT) {
            // 204 No Content - successful deletion
            $this->assertEmpty($response->getContent(), 'No Content response should have empty body');

            // Verify the category was actually deleted
            $deletedCategory = $this->getEntityManager()
                ->getRepository(HelpCategory::class)
                ->find($categoryId);
            $this->assertNull($deletedCategory, 'Help category should be deleted when it has no content');

        } elseif ($expectedStatusCode === Response::HTTP_INTERNAL_SERVER_ERROR) {
            // 500 Internal Server Error - category doesn't exist
            // The ExceptionListener intercepts the NotFoundHttpException and returns 500
            // We only verify the status code, not the response body content

        } elseif ($expectedStatusCode === Response::HTTP_UNPROCESSABLE_ENTITY) {
            // 422 Unprocessable Entity - category has linked content
            $responseData = json_decode($response->getContent(), true);
            $this->assertIsArray($responseData, 'Response should be a valid JSON array');
            $this->assertArrayHasKey('status', $responseData);
            $this->assertArrayHasKey('error', $responseData);
            $this->assertArrayHasKey('data', $responseData);

            $this->assertEquals(422, $responseData['status']);
            $this->assertTrue($responseData['error']);
            $this->assertIsString($responseData['data'], 'Error message should be a string');
            $this->assertStringContainsString($categoryName, $responseData['data']);

            // Verify the category was NOT deleted
            $existingCategory = $this->getEntityManager()
                ->getRepository(HelpCategory::class)
                ->find($categoryId);
            $this->assertNotNull($existingCategory, 'Help category should not be deleted when it has content');
        }
    }


    public static function helpCategoryDeletionProvider(): \Generator
    {
        yield 'successful deletion - category without content' => [
            'scenario' => 'Delete category without linked content',
            'categoryExists' => true,
            'hasContent' => false,
            'expectedStatusCode' => Response::HTTP_NO_CONTENT,
        ];

        yield 'category not found - non-existent ID' => [
            'scenario' => 'Delete non-existent category',
            'categoryExists' => false,
            'hasContent' => false,
            'expectedStatusCode' => Response::HTTP_INTERNAL_SERVER_ERROR,
        ];

        yield 'unprocessable entity - category with linked content' => [
            'scenario' => 'Delete category with linked help texts',
            'categoryExists' => true,
            'hasContent' => true,
            'expectedStatusCode' => Response::HTTP_UNPROCESSABLE_ENTITY,
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function getHelpCategoryResponse($helpCategory): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'GET',
            AdminHelpCategoryEndpoints::helpCategoryEndpoint($helpCategory->getId()),
            [],
            [],
            [],
            $userToken
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function getHelpCategoriesResponse(): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'GET',
            AdminHelpCategoryEndpoints::helpCategoriesEndpoint(),
            [],
            [],
            [],
            $userToken
        );
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            HelpText::class,
            HelpCategory::class,
            HelpCategoryTranslation::class
        ]);

        parent::tearDown();
    }
}
