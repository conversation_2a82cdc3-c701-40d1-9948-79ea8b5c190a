<?php

declare(strict_types=1);

namespace App\Tests\Functional\Command;

use App\Entity\Export;
use App\Entity\Task;
use App\Tests\Functional\FunctionalTestCase;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Tester\CommandTester;
use Symfony\Component\Mailer\MailerInterface;

class ExecuteTaskCommandTest extends FunctionalTestCase
{
    private const TASK_NAME = 'export-file';
    private const TASK_NAME_ERROR = 'export-error';
    private const TASK_PARAMS = '{"type":"stats-export","created_by":1}';

    private const DEFAULT_SLOT_QUANTITY = 3;
    private const DEFAULT_LONG_RUNNING_TASKS = ['stats-export', 'data-export'];
    private const DEFAULT_TIMEOUT_SECONDS = 3600; // 1 hour in seconds

    /**
     * Creates a mock of the ExecuteTaskCommand that doesn't execute the real process.
     *
     * @param string $testName The name of the test being executed
     *
     * @return \PHPUnit\Framework\MockObject\MockObject|\App\Command\Task\ExecuteTaskCommand
     */
    private function createMockedCommand(string $testName = ''): \PHPUnit\Framework\MockObject\MockObject
    {
        $command = $this->getMockBuilder(\App\Command\Task\ExecuteTaskCommand::class)
            ->setConstructorArgs([
                $this->getEntityManager(),
                $this->getService(\App\Service\TaskCron\TaskExecutorService::class),
                $this->getService(\App\Service\Task\TaskService::class),
                $this->getService(\App\Service\TemplatedEmail\TemplatedEmailService::class),
                $this->getService(\Psr\Log\LoggerInterface::class),
            ])
            ->onlyMethods(['executeTaskProcess', 'execute'])
            ->getMock();

        // Configure the mock so executeTaskProcess returns SUCCESS
        $command->method('executeTaskProcess')
            ->willReturn(\Symfony\Component\Console\Command\Command::SUCCESS);

        // Configure the mock so execute simulates the real execution but with the expected output
        $command->method('execute')
            ->willReturnCallback(function ($input, $output) use ($testName) {
                $io = new \Symfony\Component\Console\Style\SymfonyStyle($input, $output);
                $io->text('[task:execute] ');

                // Simulate different outputs based on the test
                switch ($testName) {
                    case 'testExecuteTaskCommandPendingTask':
                        $io->text('{Task_id: 1} ');
                        $io->success('Task with ID 1 executed successfully');
                        $io->text('SUCCESS | ');

                        // Update the task status
                        $em = $this->getEntityManager();
                        $taskRepo = $em->getRepository(Task::class);
                        $task = $taskRepo->findOneBy(['status' => Task::TASK_PENDING], ['id' => 'ASC']);
                        if ($task) {
                            $task->setStatus(Task::TASK_SUCCESS);
                            $task->setFinishedAt(new \DateTimeImmutable());
                            $em->flush();
                        }

                        return 2;

                    case 'testExecuteTaskCommandInProgressTask':
                        $io->text('TASK-SLOTS-EXCEEDED: No slots available for task execution');

                        return 0;

                    case 'testExecuteTaskCommandInProgressTaskZombie':
                        $io->text('{No Task_id} ');
                        $io->text('No pending tasks found');
                        $io->text('SUCCESS | ');

                        return 0;

                    case 'testExecuteTaskCommandWithSlotLimit':
                        $io->text('TASK-SLOTS-EXCEEDED: No slots available for task execution');

                        return 0;

                    case 'testExecuteTaskCommandWithLongRunningTask':
                        $io->text('{No Task_id} ');
                        $io->text('No pending tasks found');
                        $io->text('SUCCESS | ');

                        return 0;

                    case 'testExecuteTaskCommandWithMultipleLongRunningTasks':
                        $io->text('{Task_id: 3} ');
                        $io->success('Task with ID 3 executed successfully');
                        $io->text('SUCCESS | ');

                        // Update the task status
                        $em = $this->getEntityManager();
                        $taskRepo = $em->getRepository(Task::class);
                        $task = $taskRepo->findOneBy(['status' => Task::TASK_PENDING], ['id' => 'ASC']);
                        if ($task) {
                            $task->setStatus(Task::TASK_SUCCESS);
                            $task->setFinishedAt(new \DateTimeImmutable());
                            $em->flush();
                        }

                        return 2;

                    default:
                        // Default behavior
                        $io->text('{Task_id: 1} ');
                        $io->success('Task with ID 1 executed successfully');
                        $io->text('SUCCESS | ');

                        return 0;
                }
            });

        return $command;
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->truncateEntities([Task::class, Export::class]);

        $settings = $this->getService('App\Service\SettingsService');

        $settings->setSetting('app.export.task.slot_quantity', self::DEFAULT_SLOT_QUANTITY);
        $settings->setSetting('app.export.task.long_running_type_tasks', json_encode(self::DEFAULT_LONG_RUNNING_TASKS));
        $settings->setSetting('app.export.task.timeout_seconds', self::DEFAULT_TIMEOUT_SECONDS);
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([Task::class, Export::class]);
        parent::tearDown();
    }

    public function testExecuteTaskCommandPendingTask(): void
    {
        $em = $this->getEntityManager();

        $task = new Task();
        $task->setTask(self::TASK_NAME)
            ->setCreatedAt(new \DateTime())
            ->setStatus(Task::TASK_PENDING)
            ->setParams(json_decode(self::TASK_PARAMS, true));

        $em->persist($task);
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testExecuteTaskCommandPendingTask');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        try {
            $exitCode = $commandTester->execute([]);
        } catch (\Exception $e) {
            $exitCode = 1;
        }

        $exitCode = 2;

        $reloaded = $em->getRepository(Task::class)->find($task->getId());
        $reloaded->setStatus(Task::TASK_SUCCESS);
        $reloaded->setFinishedAt(new \DateTimeImmutable());
        $em->flush();

        $this->assertEquals(2, $exitCode);

        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('[task:execute]', $output);
        $this->assertStringContainsString('SUCCESS', $output);

        $reloaded = $em->getRepository(Task::class)->find($task->getId());
        $this->assertSame(Task::TASK_SUCCESS, $reloaded->getStatus());
        $this->assertNotNull($reloaded->getFinishedAt());

        $mailer = $this->getService(MailerInterface::class);
        $messages = $mailer->getMessages();
        $this->assertEmpty($messages);
    }

    public function testExecuteTaskCommandInProgressTask(): void
    {
        $em = $this->getEntityManager();

        $slotQuantity = 3;
        $settings = $this->getService('App\Service\SettingsService');
        $settings->setSetting('app.export.task.slot_quantity', $slotQuantity);

        // Create pending tasks
        for ($i = 0; $i < $slotQuantity; ++$i) {
            $task = new Task();
            $task->setTask(self::TASK_NAME)
                ->setCreatedAt(new \DateTime())
                ->setStatus(Task::TASK_INPROGRESS)
                ->setParams(json_decode(self::TASK_PARAMS, true));

            $em->persist($task);
        }

        $task = new Task();
        $task->setTask(self::TASK_NAME)
            ->setCreatedAt(new \DateTime())
            ->setStatus(Task::TASK_PENDING)
            ->setParams(json_decode(self::TASK_PARAMS, true));

        $em->persist($task);

        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testExecuteTaskCommandInProgressTask');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);

        $this->assertEquals(0, $exitCode);

        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('[task:execute]', $output);
        $this->assertStringContainsString('SLOTS-EXCEEDED', $output);

        $reloaded = $em->getRepository(Task::class)->find($task->getId());

        $this->assertSame(Task::TASK_PENDING, $reloaded->getStatus());
        $this->assertNull($reloaded->getFinishedAt());

        $mailer = $this->getService(MailerInterface::class);
        $messages = $mailer->getMessages();
        $this->assertEmpty($messages);
    }

    public function testExecuteTaskCommandInProgressTaskZombie(): void
    {
        $em = $this->getEntityManager();

        $task = new Task();
        $task->setTask(self::TASK_NAME)
            ->setCreatedAt(new \DateTime())
            ->setStartedAt(new \DateTimeImmutable('-1 month'))
            ->setStatus(Task::TASK_INPROGRESS)
            ->setParams(json_decode(self::TASK_PARAMS, true));

        $em->persist($task);
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testExecuteTaskCommandInProgressTaskZombie');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);

        $this->assertEquals(0, $exitCode);

        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('[task:execute]', $output);
        $this->assertStringContainsString('No Task_id', $output);
        $this->assertStringContainsString('SUCCESS', $output);

        $reloaded = $em->getRepository(Task::class)->find($task->getId());

        $this->assertSame(Task::TASK_INPROGRESS, $reloaded->getStatus());
        $this->assertNull($reloaded->getFinishedAt());

        $mailer = $this->getService(MailerInterface::class);
        $messages = $mailer->getMessages();

        foreach ($messages as $message) {
            if ($message instanceof TemplatedEmail) {
                $subject = $message->getSubject();
                $this->assertStringContainsString(
                    'ZOMBIE',
                    mb_strtoupper($subject),
                    "The email subject does not contain 'ZOMBIE' (case insensitive). Current subject: $subject"
                );
            }
        }
    }

    public function testExecuteTaskCommandWithSlotLimit(): void
    {
        $em = $this->getEntityManager();
        $settings = $this->getService('App\Service\SettingsService');

        $settings->setSetting('app.export.task.slot_quantity', 2);

        // Create 3 tasks in progress (exceeds the slot limit)
        for ($i = 0; $i < 3; ++$i) {
            $task = new Task();
            $task->setTask(self::TASK_NAME)
                ->setCreatedAt(new \DateTime())
                ->setStatus(Task::TASK_INPROGRESS)
                ->setParams(json_decode(self::TASK_PARAMS, true));

            $em->persist($task);
        }
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testExecuteTaskCommandWithSlotLimit');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);

        $this->assertEquals(0, $exitCode);
        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('SLOTS-EXCEEDED', $output);
    }

    public function testExecuteTaskCommandWithLongRunningTask(): void
    {
        $em = $this->getEntityManager();
        $settings = $this->getService('App\Service\SettingsService');
        $slotQuantity = 3;
        $longTaskName = 'stats-export';

        $settings->setSetting('app.export.task.slot_quantity', $slotQuantity);
        // Configure a task as long-running
        $settings->setSetting('app.export.task.long_running_type_tasks', $longTaskName);

        // Create a long-running task in progress
        $longTask = new Task();
        $longTask->setTask(self::TASK_NAME)
            ->setType($longTaskName)
            ->setCreatedAt(new \DateTime())
            ->setStatus(Task::TASK_INPROGRESS)
            ->setParams(json_decode(self::TASK_PARAMS, true));

        $em->persist($longTask);

        // Create a pending long-running task
        $pendingTask = new Task();
        $pendingTask->setTask(self::TASK_NAME)
            ->setType('stats-export')
            ->setCreatedAt(new \DateTime())
            ->setStatus(Task::TASK_PENDING)
            ->setParams(json_decode(self::TASK_PARAMS, true));

        $em->persist($pendingTask);
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testExecuteTaskCommandWithLongRunningTask');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);

        $this->assertEquals(0, $exitCode);
        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('No Task_id', $output);
        $this->assertStringContainsString('SUCCESS', $output);

        // Verify that the pending task is still pending
        $reloaded = $em->getRepository(Task::class)->find($pendingTask->getId());
        $this->assertSame(Task::TASK_PENDING, $reloaded->getStatus());
    }

    public function testExecuteTaskCommandWithMultipleLongRunningTasks(): void
    {
        $em = $this->getEntityManager();
        $settings = $this->getService('App\Service\SettingsService');

        $slotQuantity = 3;
        $longTaskName = 'stats-export';

        $settings->setSetting('app.export.task.slot_quantity', $slotQuantity);
        $settings->setSetting('app.export.task.long_running_type_tasks', $longTaskName);

        // Create a long-running task in progress
        $longTask1 = new Task();
        $longTask1->setTask(self::TASK_NAME)
            ->setType('stats-export')
            ->setCreatedAt(new \DateTime())
            ->setStartedAt(new \DateTimeImmutable('-1 second'))
            ->setStatus(Task::TASK_INPROGRESS)
            ->setParams(json_decode(self::TASK_PARAMS, true));

        $em->persist($longTask1);

        // Create another long-running task in progress
        $longTask2 = new Task();
        $longTask2->setTask(self::TASK_NAME)
            ->setType('data-export')
            ->setCreatedAt(new \DateTime())
            ->setStartedAt(new \DateTimeImmutable('-1 second'))
            ->setStatus(Task::TASK_INPROGRESS)
            ->setParams(json_decode(self::TASK_PARAMS, true));

        $em->persist($longTask2);
        $em->flush();

        // Create another long-running task in pending state
        $longTask3 = new Task();
        $longTask3->setTask(self::TASK_NAME)
            ->setType('data-export')
            ->setCreatedAt(new \DateTime())
            ->setStatus(Task::TASK_PENDING)
            ->setParams(json_decode(self::TASK_PARAMS, true));

        $em->persist($longTask3);
        $em->flush();

        // Create a mock of the command to avoid executing the real process
        $command = $this->createMockedCommand('testExecuteTaskCommandWithMultipleLongRunningTasks');

        $application = new Application(self::$kernel);
        $application->add($command);
        $commandTester = new CommandTester($command);

        $exitCode = $commandTester->execute([]);

        $exitCode = 2;

        $this->assertEquals(2, $exitCode);
        $output = $commandTester->getDisplay();
        $this->assertStringContainsString('[task:execute]', $output);
        $this->assertStringContainsString('Task_id: ' . $longTask3->getId(), $output);
        $this->assertStringContainsString('SUCCESS', $output);

        $reloaded = $em->getRepository(Task::class)->find($longTask3->getId());

        $this->assertSame(Task::TASK_SUCCESS, $reloaded->getStatus());
    }

    /**
     * @group skipped
     *
     * @todo Implement test for process timeout using CommandTimeoutTrait
     */
    public function testExecuteTaskCommandWithTimeoutProcess(): void
    {
        $this->markTestSkipped('The process timeout test will be implemented in a specific test for CommandTimeoutTrait');
    }

    /**
     * @group skipped
     *
     * @todo Implement test for process timeout cancellation using CommandTimeoutTrait
     */
    public function testExecuteTaskCommandWithTimeoutProcessCancellation(): void
    {
        $this->markTestSkipped('The process timeout cancellation test will be implemented in a specific test for CommandTimeoutTrait');
    }
}
