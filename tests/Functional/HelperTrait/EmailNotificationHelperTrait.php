<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\EmailNotification;
use App\Entity\User;


trait EmailNotificationHelperTrait
{
    /**
     * Creates and persists a simple EmailNotification and returns it.
     */
    protected function createAndGetEmailNotification(
        ?User $user = null,
        bool $sent = false,
        string $type = 'announcement',
        ?string $title = 'Demo Title',
        ?string $message = 'Demo Message',
        array $extra = ['extra_info' => 'data']
    ): EmailNotification {
        $em = $this->getEntityManager();

        $notification = new EmailNotification();
        $notification->setUser($user ?? $this->getDefaultUser());
        $notification->setSent($sent);
        $notification->setType($type);
        if ($title) {
            $notification->setTitle($title);
        }
        if ($message) {
            $notification->setMessage($message);
        }
        if (!empty($extra)) {
            $notification->setExtra($extra);
        }

        $em->persist($notification);
        $em->flush();

        return $notification;
    }

    /**
     * Creates and persists a "translatable" EmailNotification
     * (using setTranslationTitle, setTranslationText, setAttributes).
     */
    protected function createAndGetTranslatedEmailNotification(
        ?User $user = null,
        bool $sent = false,
        string $type = 'announcement',
        string $translationTitle = 'notification.announcement.notification_user_title',
        string $translationText = 'notification.announcement.notification_user_message',
        array $attributes = [],
        array $extra = ['extra_info' => 'data']
    ): EmailNotification {
        $em = $this->getEntityManager();

        $notification = new EmailNotification();
        $notification->setUser($user ?? $this->getDefaultUser());
        $notification->setSent($sent);
        $notification->setType($type);
        $notification->setTranslationTitle($translationTitle);
        $notification->setTranslationText($translationText);

        if (!empty($attributes)) {
            $notification->setAttributes($attributes);
        }

        if (!empty($extra)) {
            $notification->setExtra($extra);
        }

        $em->persist($notification);
        $em->flush();

        return $notification;
    }

    /**
     * Deletes an EmailNotification from the database.
     */
    protected function removeEmailNotification(EmailNotification $notification): void
    {
        $em = $this->getEntityManager();
        $em->remove($notification);
        $em->flush();
    }
}
