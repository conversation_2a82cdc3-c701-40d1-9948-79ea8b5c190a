<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\CourseCategory;
use App\Entity\TypeCourse;

trait CourseCategoryHelperTrait
{
    protected function getCourseCategoriesAll(
        string $name = 'Test CourseCategory',
        bool $active = true
    ): CourseCategory {
        $em = $this->getEntityManager();
        $courseCategory = new CourseCategory();
        $courseCategory->setName($name);
        $courseCategory->setActive($active);
        $em->persist($courseCategory);
        $em->flush();

        return $courseCategory;
    }

    protected function createAndGetCourseCategories(
        string $name = 'Test CourseCategory name',
        string $description = 'Test CourseCategory description',
        string $orderType = 'auto',
        bool $active = true,
        ?CourseCategory $parent = null,
        ?TypeCourse $typeCourse = null,
    ): CourseCategory {
        $em = $this->getEntityManager();

        $typeCourse = $this->createTypeCourseIfNull();
        $em->refresh($typeCourse);

        $courseCategory = new CourseCategory();
        $courseCategory->setName($name);
        $courseCategory->setDescription($description);
        $courseCategory->setActive($active);
        $courseCategory->setOrderType($orderType);
        $courseCategory->setParent($parent);
        $courseCategory->setTypeCourse($typeCourse);
        $em->persist($courseCategory);
        $em->flush();

        return $courseCategory;
    }

    /**
     * Creates and persists a TypeCourse and returns it.
     *
     * @throws ORMException
     */
    protected function createTypeCourseIfNull(
        string $name = 'Demo Type Course',
        string $description = 'Description - Demo Type Course',
        string $code = 'online',
        string $denomination = 'INTERN',
        bool $active = true
    ): TypeCourse {
        $em = $this->getEntityManager();

        $typeCourse = new TypeCourse();
        $typeCourse->setName($name);
        $typeCourse->setDescription($description);
        $typeCourse->setCode($code);
        $typeCourse->setDenomination($denomination);
        $typeCourse->setActive($active);

        $em->persist($typeCourse);
        $em->flush();

        return $typeCourse;
    }

    public function setParamsQueryCreateCourseCategories()
    {
        $content = [];
        $content['id'] = 1;
        $content['name'] = 'Test CourseCategory name';
        $content['description'] = 'Test CourseCategory description';
        $content['orderType'] = 'auto';
        $content['active'] = true;
        $content['typeCourse'] = null;

        return $content;
    }

    public function setParamsQueryUpdateCourseCategories()
    {
        $em = $this->getEntityManager();

        $typeCourse = $em->getRepository(CourseCategory::class)->find(1);
        $content = [];
        $content['name'] = $typeCourse->getName();
        $content['description'] = $typeCourse->getDescription();
        $content['orderType'] = $typeCourse->getOrderType();
        $content['active'] = $typeCourse->isActive();
        $content['typeCourse'] = $typeCourse->getTypeCourse();
        var_dump($content);

        return $content;
    }
}
