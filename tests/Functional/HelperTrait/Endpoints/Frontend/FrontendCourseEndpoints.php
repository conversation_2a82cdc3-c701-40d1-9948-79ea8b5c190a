<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait\Endpoints\Frontend;

class FrontendCourseEndpoints
{
    public static function courseEndpoint($courseId, $new, $announcementId): string
    {
        return '/courses/' . $courseId . '?new=' . ($new ? 'true' : 'false') . (isset($announcementId) ? '&idAnnouncement=' . $announcementId : '');
    }

    public static function userCourseChapterUpdateEndpoint(int $userCourseChapterId): string
    {
        return \sprintf('/api/chapter/%d/update', $userCourseChapterId);
    }

    public static function courseOpinionEndpoint(int $courseId, ?int $announcementId = null, ?int $page = null, ?int $pageSize = null): string
    {
        $url = "/course/{$courseId}/opinions";

        $queryParams = [];

        if (null !== $announcementId) {
            $queryParams['announcementId'] = $announcementId;
        }

        if (null !== $page) {
            $queryParams['page'] = $page;
        }

        if (null !== $pageSize) {
            $queryParams['pageSize'] = $pageSize;
        }

        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }

        return $url;
    }
}
