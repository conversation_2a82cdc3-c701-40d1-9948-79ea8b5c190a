<?php

declare(strict_types=1);

namespace App\Tests\Entity;

use App\Entity\Announcement;
use App\Entity\AnnouncementManager;
use App\Entity\User;
use PHPUnit\Framework\TestCase;

class AnnouncementManagerTest extends TestCase
{
    public function testGettersAndSetters(): void
    {
        $announcement = new Announcement();
        $manager = new User();

        $announcementManager = new AnnouncementManager();

        $this->assertSame($announcementManager, $announcementManager->setAnnouncement($announcement));
        $this->assertSame($announcementManager, $announcementManager->setManager($manager));

        $this->assertSame($announcement, $announcementManager->getAnnouncement());
        $this->assertSame($manager, $announcementManager->getManager());
    }

    public function testNullableGetters(): void
    {
        $announcementManager = new AnnouncementManager();

        $this->assertNull($announcementManager->getAnnouncement());
        $this->assertNull($announcementManager->getManager());
    }
}
