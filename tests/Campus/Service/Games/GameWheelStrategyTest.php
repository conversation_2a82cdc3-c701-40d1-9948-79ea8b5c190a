<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\Games;

use App\Campus\Games\GameStrategyInterface;
use App\Campus\Games\Wheel;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use Doctrine\ORM\EntityManagerInterface;

class GameWheelStrategyTest extends TestGameStrategyInterface
{
    public function getStrategy(): GameStrategyInterface
    {
        return new Wheel($this->createMock(EntityManagerInterface::class));
    }

    public static function getValidData(): \Generator
    {
        $chapter = new Chapter();
        $chapterType = new ChapterType();
        $chapterType->setCode('999');
        $chapterType->setPercentageCompleted('0.75');
        $chapter->setType($chapterType);

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 2,
                'correct' => true,
            ],
            [
                'id' => 2,
                'questionId' => 2,
                'time' => 3,
                'correct' => true,
            ],
            [
                'id' => 3,
                'questionId' => 3,
                'time' => 5,
                'correct' => true,
            ],
            [
                'id' => 4,
                'questionId' => 4,
                'time' => 5,
                'correct' => true,
            ],
        ];

        $attempts = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
            ],
            [
                'id' => 2,
                'questionId' => 2,
                'time' => 2,
                'correct' => true,
            ],
        ];

        yield 'result ko, no chapter passed' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => null,
            'expectedPoints' => null,
        ];

        yield 'result ko, no total time passed' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => null,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no answers passed' => [
            'data' => ['answers' => null,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ok 4 correct answers. With 0 previous attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 60,
                'time' => 9,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.81,
        ];

        yield 'result ok 4 correct answers. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 4,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.73,
        ];

        $answers = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 2,
                'correct' => true,
            ],
            [
                'id' => 2,
                'questionId' => 2,
                'time' => 3,
                'correct' => true,
            ],
            [
                'id' => 3,
                'questionId' => 3,
                'time' => 5,
                'correct' => false,
            ],
            [
                'id' => 4,
                'questionId' => 4,
                'time' => 5,
                'correct' => false,
            ],
        ];

        $attempts = [
            [
                'id' => 1,
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
            ],
            [
                'id' => 2,
                'questionId' => 2,
                'time' => 2,
                'correct' => true,
            ],
            [
                'id' => 3,
                'questionId' => 1,
                'time' => 5,
                'correct' => true,
            ],
            [
                'id' => 4,
                'questionId' => 2,
                'time' => 2,
                'correct' => true,
            ],
        ];

        yield 'result ok 2 correct answers and 2 fail answer. With 4 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 4,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.57,
        ];
    }
}
