<?php

declare(strict_types=1);

namespace App\Tests\Unit\Service;

use App\Entity\Task;
use App\Service\SettingsService;
use App\Service\SlotManagerService;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

/**
 * Unit test for SlotManagerService.
 *
 * This test focuses on the core functionality of the SlotManagerService
 * without testing the integration with Doctrine or other external services.
 */
class SlotManagerServiceTest extends TestCase
{
    private EntityManagerInterface $em;
    private SettingsService $settings;
    private LoggerInterface $logger;
    private SlotManagerService $slotManagerService;

    protected function setUp(): void
    {
        $this->em = $this->createMock(EntityManagerInterface::class);
        $this->settings = $this->createMock(SettingsService::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->slotManagerService = new SlotManagerService(
            $this->em,
            $this->settings,
            $this->logger
        );
    }

    /**
     * Test that the service returns the correct timeout value from settings.
     */
    public function testGetTaskTimeoutSeconds(): void
    {
        // Configure settings to return a specific timeout value
        $this->settings->method('get')
            ->with('app.export.task.timeout_seconds', 3600)
            ->willReturn(1800);

        // Execute the method
        $result = $this->slotManagerService->getTaskTimeoutSeconds();

        // Assert the result
        $this->assertEquals(1800, $result);
    }

    /**
     * Test that the service uses the default timeout value when not configured.
     */
    public function testGetTaskTimeoutSecondsWithDefaultValue(): void
    {
        // Configure settings to return the default value
        $this->settings->method('get')
            ->with('app.export.task.timeout_seconds', 3600)
            ->willReturn(3600);

        // Execute the method
        $result = $this->slotManagerService->getTaskTimeoutSeconds();

        // Assert the result (should use default value 3600)
        $this->assertEquals(3600, $result);
    }

    /**
     * Test that the service correctly checks if a task is a long-running task.
     */
    public function testIsLongRunningTask(): void
    {
        // Configure settings to return a list of long-running task types
        $this->settings->method('get')
            ->with('app.export.task.long_running_type_tasks', [])
            ->willReturn(['long-task-type']);

        // Create a mock task with the long-running type
        $task = $this->createMock(Task::class);
        $task->method('getType')->willReturn('long-task-type');

        // Use reflection to access the protected method
        $reflectionClass = new \ReflectionClass(SlotManagerService::class);
        $method = $reflectionClass->getMethod('isLongRunningTask');
        $method->setAccessible(true);

        // Execute the method
        $result = $method->invoke($this->slotManagerService, $task);

        // Assert the result
        $this->assertTrue($result);
    }

    /**
     * Test that the service correctly identifies a task that is not long-running.
     */
    public function testIsNotLongRunningTask(): void
    {
        // Configure settings to return a list of long-running task types
        $this->settings->method('get')
            ->with('app.export.task.long_running_type_tasks', [])
            ->willReturn(['long-task-type']);

        // Create a mock task with a regular type
        $task = $this->createMock(Task::class);
        $task->method('getType')->willReturn('regular-task-type');

        // Use reflection to access the protected method
        $reflectionClass = new \ReflectionClass(SlotManagerService::class);
        $method = $reflectionClass->getMethod('isLongRunningTask');
        $method->setAccessible(true);

        // Execute the method
        $result = $method->invoke($this->slotManagerService, $task);

        // Assert the result
        $this->assertFalse($result);
    }
}
