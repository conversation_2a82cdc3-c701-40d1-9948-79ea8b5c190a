<?php

declare(strict_types=1);

namespace App\Tests\Unit\Resources\Traits\Command;

use App\Entity\CronJobTimeout;
use App\Resources\Traits\Command\CommandTimeoutTrait;
use App\Service\TemplatedEmail\TemplatedEmailService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Console\Style\SymfonyStyle;

class CommandTimeoutTraitTest extends TestCase
{
    private $traitMock;
    private $entityManager;
    private $templatedEmailService;
    private $io;
    private $repository;

    protected function setUp(): void
    {
        // Create an anonymous class that uses the trait
        $this->traitMock = new class {
            use CommandTimeoutTrait;

            public $em;
            public $templatedEmailService;
            public $name = 'test:command';

            public function getName()
            {
                return $this->name;
            }

            // Public methods to test private trait methods
            public function testGetTimeoutForCommand($commandName, $parentCommandName = null)
            {
                return $this->getTimeoutForCommand($commandName, $parentCommandName);
            }

            public function testExtractBaseCommand($commandName)
            {
                return $this->extractBaseCommand($commandName);
            }
        };

        // Create mocks
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->repository = $this->createMock(EntityRepository::class);
        $this->templatedEmailService = $this->createMock(TemplatedEmailService::class);
        $this->io = $this->createMock(SymfonyStyle::class);

        // Configure the trait mock
        $this->traitMock->em = $this->entityManager;
        $this->traitMock->templatedEmailService = $this->templatedEmailService;

        // Configure the EntityManager to return the repository
        $this->entityManager->method('getRepository')
            ->willReturn($this->repository);
    }

    /**
     * @dataProvider extractBaseCommandProvider
     */
    public function testExtractBaseCommand($input, $expected)
    {
        $this->assertEquals($expected, $this->traitMock->testExtractBaseCommand($input));
    }

    public static function extractBaseCommandProvider()
    {
        return [
            'Simple command' => ['test:command', 'test:command'],
            'Command with arguments' => ['test:command arg1 arg2', 'test:command'],
            'Command with spaces' => ['  test:command  ', 'test:command'],
        ];
    }

    public function testGetTimeoutForCommandWithExistingTimeout()
    {
        // Create a mock of CronJobTimeout
        $cronJobTimeout = $this->createMock(CronJobTimeout::class);
        $cronJobTimeout->method('getTimeout')->willReturn(1800);

        // Configure the repository to return the timeout
        $this->repository->method('findOneBy')
            ->with(['command' => 'test:command'])
            ->willReturn($cronJobTimeout);

        // Verify that the correct timeout is returned
        $this->assertEquals(1800, $this->traitMock->testGetTimeoutForCommand('test:command'));
    }

    public function testGetTimeoutForCommandWithParentCommand()
    {
        // Create a mock of CronJobTimeout for the parent command
        $parentCronJobTimeout = $this->createMock(CronJobTimeout::class);
        $parentCronJobTimeout->method('getTimeout')->willReturn(2400);

        // Configure the repository to return null for the current command and the timeout for the parent command
        $this->repository->method('findOneBy')
            ->will($this->returnCallback(function ($criteria) use ($parentCronJobTimeout) {
                if ('parent:command' === $criteria['command']) {
                    return $parentCronJobTimeout;
                }

                return null;
            }));

        // Verify that the parent command timeout is returned
        $this->assertEquals(2400, $this->traitMock->testGetTimeoutForCommand('test:command', 'parent:command'));
    }

    public function testGetTimeoutForCommandWithNoTimeout()
    {
        // Configure the repository to return null
        $this->repository->method('findOneBy')->willReturn(null);

        // Verify that the default timeout is returned
        $this->assertEquals(3600, $this->traitMock->testGetTimeoutForCommand('test:command'));
    }

    public function testGetTimeoutForCommandWithException()
    {
        // Configure the repository to throw an exception
        $this->repository->method('findOneBy')->willThrowException(new \Exception('Test exception'));

        // Verify that the default timeout is returned
        $this->assertEquals(3600, $this->traitMock->testGetTimeoutForCommand('test:command'));
    }
}
