<?php

declare(strict_types=1);

namespace App\Tests\Service\Annoucement\Admin;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Repository\AnnouncementRepository;
use App\Repository\AnnouncementUserRepository;
use App\Service\Annoucement\Admin\AnnouncementAlertTutorService;
use App\Service\Annoucement\Admin\AnnouncementAprovedCriteriaService;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementUserService;
use App\Service\Annoucement\Admin\TaskUserService;
use App\Service\Annoucement\Admin\UserChapterService;
use App\Service\SettingsService;
use App\Tests\Mother\Entity\AnnouncementGroupMother;
use App\Tests\Mother\Entity\AnnouncementGroupSessionMother;
use App\Tests\Mother\Entity\AnnouncementMother;
use App\Tests\Mother\Entity\AnnouncementUserMother;
use App\Tests\Mother\Entity\CourseMother;
use App\Tests\Mother\Entity\TypeCourseMother;
use App\Tests\Mother\Entity\UserMother;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class AnnouncementUserServiceTest extends TestCase
{
    private EntityManagerInterface $entityManager;
    private AnnouncementConfigurationsService $announcementConfigurationsService;
    private AnnouncementAlertTutorService $announcementAlertTutorService;
    private TaskUserService $taskUserService;
    private UserChapterService $userChapterService;
    private AnnouncementAprovedCriteriaService $announcementApprovedCriteriaService;
    private SettingsService $settings;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->announcementConfigurationsService = $this->createMock(AnnouncementConfigurationsService::class);
        $this->announcementAlertTutorService = $this->createMock(AnnouncementAlertTutorService::class);
        $this->taskUserService = $this->createMock(TaskUserService::class);
        $this->userChapterService = $this->createMock(UserChapterService::class);
        $this->announcementApprovedCriteriaService = $this->createMock(AnnouncementAprovedCriteriaService::class);
        $this->settings = $this->createMock(SettingsService::class);
    }

    /**
     * @throws Exception
     */
    #[DataProvider('getProgressTotalAssistanceDataProvider')]
    public function testGetProgressTotalAssistance(
        $assistancePercentage,
        bool $session1Assistance,
        bool $session2Assistance,
    ): void {
        $announcementUser = $this->getAnnouncementUser($session1Assistance, $session2Assistance);
        $announcementRepositoryMock = $this->createMock(AnnouncementRepository::class);
        $announcementRepositoryMock->expects($this->any())
            ->method('findAnnouncementUserByGroup')
            ->with($announcementUser->getAnnouncementGroup()->getId(), $announcementUser->getAnnouncement()->getId())
            ->willReturn([['id' => $announcementUser->getUser()->getId()]]);

        $announcementUserRepositoryMock = $this->createMock(AnnouncementUserRepository::class);
        $announcementUserRepositoryMock->expects($this->any())
            ->method('find')
            ->with($announcementUser->getUser()->getId())
            ->willReturn($announcementUser);

        $this->entityManager->expects($this->any())
            ->method('getRepository')
            ->willReturnCallback(function ($entityClass) use ($announcementRepositoryMock, $announcementUserRepositoryMock) {
                if (Announcement::class === $entityClass) {
                    return $announcementRepositoryMock;
                }
                if (AnnouncementUser::class === $entityClass) {
                    return $announcementUserRepositoryMock;
                }

                return null;
            });

        $announcementUserService = new AnnouncementUserService(
            $this->entityManager,
            $this->announcementConfigurationsService,
            $this->announcementAlertTutorService,
            $this->taskUserService,
            $this->userChapterService,
            $this->announcementApprovedCriteriaService,
            $this->settings
        );
        $percentageApprovedCriteriaUser = $announcementUserService->getProgressTotalAssistance($announcementUser);
        self::assertEquals($percentageApprovedCriteriaUser, $assistancePercentage);
    }

    public static function getProgressTotalAssistanceDataProvider(): \Generator
    {
        yield 'No session assistance' => [
            'assistancePercentage' => 0,
            'session1Assistance' => false,
            'session2Assistance' => false,
        ];

        yield 'One session assistance' => [
            'assistancePercentage' => 50,
            'session1Assistance' => true,
            'session2Assistance' => false,
        ];

        yield 'Two sessions assistance' => [
            'assistancePercentage' => 100,
            'session1Assistance' => true,
            'session2Assistance' => true,
        ];
    }

    /**
     * @throws Exception
     */
    #[DataProvider('testVerifyIfApprovedUserAnnouncementPresentialAndVirtualDataProvider')]
    public function testVerifyIfApprovedUserAnnouncementPresentialAndVirtual($percentageOfCompletedTraining, $session1Assistance, $session2Assistance, $expectedResult)
    {
        $announcementUser = $this->getAnnouncementUser($session1Assistance, $session2Assistance);
        $announcementRepositoryMock = $this->createMock(AnnouncementRepository::class);
        $announcementRepositoryMock->expects($this->any())
            ->method('findAnnouncementUserByGroup')
            ->with($announcementUser->getAnnouncementGroup()->getId(), $announcementUser->getAnnouncement()->getId())
            ->willReturn([['id' => $announcementUser->getUser()->getId()]]);

        $announcementUserRepositoryMock = $this->createMock(AnnouncementUserRepository::class);
        $announcementUserRepositoryMock->expects($this->any())
            ->method('find')
            ->with($announcementUser->getUser()->getId())
            ->willReturn($announcementUser);

        $this->entityManager->expects($this->any())
            ->method('getRepository')
            ->willReturnCallback(function ($entityClass) use ($announcementRepositoryMock, $announcementUserRepositoryMock) {
                if (Announcement::class === $entityClass) {
                    return $announcementRepositoryMock;
                }
                if (AnnouncementUser::class === $entityClass) {
                    return $announcementUserRepositoryMock;
                }

                return null;
            });

        // Use partial mock to mock only the methods we need. The rest of the code will be executed as normal.
        $announcementApprovedCriteriaService = $this->createPartialMock(AnnouncementAprovedCriteriaService::class, [
            'hasMinimumGradeToPassChapters',
            'hasCompleteTasks',
            'hasHoursOfCompletedTraining',
            'getValueHoursOfCompletedTraining',
        ]);

        $announcementApprovedCriteriaService->method('hasMinimumGradeToPassChapters')->willReturn(false);
        $announcementApprovedCriteriaService->method('hasCompleteTasks')->willReturn(false);
        $announcementApprovedCriteriaService->method('hasHoursOfCompletedTraining')->willReturn(true);
        $announcementApprovedCriteriaService->method('getValueHoursOfCompletedTraining')->willReturn($percentageOfCompletedTraining);

        $announcementUserService = new AnnouncementUserService(
            $this->entityManager,
            $this->announcementConfigurationsService,
            $this->announcementAlertTutorService,
            $this->taskUserService,
            $this->userChapterService,
            $announcementApprovedCriteriaService,
            $this->settings
        );

        $result = $announcementUserService->verifyIfAprovedUserAnnouncementPresentialAndVirtual($announcementUser);

        $this->assertEquals($expectedResult, $result);
    }

    public static function testVerifyIfApprovedUserAnnouncementPresentialAndVirtualDataProvider(): \Generator
    {
        yield 'User approved with 100% assistance and 50% percentage required' => [
            'percentageOfCompletedTraining' => 50,
            'session1Assistance' => true,
            'session2Assistance' => true,
            'expectedResult' => true,
        ];

        yield 'User approved with 50% assistance and 50% percentage required' => [
            'percentageOfCompletedTraining' => 50,
            'session1Assistance' => true,
            'session2Assistance' => false,
            'expectedResult' => true,
        ];

        yield 'User not approved with 50% assistance and 75% percentage required' => [
            'percentageOfCompletedTraining' => 75,
            'session1Assistance' => false,
            'session2Assistance' => true,
            'expectedResult' => false,
        ];
    }

    private function getAnnouncementUser(bool $session1Assistance, bool $session2Assistance): AnnouncementUser
    {
        $typeCourse = TypeCourseMother::create(
            id: 1,
            name: 'Presencial',
            code: 'presencial',
            denomination: 'INTERN',
        );
        $course = CourseMother::create(
            id: 1,
            typeCourse: $typeCourse,
        );
        $announcement = AnnouncementMother::create(
            id: 1,
            course: $course
        );
        $user = UserMother::create(
            id: 1,
            email: '<EMAIL>'
        );
        $groupSession1 = AnnouncementGroupSessionMother::create(
            id: 1,
            startAt: new \DateTimeImmutable('today'),
            finishAt: new \DateTimeImmutable('tomorrow'),
            assistance: [
                [
                    'id' => $user->getId(),
                    'assistance' => $session1Assistance,
                ],
            ],
            sessionNumber: 1
        );
        $groupSession2 = AnnouncementGroupSessionMother::create(
            id: 2,
            startAt: new \DateTimeImmutable('today'),
            finishAt: new \DateTimeImmutable('tomorrow'),
            assistance: [
                [
                    'id' => $user->getId(),
                    'assistance' => $session2Assistance,
                ],
            ],
            sessionNumber: 2
        );
        $announcementGroup = AnnouncementGroupMother::create(
            id: 1,
            code: 'group-1',
            announcement: $announcement,
            numSessions: 2,
            announcementGroupSessions: [$groupSession1, $groupSession2],
            groupNumber: 1,
        );

        return AnnouncementUserMother::create(
            id: 1,
            announcement: $announcement,
            user: $user,
            announcementGroup: $announcementGroup
        );
    }
}
