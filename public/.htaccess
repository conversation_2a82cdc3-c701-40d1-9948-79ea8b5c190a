#Header always set Content-Security-Policy: upgrade-insecure-requests
# Use the front controller as index file. It serves as a fallback solution when
# every other rewrite/redirect fails (e.g. in an aliased environment without
# mod_rewrite). Additionally, this reduces the matching process for the
# start page (path "/") because otherwise Apache will apply the rewriting rules
# to each configured DirectoryIndex file (e.g. index.php, index.html, index.pl).
DirectoryIndex index.php

# By default, Apache does not evaluate symbolic links if you did not enable this
# feature in your server configuration. Uncomment the following line if you
# install assets as symlinks or if you experience problems related to symlinks
# when compiling LESS/Sass/CoffeScript assets.
# Options +FollowSymlinks

# Disabling MultiViews prevents unwanted negotiation, e.g. "/index" should not resolve
# to the front controller "/index.php" but be rewritten to "/index.php/index".
<IfModule mod_negotiation.c>
    Options -MultiViews
</IfModule>

<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

    RewriteRule ^maintenance maintenance.php [L]

    # Maintenance mode
    # Gestionet is allowed to access the site (***********)
    RewriteCond %{HTTP:CF-Connecting-IP} !***********
    RewriteCond %{HTTP:CF-Connecting-IP} !************
    RewriteCond %{HTTP:CF-Connecting-IP} !************
    RewriteCond %{HTTP:CF-Connecting-IP} !************
    RewriteCond %{HTTP:CF-Connecting-IP} !************
    RewriteCond %{HTTP:CF-Connecting-IP} !************
    RewriteCond %{HTTP:CF-Connecting-IP} !************

    RewriteCond %{HTTP:X-FORWARDED-FOR} !***********
    RewriteCond %{HTTP:X-FORWARDED-FOR} !************
    RewriteCond %{HTTP:X-FORWARDED-FOR} !************
    RewriteCond %{HTTP:X-FORWARDED-FOR} !************
    RewriteCond %{HTTP:X-FORWARDED-FOR} !************
    RewriteCond %{HTTP:X-FORWARDED-FOR} !************
    RewriteCond %{HTTP:X-FORWARDED-FOR} !************

    RewriteCond %{REMOTE_ADDR} !^192\.168\.0\.0
    RewriteCond %{REMOTE_ADDR} !^127\.0\.0\.1
    RewriteCond %{DOCUMENT_ROOT}/maintenance.php -f
    RewriteCond %{DOCUMENT_ROOT}/maintenance.enable -f
    RewriteCond %{SCRIPT_FILENAME} !maintenance.php
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^.*$ /maintenance [R=503,L]
    ErrorDocument 503 /maintenance
#    Header Set Cache-Control "max-age=0, no-store"

    # Determine the RewriteBase automatically and set it as environment variable.
    # If you are using Apache aliases to do mass virtual hosting or installed the
    # project in a subdirectory, the base path will be prepended to allow proper
    # resolution of the index.php file and to redirect to the correct URI. It will
    # work in environments without path prefix as well, providing a safe, one-size
    # fits all solution. But as you do not need it in this case, you can comment
    # the following 2 lines to eliminate the overhead.
    RewriteCond %{REQUEST_URI}::$0 ^(/.+)/(.*)::\2$
    RewriteRule .* - [E=BASE:%1]

    # Sets the HTTP_AUTHORIZATION header removed by Apache
    RewriteCond %{HTTP:Authorization} .+
    RewriteRule ^ - [E=HTTP_AUTHORIZATION:%0]

    # Redirect to URI without front controller to prevent duplicate content
    # (with and without `/index.php`). Only do this redirect on the initial
    # rewrite by Apache and not on subsequent cycles. Otherwise we would get an
    # endless redirect loop (request -> rewrite to front controller ->
    # redirect -> request -> ...).
    # So in case you get a "too many redirects" error or you always get redirected
    # to the start page because your Apache does not expose the REDIRECT_STATUS
    # environment variable, you have 2 choices:
    # - disable this feature by commenting the following 2 lines or
    # - use Apache >= 2.3.9 and replace all L flags by END flags and remove the
    #   following RewriteCond (best solution)
    RewriteCond %{ENV:REDIRECT_STATUS} =""
    RewriteRule ^index\.php(?:/(.*)|$) %{ENV:BASE}/$1 [R=301,L]

    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^vcms /vcms/index.html [L]

    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^roleplay /roleplay/index.html [L]

    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(campus|games|vcms|roleplay) %{ENV:BASE}/index.html [L]

    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ %{ENV:BASE}/index.php [L]

</IfModule>

<IfModule !mod_rewrite.c>
    <IfModule mod_alias.c>
        # When mod_rewrite is not available, we instruct a temporary redirect of
        # the start page to the front controller explicitly so that the website
        # and the generated links can still be used.
        RedirectMatch 307 ^/$ /index.php/
        # RedirectTemp cannot be used instead
    </IfModule>
</IfModule>
<IfModule mod_headers.c>
    # Web Protection against clickjacking
    Header always set X-Frame-Options SAMEORIGIN

    # Configurar una Content Security Policy básica
    # Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';img-src 'self'"
#     Header set Content-Security-Policy "\
#        default-src 'self' *.gestionetdev.com vimeo.com *.vimeo.com *.vimeocdn.com *.webspellchecker.net; \
#        script-src 'self' 'unsafe-inline' 'unsafe-eval' *.youtube.com *.embedly.com *.cloudflare.com *.webspellchecker.net; \
#        style-src 'self' 'unsafe-inline' font.googleapis.com *.jsdelivr.net *.bootstrapcdn.com *.cloudflare.com ; \
#        font-src 'self' fonts.gstatic.com *.bootstrapcdn.com data:; \
#        img-src * data:; \
#        frame-src *"

    # Web Protection against interpreting malicious content type
    Header set X-Content-Type-Options "nosniff"

    # Web Protection against reflected cross-site scripting
    Header set X-XSS-Protection "1; mode=block"

</IfModule>
