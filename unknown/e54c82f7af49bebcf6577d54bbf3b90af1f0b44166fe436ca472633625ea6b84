<?php

declare(strict_types=1);

namespace App\Service\Announcement;

use App\Entity\ExtraData;
use App\Repository\ExtraDataRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

class AnnouncementExtraService
{
    protected SettingsService $settings;
    private EntityManagerInterface $entityManager;
    private ExtraDataRepository $extraDataRepository;

    public function __construct(SettingsService $settings, EntityManagerInterface $entityManager, ExtraDataRepository $extraDataRepository)
    {
        $this->settings = $settings;
        $this->entityManager = $entityManager;
        $this->extraDataRepository = $extraDataRepository;
    }

    /**
     * Retrieves all active extraData with translations if available.
     *
     * @param string|null $locale the desired translation language (optional)
     */
    public function getActiveExtraData(?string $locale = 'es'): array
    {
        $activeExtras = $this->extraDataRepository->findBy(['active' => true]);

        $extraData = [];
        foreach ($activeExtras as $extra) {
            $translatedName = $extra->getName();
            $translatedDescription = $extra->getDescription();

            if ($locale && method_exists($extra, 'translate')) {
                $translation = $extra->translate($locale);
                $translatedName = $translation->getName() ?? $translatedName;
                $translatedDescription = $translation->getDescription() ?? $translatedDescription;
            }

            $extraData[] = [
                'id' => $extra->getId(),
                'name' => $translatedName,
                'description' => $translatedDescription,
                'value' => null,
            ];
        }

        return $extraData;
    }

    public function getAllowedExtraSections(): array
    {
        $activeExtras = $this->getActiveExtraData();

        return array_map(function ($extra) {
            return $extra['id'];
        }, $activeExtras);
    }

    public function filterExtraSections(array $extras, array $allowedExtraSections): array
    {
        $mappedSections = [];
        foreach ($allowedExtraSections as $id) {
            if (is_numeric($id)) {
                $mappedSections[$id] = $extras[$id] ?? null;
            }
        }

        return $mappedSections;
    }

    public function getUniqueExtras(?string $locale = 'es'): array
    {
        $allowedSections = $this->getAllowedExtraSections();

        if (empty($allowedSections)) {
            return [];
        }

        $connection = $this->entityManager->getConnection();

        $selects = [];
        foreach ($allowedSections as $section) {
            $selects[] = "JSON_UNQUOTE(JSON_EXTRACT(a.extra, '$.{$section}')) AS `{$section}`";
        }

        $sql = '
            SELECT DISTINCT
                ' . implode(', ', $selects) . '
            FROM announcement a
            WHERE a.extra IS NOT NULL
        ';

        $results = $connection->executeQuery($sql)->fetchAllAssociative();

        $groupedByField = [];
        foreach ($allowedSections as $section) {
            $groupedByField[$section] = [];
        }

        foreach ($results as $row) {
            foreach ($allowedSections as $section) {
                if (!empty($row[$section])) {
                    $value = strtoupper(trim($row[$section]));
                    if (!\in_array($value, $groupedByField[$section], true)) {
                        $groupedByField[$section][] = $value;
                    }
                }
            }
        }

        $extraOptions = [];
        foreach ($groupedByField as $key => $values) {
            $extra = $this->extraDataRepository->find($key);
            if ($extra) {
                $translated = $extra->translate($locale);
                $name = $translated->getName() ?? $extra->getName();
            } else {
                $name = $key;
            }

            $extraOptions[] = [
                'id' => $key,
                'name' => $name,
                'options' => $values,
            ];
        }

        return $extraOptions;
    }

    public function addExtraSelectsQueryBuilder(\Doctrine\DBAL\Query\QueryBuilder $qb, ?array $extra): \Doctrine\DBAL\Query\QueryBuilder
    {
        if ($extra && \is_array($extra)) {
            foreach (array_keys($extra) as $key) {
                $qb->addSelect("JSON_UNQUOTE(JSON_EXTRACT(a.extra, '$.\"$key\"')) AS `$key`");
            }
        }

        return $qb;
    }

    public function applyExtraFiltersQueryBuilder(\Doctrine\DBAL\Query\QueryBuilder $qb, ?array $extra): \Doctrine\DBAL\Query\QueryBuilder
    {
        if ($extra && \is_array($extra)) {
            foreach ($extra as $key => $value) {
                if (null !== $value) {
                    $qb->andWhere("LOWER(JSON_UNQUOTE(JSON_EXTRACT(a.extra, '$.\"$key\"'))) = LOWER(:value_$key)")
                       ->setParameter("value_$key", strtolower($value));
                }
            }
        }

        return $qb;
    }

    public function applyExtraFiltersQueryBuilderJson($qb, $extra)
    {
        if ($extra && \is_array($extra)) {
            foreach ($extra as $key => $value) {
                if (null !== $value) {
                    $qb->andWhere("UPPER(a.extra) LIKE UPPER(:extraKey_$key)")
                        ->setParameter("extraKey_$key", '%"' . $key . '":"' . strtoupper($value) . '"%');
                }
            }
        }

        return $qb;
    }

    public function mapFilteredExtraSections(array $filteredExtraSections, array $extraData, bool $description = false): array
    {
        $finalExtra = [];

        foreach ($filteredExtraSections as $key => $value) {
            foreach ($extraData as $extra) {
                if ($extra['id'] == $key) {
                    $finalExtra[] = $this->processExtraItem(
                        $extra['id'],
                        $extra['name'],
                        $extra['description'] ?? null,
                        $value,
                        $description
                    );
                }
            }
        }

        return $finalExtra;
    }

    public function mapExtraDetailsFromRepo(
        array $filteredExtraSections,
        array $allowedExtraSections,
        EntityManagerInterface $em,
        string $locale,
        bool $description = false
    ): array {
        $finalExtra = [];
        $repo = $em->getRepository(ExtraData::class);

        foreach ($allowedExtraSections as $key) {
            if (is_numeric($key) && !\array_key_exists($key, $filteredExtraSections)) {
                $filteredExtraSections[$key] = null;
            }
        }

        foreach ($filteredExtraSections as $key => $value) {
            $extraData = $repo->find($key);
            if ($extraData) {
                $translation = $extraData->translate($locale);
                $translatedName = $translation ? $translation->getName() : $extraData->getName();
                $translatedDescription = $translation ? $translation->getDescription() : $extraData->getDescription();

                $finalExtra[] = $this->processExtraItem(
                    $extraData->getId(),
                    $translatedName ?? $extraData->getName(),
                    $translatedDescription,
                    $value,
                    $description
                );
            }
        }

        return $finalExtra;
    }

    private function processExtraItem(int $id, string $name, ?string $description, $value, bool $includeDescription): array
    {
        $extraItem = [
            'id' => $id,
            'name' => $name,
            'value' => $value,
        ];

        if ($includeDescription && null !== $description) {
            $extraItem['description'] = $description;
        }

        return $extraItem;
    }
}
