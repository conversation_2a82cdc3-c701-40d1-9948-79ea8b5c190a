<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Admin;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\User;
use App\Repository\AnnouncementGroupRepository;
use App\Service\Course\Common\UserCourseService;
use App\Service\SettingsService;
use App\Service\Traits\Announcement\AnnouncementGroupTrait;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class AnnouncementGroupService
{
    use AnnouncementGroupTrait;

    private EntityManagerInterface $em;
    private TranslatorInterface $translator;
    private AnnouncementUserService $announcementUserService;
    private UserChapterService $userChapterService;
    private SettingsService $settings;
    private Security $security;
    private RequestStack $requestStack;
    private AnnouncementGroupAssistanceService $announcementGroupAssistanceService;
    private AnnouncementGroupRepository $announcementGroupRepository;
    private AdminUrlGenerator $adminUrlGenerator;
    private ParameterBagInterface $params;
    private UserCourseService $userCourseService;

    public function __construct(
        EntityManagerInterface $em,
        TranslatorInterface $translator,
        AnnouncementUserService $announcementUserService,
        UserChapterService $userChapterService,
        SettingsService $settings,
        Security $security,
        RequestStack $requestStack,
        AnnouncementGroupAssistanceService $announcementGroupAssistanceService,
        AnnouncementGroupRepository $announcementGroupRepository,
        AdminUrlGenerator $adminUrlGenerator,
        ParameterBagInterface $params,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->translator = $translator;
        $this->announcementUserService = $announcementUserService;
        $this->userChapterService = $userChapterService;
        $this->settings = $settings;
        $this->security = $security;
        $this->requestStack = $requestStack;
        $this->announcementGroupAssistanceService = $announcementGroupAssistanceService;
        $this->announcementGroupRepository = $announcementGroupRepository;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->params = $params;
        $this->userCourseService = $userCourseService;
    }

    public function getAnnouncementGroupsBasicInfo(Announcement $announcement)
    {
        /** @var User $user */
        $user = $this->security->getUser();
        $filterByTutor = $user->isTutor() && !($user->isAdmin() || $user->isManager());
        $info = [];
        if ($filterByTutor) {
            foreach ($announcement->getAnnouncementGroups() as $group) {
                $tutor = $group->getAnnouncementTutor();
                if ($tutor && $filterByTutor) {
                    $tutorUser = $tutor->getTutor();
                    if ($tutorUser->getId() === $user->getId()) {
                        $info[] = [
                            'id' => $group->getId(),
                            'code' => $group->getCode(),
                            'name' => $this->generateGroupName($group->getGroupNumber())
                        ];
                    }
                }
            }
        } else {
            foreach ($announcement->getAnnouncementGroups() as $group) {
                $info[] = [
                    'id' => $group->getId(),
                    'code' => $group->getCode(),
                    'name' => $this->generateGroupName($group->getGroupNumber())
                ];
            }
        }

        return $info;
    }

    public function getGroupsAnnouncement(Announcement $announcement, bool $sessions = false): array
    {
        $course = $announcement->getCourse();

        $isNull = $this->em->getRepository(AnnouncementGroup::class)
            ->createQueryBuilder('g')
            ->where('g.announcement =:announcement AND g.groupNumber is null')
            ->setParameter('announcement', $announcement)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult()
        ;
        if ($isNull) {
            $this->em->getRepository(AnnouncementGroup::class)->refreshGroupNumbers($announcement);
        }

        $usersByGroup = $this->getUsersByGroup($announcement);

        $groups = [];

        foreach ($usersByGroup as $group => $users) {
            $dataUsers = [];
            $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($group);

            foreach ($users as $u) {
                $user = $this->em->getRepository(User::class)->findOneBy(['id' => $u['id']]);
                $u['details'] = $this->userCourseService->getUserData($course, $user, $announcement);
                $dataUsers[] = $u;
            }
            $groupNumber = $announcementGroup ? $announcementGroup->getGroupNumber() : 1;
            $groupName = $this->translator->trans('announcements.common.group', [], 'messages') . ' ' . $groupNumber;
            $groupInfo = $this->getInformationGroup($announcement, $group);
            $groupCombinedName = $groupInfo['code'] ? $groupName . ' (' . $groupInfo['code'] . ')' : $groupName;
            $temp = [
                'name' => $groupCombinedName,
                'total' => \count($users),
                'groupInfo' => $groupInfo,
                'users' => $dataUsers,
            ];
            if ($sessions) {
                $temp['sessions'] = $this->announcementGroupAssistanceService->getSessionsByGroup($announcement, $group);
            }
            $groups[] = $temp;
        }

        return $groups;
    }

    public function getStudentsByGroup(AnnouncementGroup $announcementGroup, Announcement $announcement)
    {
        if (!$announcementGroup) {
            return [];
        }

        $studentsGroup = $announcementGroup->getAnnouncementUsers();
        $students = [];

        foreach ($studentsGroup as $student) {
            $students[] = $this->getInformationUser($student->getUser());
        }

        return [
            'name' => $this->translator->trans('announcements.common.group', [], 'messages'),
            'total' => \count($students),
            'groupInfo' => $this->getInformationGroup($announcement, $announcementGroup->getId()),
            'users' => $students,
        ];
    }

    public function getGroupByAnnouncement(Announcement $announcement)
    {
        $user = $this->security->getUser();
        $groups = [];

        $announcementGroup = $this->shouldFilterByTutor($user) ?
            $this->announcementGroupRepository->getGroupsByTutor($announcement, $user) :
            $this->em->getRepository(AnnouncementGroup::class)->findBy(['announcement' => $announcement]);

        foreach ($announcementGroup as $group) {
            $groupName = $this->generateGroupName($group->getGroupNumber());
            $groups[] = [
                'id' => $group->getId(),
                'name' => $groupName,
                'numGroup' => $group->getGroupNumber(),
                'code' => $group->getCode(),
            ];
        }

        return $groups;
    }

    public function generateGroupName($numGroup): string
    {
        return $this->translator->trans('announcements.common.group', [], 'messages') . ' ' . $numGroup;
    }

    /**
     * @return AnnouncementGroup[]
     */
    public function getAnnouncementGroups(Announcement $announcement, User $user): array
    {
        // return $user->isTutor() && !($user->isAdmin() || $user->isManager());
        if ($user->isSuperAdmin() || $user->isAdmin() || $user->isManager()) {
            return $announcement->getAnnouncementGroups()->toArray();
        }

        if ($user->isTutor()) {
            return $this->em->getRepository(AnnouncementGroup::class)->createQueryBuilder('ag')
                ->select('ag')
                ->join('ag.announcementTutor', 'at')
                ->join('at.tutor', 'u')
                ->where('ag.announcement = :announcement')
                ->andWhere('u.id = :userId')
                ->setParameter('announcement', $announcement)
                ->setParameter('userId', $user->getId())
                ->getQuery()
                ->getResult();
        }

        return [];
    }
}
