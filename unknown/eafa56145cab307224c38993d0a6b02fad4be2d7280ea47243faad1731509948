<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\VcmsProject;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<VcmsProject>
 *
 * @method VcmsProject|null find($id, $lockMode = null, $lockVersion = null)
 * @method VcmsProject|null findOneBy(array $criteria, array $orderBy = null)
 * @method VcmsProject[]    findAll()
 * @method VcmsProject[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class VcmsProjectRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, VcmsProject::class);
    }

    public function add(VcmsProject $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(VcmsProject $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
