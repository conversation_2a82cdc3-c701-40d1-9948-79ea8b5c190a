<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\User;
use App\Service\User\Authentication\StarTeam;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UserUpdateEmailCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly StarTeam $starTeam
    ) {
        $this->em->getConnection()->getConfiguration()->setSQLLogger(null);

        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('starteam:user:email:update')
            ->setDescription('Update user email in DB from StarTeam')
            ->addOption('limit', null, InputArgument::OPTIONAL, 'Limit', 100);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $start = microtime(true);

            $limit = $input->getOption('limit');

            $userRepository = $this->em->getRepository(User::class);
            $users = $userRepository->createQueryBuilder('u')
                ->andWhere('u.code IS NOT NULL')
                ->andWhere("u.email LIKE '%@user.com'")
                ->andWhere("JSON_EXTRACT(u.meta, '$.starteamEmailProcessed') IS NULL")
                ->setMaxResults($limit)
                ->getQuery()
                ->getResult();

            foreach ($users as $user) {
                $employee = $this->starTeam->getUserData($user->getCode());
                $meta = $user->getMeta();

                $meta['starteamEmailProcessed'] = true;
                $meta['starteamEmailError'] = null;

                if (!$employee) {
                    $output->writeln('Not found: ' . $user->getCode());
                    $meta['starteamEmailError'] = 'NOT_FOUND';
                } elseif (
                    !$employee->personalEmail
                    || false === filter_var($employee->personalEmail, FILTER_VALIDATE_EMAIL)
                ) {
                    $output->writeln('Not found: ' . $user->getCode() . ' - Email: ' . $employee->personalEmail);
                    $meta['starteamEmailError'] = 'NO_EMAIL';
                } else {
                    $existsUserWithEmail = $userRepository->findOneBy(['email' => $employee->personalEmail]);
                    if ($existsUserWithEmail && $existsUserWithEmail->getId() != $user->getId()) {
                        $output->writeln(
                            'Email already exists: ' . $user->getCode() . ' - Email: ' . $employee->personalEmail
                        );
                        $meta['starteamEmailError'] = 'EMAIL_EXISTS';
                    } else {
                        $user->setEmail($employee->personalEmail);
                        $output->writeln(
                            'Updated: ' . $user->getEmail() . ' - Activo: ' . ($user->getIsActive() ? 'sí' : 'no')
                        );
                    }
                }

                $user->setMeta($meta);
                $this->em->flush();
            }

            $this->em->clear();

            $output->writeln('Time: ' . (microtime(true) - $start));

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $output->writeln($e->getMessage());

            return Command::FAILURE;
        }
    }
}
