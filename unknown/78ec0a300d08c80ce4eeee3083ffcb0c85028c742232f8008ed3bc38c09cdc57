<?php

declare(strict_types=1);

namespace App\Service\Apiv1;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;

class AnnouncementService
{
    public function getAnnouncementData(Announcement $announcement): array
    {
        return [
            'id' => $announcement->getId(),
            'code' => $announcement->getCode(),
            'type' => $announcement->getCourse()->getTypeCourse()->getCode(),
            'startAt' => $announcement->getStartAt()->format('Y-m-d H:i:s'),
            'finishAt' => $announcement->getFinishAt()->format('Y-m-d H:i:s'),
            'notifiedAt' => $announcement->getNotifiedAt() ? $announcement->getNotifiedAt()->format('Y-m-d H:i:s') : null,
            'status' => $announcement->getStatus(),
            'courseId' => $announcement->getCourse()->getId(),
            'courseName' => $announcement->getCourse()->getName(),
            'maxUsers' => $announcement->getMaxUsers(),
            'usersPerGroup' => $announcement->getUsersPerGroup(),
            'objectiveAndContents' => $announcement->getObjectiveAndContents(),
            'timezone' => $announcement->getTimezone(),
            'users' => $this->getUsers($announcement),
            'groups' => $this->getGroups($announcement),
        ];
    }

    public function getUsers(Announcement $announcement): array
    {
        $users = [];
        foreach ($announcement->getCalled() as $announcementUser) {
            $users[] = [
                'id' => $announcementUser->getUser()->getId(),
                'firstName' => $announcementUser->getUser()->getFirstName(),
                'lastName' => $announcementUser->getUser()->getLastName(),
                'email' => $announcementUser->getUser()->getEmail(),
                'assistanceConfirmation' => $announcementUser->isIsConfirmationAssistance(),
                'external' => $announcementUser->isExternal(),
            ];
        }

        return $users;
    }

    public function getGroups(Announcement $announcement): array
    {
        $groups = [];
        foreach ($announcement->getAnnouncementGroups() as $group) {
            $groups[] = [
                'id' => $group->getId(),
                'code' => $group->getCode(),
                'tutorId' => $group && $group->getAnnouncementTutor() && $group->getAnnouncementTutor()->getTutor() ? $group->getAnnouncementTutor()->getTutor()->getId() : null,
                'tutorCompanyId' => $group && $group->getAnnouncementTutor() && $group->getAnnouncementTutor()->getUserCompany() ? $group->getAnnouncementTutor()->getUserCompany()->getId() : null,
                'sessions' => $this->getSessions($group),
            ];
        }

        return $groups;
    }

    public function getSessions(AnnouncementGroup $announcementGroup): array
    {
        $sessions = [];
        foreach ($announcementGroup->getAnnouncementGroupSessions() as $session) {
            $sessions[] = [
                'id' => $session->getId(),
                'modalityId' => $session && $session->getModality() ? $session->getModality()->getId() : null,
                'localCoinIso' => $session && $session->getTypeMoney() ? $session->getTypeMoney()->getCodeIso() : null,
                'localCoinAmount' => $session ? $session->getCost() : null,
                'startAt' => $session->getStartAt(),
                'finishAt' => $session->getFinishAt(),
                'assistance' => $this->formatAssistance($session->getAssistance()),
                'timezone' => $session->getTimezone(),
                'place' => $session->getPlace(),
                'type' => $session->getType(),
            ];
        }

        return $sessions;
    }

    public function formatAssistance(array $assistance): array
    {
        // change id key to userId without changing the rest of the keys
        return array_map(function ($item) {
            $item['userId'] = $item['id'];
            unset($item['id']);

            return $item;
        }, $assistance);
    }
}
