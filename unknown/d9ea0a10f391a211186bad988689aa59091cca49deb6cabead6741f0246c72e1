<?php

declare(strict_types=1);

namespace App\Service\DateFormatter;

use App\Service\SettingsService;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Security;

/**
 * Service for formatting dates according to locale preferences.
 */
class DateFormatterService
{
    private Security $security;
    private ParameterBagInterface $params;
    private SettingsService $settings;
    private array $monthsCache = [];
    private array $daysCache = [];

    public function __construct(
        Security $security,
        ParameterBagInterface $params,
        SettingsService $settings
    ) {
        $this->security = $security;
        $this->params = $params;
        $this->settings = $settings;
    }

    /**
     * Get the current user's locale or default locale.
     */
    public function getUserLocale(): string
    {
        $user = $this->security->getUser();
        if ($user && method_exists($user, 'getLocale')) {
            $locale = \call_user_func([$user, 'getLocale']);

            return $locale ?: $this->getDefaultLocale();
        }

        return $this->getDefaultLocale();
    }

    /**
     * Get the default locale from parameters.
     */
    public function getDefaultLocale(): string
    {
        return $this->params->has('app.defaultLanguage')
            ? $this->params->get('app.defaultLanguage')
            : 'es';
    }

    /**
     * Validate if a locale is supported by the application.
     *
     * @throws \InvalidArgumentException When no languages are configured or locale is not supported
     */
    private function validateLocale(string $locale): string
    {
        // Get available languages from settings
        $availableLanguages = $this->settings->get('app.languages');

        // If no languages configured, throw exception
        if (!$availableLanguages || !is_array($availableLanguages)) {
            throw new \InvalidArgumentException('No languages configured in app.languages setting');
        }

        // If locale is in available languages, return it
        if (\in_array($locale, $availableLanguages, true)) {
            return $locale;
        }

        // If not found, throw exception
        throw new \InvalidArgumentException("Locale '$locale' is not supported. Available locales: " . implode(', ', $availableLanguages));
    }

    /**
     * Format a date according to the user's locale.
     *
     * @param \DateTimeInterface $date   The date to format
     * @param string             $format The format to use (short, medium, long, full)
     * @param string|null        $locale Optional locale override
     *
     * @return string Formatted date
     *
     * @throws \InvalidArgumentException When locale is not supported
     */
    public function formatDate(\DateTimeInterface $date, string $format = 'medium', ?string $locale = null): string
    {
        // Get the locale
        $locale = $locale ?: $this->getUserLocale();

        // Validate the locale against available languages
        $validatedLocale = $this->validateLocale($locale);
        $localeMap = $this->getLocaleMap($validatedLocale);

        // Define the format style according to the parameter
        $dateStyle = match ($format) {
            'short' => \IntlDateFormatter::SHORT,
            'medium' => \IntlDateFormatter::MEDIUM,
            'long' => \IntlDateFormatter::LONG,
            'full' => \IntlDateFormatter::FULL,
            default => \IntlDateFormatter::MEDIUM,
        };

        // Create the formatter with the correct locale
        $formatter = new \IntlDateFormatter(
            $localeMap,
            $dateStyle,
            \IntlDateFormatter::NONE
        );

        // Use IntlDateFormatter without specific customizations

        // Format the date
        return $formatter->format($date);
    }

    /**
     * Format a date and time according to the user's locale.
     *
     * @param \DateTimeInterface $dateTime   The date and time to format
     * @param string             $dateFormat The date format to use (short, medium, long, full)
     * @param string             $timeFormat The time format to use (short, medium, long, full)
     * @param string|null        $locale     Optional locale override
     *
     * @return string Formatted date and time
     *
     * @throws \InvalidArgumentException When locale is not supported
     */
    public function formatDateTime(
        \DateTimeInterface $dateTime,
        string $dateFormat = 'medium',
        string $timeFormat = 'short',
        ?string $locale = null
    ): string {
        // Get the locale
        $locale = $locale ?: $this->getUserLocale();

        // Validate the locale against available languages
        $validatedLocale = $this->validateLocale($locale);
        $localeMap = $this->getLocaleMap($validatedLocale);

        // Define the format styles according to the parameters
        $dateStyle = match ($dateFormat) {
            'short' => \IntlDateFormatter::SHORT,
            'medium' => \IntlDateFormatter::MEDIUM,
            'long' => \IntlDateFormatter::LONG,
            'full' => \IntlDateFormatter::FULL,
            default => \IntlDateFormatter::MEDIUM,
        };

        $timeStyle = match ($timeFormat) {
            'short' => \IntlDateFormatter::SHORT,
            'medium' => \IntlDateFormatter::MEDIUM,
            'long' => \IntlDateFormatter::LONG,
            'full' => \IntlDateFormatter::FULL,
            default => \IntlDateFormatter::SHORT,
        };

        // Create the formatter with the correct locale
        $formatter = new \IntlDateFormatter(
            $localeMap,
            $dateStyle,
            $timeStyle
        );

        // Use IntlDateFormatter without specific customizations

        // Format the date and time
        return $formatter->format($dateTime);
    }

    /**
     * Format a date with a custom pattern.
     *
     * @param \DateTimeInterface $date    The date to format
     * @param string             $pattern The ICU pattern to use
     * @param string|null        $locale  Optional locale override
     *
     * @return string Formatted date
     *
     * @throws \InvalidArgumentException When locale is not supported
     */
    public function formatWithPattern(\DateTimeInterface $date, string $pattern, ?string $locale = null): string
    {
        $locale = $locale ?: $this->getUserLocale();

        // Validate the locale against available languages
        $validatedLocale = $this->validateLocale($locale);
        $localeMap = $this->getLocaleMap($validatedLocale);

        $formatter = new \IntlDateFormatter(
            $localeMap,
            \IntlDateFormatter::FULL,
            \IntlDateFormatter::FULL,
            null,
            null,
            $pattern
        );

        return $formatter->format($date);
    }

    /**
     * Get month name in the specified locale.
     *
     * @param int         $monthNumber Month number (1-12)
     * @param string|null $locale      Optional locale override
     *
     * @return string Month name
     *
     * @throws \InvalidArgumentException When month number is invalid or locale is not supported
     */
    public function getMonthName(int $monthNumber, ?string $locale = null): string
    {
        if ($monthNumber < 1 || $monthNumber > 12) {
            throw new \InvalidArgumentException("Month number must be between 1 and 12, got: $monthNumber");
        }

        $locale = $locale ?: $this->getUserLocale();

        // Validate the locale against available languages
        $validatedLocale = $this->validateLocale($locale);
        $localeMap = $this->getLocaleMap($validatedLocale);
        $cacheKey = $localeMap . '_' . $monthNumber;

        if (isset($this->monthsCache[$cacheKey])) {
            return $this->monthsCache[$cacheKey];
        }

        $date = new \DateTime(\sprintf('%d-%02d-01', date('Y'), $monthNumber));

        $this->monthsCache[$cacheKey] = (new \IntlDateFormatter(
            $localeMap,
            \IntlDateFormatter::NONE,
            \IntlDateFormatter::NONE,
            null,
            null,
            'MMMM'
        ))->format($date);

        return $this->monthsCache[$cacheKey];
    }

    /**
     * Get day name in the specified locale.
     *
     * @param int         $dayNumber   Day number (1-7, where 1 is Monday in ISO-8601)
     * @param string|null $locale      Optional locale override
     * @param bool        $abbreviated Whether to return abbreviated day name
     *
     * @return string Day name
     *
     * @throws \InvalidArgumentException When day number is invalid or locale is not supported
     */
    public function getDayName(int $dayNumber, ?string $locale = null, bool $abbreviated = false): string
    {
        if ($dayNumber < 1 || $dayNumber > 7) {
            throw new \InvalidArgumentException("Day number must be between 1 and 7 (ISO-8601), got: $dayNumber");
        }

        $locale = $locale ?: $this->getUserLocale();

        // Validate the locale against available languages
        $validatedLocale = $this->validateLocale($locale);
        $localeMap = $this->getLocaleMap($validatedLocale);
        $cacheKey = $localeMap . '_' . $dayNumber . ($abbreviated ? '_abbr' : '');

        if (isset($this->daysCache[$cacheKey])) {
            return $this->daysCache[$cacheKey];
        }

        // Create a date for the specified day of week
        // ISO-8601: 1 (Monday) through 7 (Sunday)
        $date = new \DateTime();
        $date->setISODate((int) date('Y'), (int) date('W'), $dayNumber);

        $pattern = $abbreviated ? 'E' : 'EEEE';

        $this->daysCache[$cacheKey] = (new \IntlDateFormatter(
            $localeMap,
            \IntlDateFormatter::NONE,
            \IntlDateFormatter::NONE,
            null,
            null,
            $pattern
        ))->format($date);

        return $this->daysCache[$cacheKey];
    }

    /**
     * Format a date range.
     *
     * @param \DateTimeInterface $startDate Start date
     * @param \DateTimeInterface $endDate   End date
     * @param string             $format    Format to use
     * @param string|null        $locale    Optional locale override
     *
     * @return string Formatted date range
     */
    public function formatDateRange(
        \DateTimeInterface $startDate,
        \DateTimeInterface $endDate,
        string $format = 'medium',
        ?string $locale = null
    ): string {
        $locale = $locale ?: $this->getUserLocale();

        $startFormatted = $this->formatDate($startDate, $format, $locale);
        $endFormatted = $this->formatDate($endDate, $format, $locale);

        // Simple format with dash
        return $startFormatted . ' - ' . $endFormatted;
    }

    /**
     * Format a date using IntlDateFormatter with full format.
     *
     * @param \DateTimeInterface $date   The date to format
     * @param string|null        $locale Optional locale override
     *
     * @return string Formatted date
     *
     * @throws \InvalidArgumentException When locale is not supported
     */
    public function formatRelativeDate(
        \DateTimeInterface $date,
        ?string $locale = null
    ): string {
        $locale = $locale ?: $this->getUserLocale();

        // Validate the locale against available languages
        $validatedLocale = $this->validateLocale($locale);
        $localeMap = $this->getLocaleMap($validatedLocale);

        // Use IntlDateFormatter with full format to display the date
        $formatter = new \IntlDateFormatter(
            $localeMap,
            \IntlDateFormatter::FULL,
            \IntlDateFormatter::NONE
        );

        return $formatter->format($date);
    }

    /**
     * Map language codes to full locale codes for EasyLearning supported languages only.
     *
     * @throws \InvalidArgumentException When locale is not supported
     */
    private function getLocaleMap(string $locale): string
    {
        // Only handle languages that are supported in EasyLearning
        // Only handle the 4 languages actually configured in EasyLearning: en, pt, es, fr
        return match ($locale) {
            'es' => \Locale::composeLocale(['language' => 'es', 'region' => 'ES']),
            'en' => \Locale::composeLocale(['language' => 'en', 'region' => 'US']),
            'fr' => \Locale::composeLocale(['language' => 'fr', 'region' => 'FR']),
            'pt' => \Locale::composeLocale(['language' => 'pt', 'region' => 'PT']),
            default => throw new \InvalidArgumentException("Unsupported locale '$locale' for EasyLearning. Only 'en', 'pt', 'es', 'fr' are supported."),
        };
    }
}
