services:
  database:
    container_name: easylearning-database
    image: mariadb:10.5
    restart: unless-stopped
    ports:
      - "3316:3306"
    volumes:
      - ./docker/db:/var/lib/mysql
      - ./docker/mariadb/initdb:/docker-entrypoint-initdb.d
    environment:
      MYSQL_ROOT_PASSWORD: docker
      MYSQL_DATABASE: easylearning
    networks:
      easylearning-network:
        ipv4_address: ************
    command: --max_allowed_packet=64505856

  backend_apache:
    container_name: easylearning-backend-apache
    build: ./docker/apache
    restart: unless-stopped
    ports:
      - "8091:80"
      - "18091:443"
      - "9091:9001"
    volumes:
      - ./:/var/www/html
      - ./docker/apache/virtualhost.conf:/etc/apache2/sites-enabled/000-default.conf
      - ./docker/apache/virtualhost-ssl.conf:/etc/apache2/sites-enabled/default-ssl.conf
    networks:
      easylearning-network:
        ipv4_address: ************

  backend_php:
    container_name: easylearning-backend-php
    build:
      context: ./docker/php
      args:
        UID: "${UID}"
        GID: "${GID}"
    restart: unless-stopped
    user: "${UID}:${GID}"
    volumes:
      - ./:/var/www/html/
      - ./docker/php/php.ini:/usr/local/etc/php/conf.d/custom.ini
      - ./docker/php/xdebug.ini:/usr/local/etc/php/conf.d/xdebug.ini
      - ./docker/php/error_reporting.ini:/usr/local/etc/php/conf.d/error_reporting.ini
    environment:
      SYMFONY_HOME: /var/www/html/.symfony
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - database
      - backend_apache
    networks:
      easylearning-network:
        ipv4_address: ************

networks:
  easylearning-network:
    driver: bridge
    driver_opts:
      com.docker.network.enable_ipv6: "false"
    ipam:
      driver: default
      config:
        - subnet: **********/24
