<?php

namespace App\Modules\CourseSection\Services;

use App\Entity\CourseSection;
use App\Entity\CourseSectionTranslation;
use App\Modules\Common\Services\BaseService;
use App\Entity\CourseCategory;
use Doctrine\ORM\EntityManager;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;

class SectionCrudService extends BaseService
{
    public function formatSectionStructure(CourseSection $section): array
    {
        $locale = $this->getUserLocale();
        $name = $this->getTranslationSectionByLocale($section, $locale);

        return [
            'id' => $section->getId(),
            'name' => $name ?? $section->getName(),
            'description' => $section->getDescription(),
            'slug' => $section->getSlug(),
            'active' => $section->isActive(),
            'sort' => $section->getSort(),
            'isMain' => $section->isIsMain(),
            'hideCategoryName' => $section->getHideCategoryName(),
            'translations' => $this->getTranslationSection($section),
            'sectionCategories' => $this->getCourseCategory($section),
            'subtypes' => $section->getMeta(),
            'atAndBy' => $this->getAtAndBy($section),
            'isManualSelection' => $section->isIsManualSelection(),
        ];
    }

    private function getAtAndBy(CourseSection $section): array    
    {
        $atAndBy = [];

        if (!$section->getCreatedBy() || !$section->getCreatedAt()) {
            $atAndBy["createdBy"] = null;
            $atAndBy["createdAt"] = null;
        }

        if ($section->getCreatedBy()) {
            $atAndBy["createdBy"] = $section->getCreatedBy()->getFirstName() . ' ' . $section->getCreatedBy()->getLastName();
            $atAndBy["createdAt"] = date_format($section->getCreatedAt(), "d-m-Y");
            if ($section->getUpdatedAt()) {
                $atAndBy["UpdateBy"] = $section->getUpdatedBy()->getFirstName() . ' ' . $section->getCreatedBy()->getLastName();
                $atAndBy["UpdateAt"] = date_format($section->getUpdatedAt(), "d-m-Y");
            } else {
                $atAndBy["UpdateBy"] = null;
                $atAndBy["UpdateAt"] = null;
            }
        }

        return $atAndBy;
    }

    private function getTranslationSection(CourseSection $section): array
    {
        $translations = [];
        /** @var CourseSectionTranslation $translation */
        foreach ($section->getTranslations() as $translation) {
            $name = $translation->getName();
            $description = $translation->getDescription();
            $translations[] = [
                'locale' => $translation->getLocale(),
                'name' => $name,
                'description' => $description
            ];
        }

        return $translations;
    }

    private function getTranslationCategoryByLocale(CourseCategory $category, string $locale)
    {
        $name = null;
        /** @var CourseCategoryTranslation $translation */
        foreach ($category->getTranslations() as $translation) {
            if ($translation->getLocale() === $locale) {
                $name = $translation->getName();
                break;
            }
        }

        return $name;
    }


    public function delTranslationSection(CourseSection $section): void
    {
        /** @var CourseSectionTranslation $translation */
        foreach ($section->getTranslations() as $translation) {
            $this->em->remove($translation);
        }

        $this->em->flush();
    }

    private function getCourseCategory(CourseSection $section): array
    {
        $categories = [];
        $locale = $this->getUserLocale();
        /** @var CourseCategory $category */
        foreach ($section->getCategories() as $category) {
            $id = $category->getId();
            $name = $this->getTranslationCategoryByLocale($category, $locale);
            $categories[] = [
                'id' => $id,
                'name' =>  $name ?? $category->getName(),
                'operation' => 'exist' //utilizada para el update/create
            ];
        }
        return $categories;
    }

    public function changeStateSection(CourseSection $courseSection, $active): void
    {
        $courseSection->setActive($active);
        $this->em->persist($courseSection);
        $this->em->flush();
    }

    public function changeIsMainSection(CourseSection $courseSection, $isMain): void
    {
        $courseSection->setIsMain($isMain);
        $this->em->persist($courseSection);
        $this->em->flush();
    }

    /**
     * 
     * @param CourseSection $courseSection
     * @param array $translations
     * @throws \Exception
     */
    public function saveCourseSectionTranslation(array $translations, CourseSection $courseSection): void
    {
        try {
            foreach ($translations as $data) {
                $translation = $this->em->getRepository(CourseSectionTranslation::class)->findOneBy([
                    'translatable' => $courseSection,
                    'locale' => $data['locale']
                ]);

                $name = $data['name'] ?? null;
                $description = $data['description'] ?? null;

                if (empty($name) && empty($description)) {
                    if ($translation) $this->em->remove($translation);
                    continue;
                }

                if (!$translation) {
                    $translation = new CourseSectionTranslation();
                    $translation->setTranslatable($courseSection);
                    $translation->setLocale($data['locale']);
                }

                $translation->setName($name)
                    ->setDescription($description);
                $this->em->persist($translation);
            }

            $this->em->flush();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
    
    private function getTranslationSectionByLocale(CourseSection $courseSection, string $locale)
    {
        $name = null;
        /** @var CourseSectionTranslation $translation */
        foreach ($courseSection->getTranslations() as $translation) {
            if ($translation->getLocale() === $locale) {
                $name = $translation->getName();
                break;
            }
        }

        return $name;
    }

    public function getUserLocale(){
        return $this->getUser()->getLocale() ?? $this->settings->get('app.defaultLanguage');
    }
}
