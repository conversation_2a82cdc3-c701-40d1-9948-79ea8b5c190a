<?php

namespace App\Modules\CourseCategory\Services;

use App\Entity\CourseCategory;
use App\Entity\TypeCourse;
use App\Entity\CourseCategoryTranslation;
use App\Entity\Course;

use App\Repository\CourseCategoryRepository;

use App\Modules\Common\Services\BaseService;
use Doctrine\ORM\EntityManagerInterface;
use App\Service\SettingsService;
use Symfony\Component\Security\Core\Security;

class CourseCategoryCrudService extends BaseService
{   
    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        Security $security     
    ) {
        parent::__construct($em, $settings, $security);
    }
    
    public function formatCourseCategoryStructure(CourseCategory $courseCategory): array
    {
        $translations = $this->getTranslationCategory($courseCategory);
        $typeCourses = $this->getTypeCourses($courseCategory);

        return [
            'category' => [
                'id' => $courseCategory->getId(),
                'name' => $courseCategory->getName(),
                'description' => $courseCategory->getDescription(),
            ],
            'orderType' => $courseCategory->getOrderType(),
            'orderProperties' => $courseCategory->getOrderProperties(),
            'translations' => $translations,
            'typeCourses' => $typeCourses
        ];
    }

    public function getTranslationCategory(CourseCategory $courseCategory): array
    {
        $translations = [];
        /** @var CourseCategoryTranslation $translation */
        foreach ($courseCategory->getTranslations() as $translation) {
            $translations[] = [
                'locale' => $translation->getLocale(),
                'name' => $translation->getName() ?? '',
                'description' => $translation->getDescription() ?? ''
            ];
        }

        return $translations;
    }

    public function getTypeCourses(CourseCategory $courseCategory): array
    {
        $typeCourses = [];
        foreach ($this->em->getRepository(TypeCourse::class)->findBy(['active' => true]) as $typeCourse) {
            $typeCourses[$typeCourse->getId()] = false;
        }
        foreach ($courseCategory->getTypeCourse() as $tc) {
            $typeCourses[$tc->getId()] = true;
        }

        return $typeCourses;
    }

    public function getCoursesToOrder(CourseCategory $courseCategory): array
    {       
        $data = $this->em->getRepository(Course::class)->getCoursesToOrder($courseCategory) ;
        foreach ($data as &$d) {
            $d['typeCourseIcon'] = TypeCourse::ICONS[$d['typeCourseId']];
            $d['typeCourseType'] = TypeCourse::TYPES[$d['typeCourseId']];
            $d['image'] = empty($d['image']) ? '/assets/common/add_image.svg' : '/' . $this->settings->get('app.course_uploads_path') . '/' . $d['image'];
        }

        return $data;
    }
    
    public function setCourseCategoryData(CourseCategory $category, array $data): array
    {
        $name = $data['name'] ?? null;
        $description = $data['description'] ?? null;
        $orderType = $data['orderType'] ?? CourseCategory::ORDER_TYPE_AUTO;

        $translations = $data['translations'] ?? [];
        $orderProperties = $data['orderProperties'] ?? [];
        $typeCourses = $data['typeCourses'] ?? [];

        $this->setTranslations($category, $translations);

        $category->setName($name)
            ->setDescription($description)
            ->setOrderProperties($orderProperties)
            ->setOrderType($orderType)
            ->setTypeCourse($this->setSelectedTypeCourse($typeCourses));

        $this->em->persist($category);


        if ($orderType === CourseCategory::ORDER_TYPE_MANUAL) {
            $coursesOrder = $data['coursesOrder'] ?? [];
            $this->setOrderType($coursesOrder);
        }

        $this->em->flush();
        return ['error' => false, 'data' => $category->getId()];
    }

    private function setOrderType(array $coursesOrder): void
    {
        foreach ($coursesOrder as $courseId => $sort) {
            $course = $this->em->getRepository(Course::class)->find($courseId);
            if (!$course) continue;
            $course->setSort($sort);
            $this->em->persist($course);
        }

        $this->em->flush();
    }

    private function setTranslations(CourseCategory $category, array $translations):void
    {
        foreach ($translations as $t) {
            $tLocale = $t['locale'] ?? null;
            $tName = $t['name'] ?? null;
            $tDescription = $t['description'] ?? null;
            $translation = $this->em->getRepository(CourseCategoryTranslation::class)->findOneBy([
                'translatable' => $category,
                'locale' => $tLocale
            ]);

            if (empty($tName) && empty($tDescription)) {
                if ($translation) $this->em->remove($translation);
                continue;
            }

            if (!$translation) {
                $translation = new CourseCategoryTranslation();
                $translation->setTranslatable($category);
                $translation->setLocale($tLocale);
            }

            $translation->setName($tName)
                ->setDescription($tDescription)
            ;

            $this->em->persist($translation);
        }
        
        $this->em->flush();

    }

    private function setSelectedTypeCourse(array $typeCourses): array
    {

        $selectedTypeCourses = [];
        /**
         * @var int $typeCourseId
         * @var bool $enabled
         */
        foreach ($typeCourses as $typeCourseId => $enabled) {
            if (!$enabled) continue;
            $selectedTypeCourse = $this->em->getRepository(TypeCourse::class)->find($typeCourseId);
            if ($selectedTypeCourse) $selectedTypeCourses[] = $selectedTypeCourse;
        }

        return $selectedTypeCourses;
    }

    public function setCourseCategoryOrder(array $categoryorder):void
    {
        /**
         * @var int $categoryId
         * @var int $sort
         */
        foreach ($categoryorder as $categoryId => $sort) {
            $category = $this->em->getRepository(CourseCategory::class)->find($categoryId);
            if ($category) {
                $category->setSort($sort);
                $this->em->persist($category);
            }
        }

        $this->em->flush();
    }

    public function getCourseCategoryByLocale(Array $courseCategories): Array
    {        
        /** @var User $user */
        $user = $this->getUser();
        $locale = $user->getLocale() ?? $this->settings->get('app.adminDefaultLanguage');
        $courseCategoryByLocale = [];

        foreach($courseCategories as $courseCategory){
           $courseCategoryObj = $this->em->getRepository(CourseCategory::class)->findOneBy(["id" => $courseCategory['id']]);

            /** @var CourseCategoryTranslation $translation */
            $translation = $courseCategoryObj->translate($locale);
            $courseCategory['name'] = $translation->getName() ?? $courseCategoryObj->getName();
            
            array_push($courseCategoryByLocale, $courseCategory);
        }

        return $courseCategoryByLocale;
    }
}