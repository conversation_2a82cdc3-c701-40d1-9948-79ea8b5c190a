<?php

namespace App\Modules\TranslationsAdmin\Services;

use App\Entity\TranslationsAdmin;
use App\Entity\TranslationsAdminTranslation;

use App\Modules\Common\Services\BaseService;

class TranslationsAdminCrudService extends BaseService
{
    
    public function formatTranslationsAdminStructure(TranslationsAdmin $translationsAdmin): array
    {
        $translations = $this->getTranslationTranslationsAdmin($translationsAdmin);

        return [
            'id' => $translationsAdmin->getId(),
            'name' => $translationsAdmin->getName(),
            'description' => $translationsAdmin->getDescription(),
            'active' => $translationsAdmin->isActive(),
            'translations' => $translations,
        ];
    }

    public function getTranslationTranslationsAdmin(TranslationsAdmin $translationsAdmin): array
    {
        $translations = [];
        /** @var TranslationsAdminTranslation $translation */
        foreach ($translationsAdmin->getTranslations() as $translation) {
            $translations[] = [
                'locale' => $translation->getLocale(),
                'name' => $translation->getName() ?? '',
                'description' => $translation->getDescription() ?? ''
            ];
        }

        return $translations;
    }

    public function changeStateTranslationsAdmin(TranslationsAdmin $translationsAdmin): void
    {
        $translationsAdmin->setActive(!$translationsAdmin->isActive());
        $this->em->persist($translationsAdmin);
        $this->em->flush();
    }

}