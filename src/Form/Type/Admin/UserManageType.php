<?php

namespace App\Form\Type\Admin;

use App\Entity\Center;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserManageType extends AbstractType
{
    private SettingsService $settings;
    protected EntityManagerInterface $em;


    public function __construct (SettingsService $settings, EntityManagerInterface $em)
    {
        $this->settings = $settings;
        $this->em        = $em;
    }


    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm (FormBuilderInterface $builder, array $options)
    {
        $builder->add('countries', ChoiceType::class, [
            'choices' => $this->settings->get('app.user.extrafields')['country']['options']['choices'],
            'multiple' => true,
            'attr' => [
                'data-ea-widget' => 'ea-autocomplete',
            ],
        ]);

        $builder->add('centers', ChoiceType::class, [
            'choices' => $this->em->getRepository(Center::class)->getList(),
            'multiple' => true,
            'attr' => [
                'data-ea-widget' => 'ea-autocomplete',
            ],
        ]);
    }


    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions (OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => 'App\Entity\UserManage',
        ]);
    }
}
