<?php

namespace App\Form\Type\Admin\Filter;

use App\Entity\Department;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserExtraDepartmentFilterType extends AbstractType
{
    protected EntityManagerInterface $em;


    /**
     * @param EntityManagerInterface $entityManager
     */
    public function __construct (EntityManagerInterface $entityManager)
    {
        $this->em        = $entityManager;
    }


    public function configureOptions (OptionsResolver $resolver)
    {
        $departmentRepository = $this->em->getRepository(Department::class);
        $departments          = $departmentRepository->findAll();
        $choices              = [];
        foreach ($departments as $department)
        {
            $choices[$department->getCode() . ' - ' . $department->getName()] = $department->getId();
        }

        $resolver->setDefaults(['choices' => $choices]);
    }


    public function getParent(): string
    {
        return ChoiceType::class;
    }
}
