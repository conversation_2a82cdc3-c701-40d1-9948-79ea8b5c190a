<?php

namespace App\Form\Type\Admin\Filter;

use App\Entity\Center;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Form\Filter\Type\ComparisonFilterType;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Intl\Locales;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CourseLocaleFilterType extends AbstractType
{
    protected EntityManagerInterface $em;
    private SettingsService $settings;


    /**
     * @param EntityManagerInterface $entityManager
     * @param SettingsService $settings
     */
    public function __construct (EntityManagerInterface $entityManager, SettingsService $settings)
    {
        $this->em        = $entityManager;
        $this->settings = $settings;
    }


    public function configureOptions (OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'comparison_type_options' => ['type' => 'entity'], // second field with some predefined values, for 'type' => 'entity' you'll get 'is same', 'is not same' choices
            'value_type'              => ChoiceType::class,
            'value_type_options'      => [
                'choices' => $this->getLanguages(),
            ],
        ]);
    }


    public function getParent (): string
    {
        return ComparisonFilterType::class;
    }


    protected function getLanguages (): array
    {
        $locales = Locales::getNames(null);
        $languages = [];
        foreach($this->settings->get('app.languages') as $language)
        {
            if(isset($locales[$language]))
            {
                $languages[$locales[$language]] = $language;
            }
        }

        return $languages;
    }
}
