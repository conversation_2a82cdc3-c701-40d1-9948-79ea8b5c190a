<?php

declare(strict_types=1);

namespace App\Entity;

use App\Behavior\Blamable;
use App\Behavior\Fileable;
use App\Behavior\Timestampable;
use App\Repository\AnnouncementDidaticGuideRepository;
use Doctrine\ORM\Mapping as ORM;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=AnnouncementDidaticGuideRepository::class)
 *
 * @Vich\Uploadable()
 */
class AnnouncementDidaticGuide
{
    use Blamable;
    use Timestampable;
    use Fileable;

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @Vich\UploadableField(mapping="didactic_guide", fileNameProperty="filename", originalName="originalName", size="fileSize", mimeType="mimeType")
     */
    private $filenameFile;

    /**
     * @ORM\OneToOne(targetEntity=Announcement::class, inversedBy="didaticGuide", cascade={"persist"})
     */
    private $announcement;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAnnouncement(): ?Announcement
    {
        return $this->announcement;
    }

    public function setAnnouncement(?Announcement $announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }

    public function __clone()
    {
        $this->id = null;
        // clone didactic guide
        if ($this->getUploadsFolder()) {
            $this->cloneFile();
        }
    }
}
