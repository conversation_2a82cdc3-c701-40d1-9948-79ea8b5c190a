<?php

namespace App\Entity;

use App\Repository\IntegrationGroupRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=IntegrationGroupRepository::class)
 */
class IntegrationGroup
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=100)
     */
    private $name;

    /**
     * @ORM\Column(type="boolean")
     */
    private $active;

    /**
     * @ORM\OneToMany(targetEntity=IntegrationMapping::class, mappedBy="integrationGroup", cascade={"persist","remove"})
     */
    private $integrationMappings;

    public function __construct()
    {
        $this->integrationMappings = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }


    /**
     * @return Collection<int, IntegrationMapping>
     */
    public function getIntegrationMappings(): Collection
    {
        return $this->integrationMappings;
    }

    /**
     * @param array $integrationMappings
     * @return IntegrationGroup
     */
    public function setIntegrationMappings(array $integrationMappings): self
    {
        $this->integrationMappings = $integrationMappings;
        return $this;
    }

    public function addIntegrationMapping(IntegrationMapping $integrationMapping): self
    {
        if (!$this->integrationMappings->contains($integrationMapping)) {
            $this->integrationMappings[] = $integrationMapping;
            $integrationMapping->setIntegrationGroup($this);
        }

        return $this;
    }

    public function removeIntegrationMapping(IntegrationMapping $integrationMapping): self
    {
        if ($this->integrationMappings->removeElement($integrationMapping)) {
            // set the owning side to null (unless already changed)
            if ($integrationMapping->getIntegrationGroup() === $this) {
                $integrationMapping->setIntegrationGroup(null);
            }
        }

        return $this;
    }
}
