<?php

namespace App\Entity;

use App\Repository\TypeCourseAlertsRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=TypeCourseAlertsRepository::class)
 */
class TypeCourseAlerts
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=TypeCourse::class, inversedBy="typeCourseAlerts")
     * @ORM\JoinColumn(nullable=false)
     */
    private $typeCourse;

    /**
     * @ORM\ManyToOne(targetEntity=AlertTypeTutor::class, inversedBy="typeCourseAlerts")
     * @ORM\JoinColumn(nullable=false)
     */
    private $alertTypeTutor;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTypeCourse(): ?TypeCourse
    {
        return $this->typeCourse;
    }

    public function setTypeCourse(?TypeCourse $typeCourse): self
    {
        $this->typeCourse = $typeCourse;

        return $this;
    }

    public function getAlertTypeTutor(): ?AlertTypeTutor
    {
        return $this->alertTypeTutor;
    }

    public function setAlertTypeTutor(?AlertTypeTutor $alertTypeTutor): self
    {
        $this->alertTypeTutor = $alertTypeTutor;

        return $this;
    }
}
