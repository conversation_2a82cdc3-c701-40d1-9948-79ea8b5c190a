<?php

namespace App\Entity;

use App\Repository\TaskCourseRepository;
use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use App\Utils\TimeZoneConverter\TimezoneVerifierTrait;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\Serializer\Annotation\Groups;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;

/**
 * All datetime values affected by the parent timezone
 * If announcement has a defined timezone, then all dates are saved based on that timezone
 * By default, all datetime values saved using server timezone
 * @ORM\Entity(repositoryClass=TaskCourseRepository::class)
 * @ORM\HasLifecycleCallbacks()
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class TaskCourse
{
    use TimezoneVerifierTrait;
    use Blamable;
    use Timestampable;
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"detail"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     * @Groups({"detail"})
     */
    private $title;

    /**
     * @ORM\Column(type="text")
     * @Groups({"detail"})
     */
    private $description;

    /**
     * @ORM\ManyToOne(targetEntity=Course::class, inversedBy="taskCourses")
     */
    private $course;

    /**
     * @ORM\ManyToOne(targetEntity=Announcement::class, inversedBy="taskCourses")
     */
    private $announcement;

    /**
     * If announcement has a timezone defined, then datetime values are stored in UTC 0
     * @ORM\Column(type="datetime")
     * @Groups({"detail"})
     */
    private $dateDelivery;

    /**
     * If announcement has a timezone defined, then datetime values are stored in UTC 0
     * @ORM\Column(type="datetime", nullable=true)
     * @Groups({"detail"})
     */
    private $dateDeliveryAnnouncement;

    /**
     * If announcement has a timezone defined, then datetime values are stored in UTC 0
     * @ORM\Column(type="datetime", nullable=true)
     * @Groups({"detail"})
     */
    private $startDate;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     * @Groups({"detail"})
     */
    private $isVisible;

    /**
     * @ORM\OneToMany(targetEntity=FilesTask::class, mappedBy="taskCourse")
     * @Groups({"detail"})
     */
    private $filesTasks;

    /**
     * @ORM\OneToMany(targetEntity=TaskUser::class, mappedBy="task")
     */
    private $taskUsers;

    /**
     * @ORM\OneToMany(targetEntity=TaskCourseGroup::class, mappedBy="taskCourse")
     */
    private $taskCourseGroups;

    public function __construct()
    {
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
        $this->dateDelivery = new DateTime();
        $this->isVisible = true;
        $this->filesTasks = new ArrayCollection();
        $this->taskUsers = new ArrayCollection();
        $this->taskCourseGroups = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->title;
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getCourse(): ?Course
    {
        return $this->course;
    }

    public function setCourse(?Course $course): self
    {
        $this->course = $course;

        return $this;
    }

    public function getAnnouncement(): ?Announcement
    {
        return $this->announcement;
    }

    public function setAnnouncement(?Announcement $announcement): self
    {
        $this->announcement = $announcement;

        return $this;
    }

    public function getDateDelivery(): ?\DateTimeInterface
    {
        return $this->dateDelivery;
    }

    public function setDateDelivery(\DateTimeInterface $dateDelivery): self
    {
        $this->dateDelivery = $dateDelivery;

        return $this;
    }

    public function getDateDeliveryAnnouncement(): ?\DateTimeInterface
    {
        return $this->dateDeliveryAnnouncement;
    }

    public function setDateDeliveryAnnouncement(\DateTimeInterface $dateDeliveryAnnouncement): self
    {
        $this->dateDeliveryAnnouncement = $dateDeliveryAnnouncement;

        return $this;
    }

    public function getStartDate(): ?\DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(?\DateTimeInterface $startDate): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function isIsVisible(): ?bool
    {
        return $this->isVisible;
    }

    public function setIsVisible(?bool $isVisible): self
    {
        $this->isVisible = $isVisible;

        return $this;
    }

    /**
     * @return Collection<int, FilesTask>
     */
    public function getFilesTasks(): Collection
    {
        return $this->filesTasks;
    }

    public function addFilesTask(FilesTask $filesTask): self
    {
        if (!$this->filesTasks->contains($filesTask)) {
            $this->filesTasks[] = $filesTask;
            $filesTask->setTaskCourse($this);
        }

        return $this;
    }

    public function removeFilesTask(FilesTask $filesTask): self
    {
        if ($this->filesTasks->removeElement($filesTask)) {
            // set the owning side to null (unless already changed)
            if ($filesTask->getTaskCourse() === $this) {
                $filesTask->setTaskCourse(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, TaskUser>
     */
    public function getTaskUsers(): Collection
    {
        return $this->taskUsers;
    }

    public function addTaskUser(TaskUser $taskUser): self
    {
        if (!$this->taskUsers->contains($taskUser)) {
            $this->taskUsers[] = $taskUser;
            $taskUser->setTask($this);
        }

        return $this;
    }

    public function removeTaskUser(TaskUser $taskUser): self
    {
        if ($this->taskUsers->removeElement($taskUser)) {
            // set the owning side to null (unless already changed)
            if ($taskUser->getTask() === $this) {
                $taskUser->setTask(null);
            }
        }

        return $this;
    }

    /**
     * @Groups({"detail"})
     * @return String
     */
    public function getExpanded()
    {
        return false;
    }

    /**
     * @return Collection<int, TaskCourseGroup>
     */
    public function getTaskCourseGroups(): Collection
    {
        return $this->taskCourseGroups;
    }

    public function addTaskCourseGroup(TaskCourseGroup $taskCourseGroup): self
    {
        if (!$this->taskCourseGroups->contains($taskCourseGroup)) {
            $this->taskCourseGroups[] = $taskCourseGroup;
            $taskCourseGroup->setTaskCourse($this);
        }

        return $this;
    }

    public function removeTaskCourseGroup(TaskCourseGroup $taskCourseGroup): self
    {
        if ($this->taskCourseGroups->removeElement($taskCourseGroup)) {
            // set the owning side to null (unless already changed)
            if ($taskCourseGroup->getTaskCourse() === $this) {
                $taskCourseGroup->setTaskCourse(null);
            }
        }

        return $this;
    }
}
