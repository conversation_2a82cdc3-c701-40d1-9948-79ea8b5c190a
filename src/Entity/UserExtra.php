<?php

namespace App\Entity;

use App\Repository\UserExtraRepository;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use Vich\UploaderBundle\Entity\File;
use Vich\UploaderBundle\Mapping\Annotation as Vich;

/**
 * @ORM\Entity(repositoryClass=UserExtraRepository::class)
 * @ORM\HasLifecycleCallbacks()
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 * @Vich\Uploadable()
 */
class UserExtra
{
    use AtAndBy;

    /**
     * @ORM\Id()
     * @ORM\GeneratedValue()
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\OneToOne(targetEntity=User::class, inversedBy="extra", cascade={"persist", "remove"})
     * @ORM\JoinColumn(nullable=false)
     */
    private $user;

    /**
     * @ORM\ManyToOne(targetEntity=ProfessionalCategory::class, cascade={"persist"})
     * @ORM\JoinColumn(nullable=true)
     * @Groups({"user_area"})
     * @Assert\NotBlank
     */
    private $category;

    /**
     * @ORM\ManyToOne(targetEntity=Department::class, cascade={"persist"})
     * @ORM\JoinColumn(nullable=true)
     * @Groups({"user_area","ranking"})
     */
    private $department;

    /**
     * @ORM\ManyToOne(targetEntity=Center::class, cascade={"persist"})
     * @ORM\JoinColumn(nullable=true)
     * @Groups({"user_area"})
     */
    private $center;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"user_area","ranking"})
     */
    private $country;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $gender;

    /**
     * @ORM\Column(type="date", nullable=true)
     */
    private $birthdate;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     * @Groups({"library"})
     */
    private $avatar;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $resume;

    /**
     * @Vich\UploadableField(mapping="user_extra_resume", fileNameProperty="resume")
     *
     * var File $resumeFile
     */
    private $resumeFile;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $division;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $pdfCv;

	/**
	 * @Vich\UploadableField(mapping="pdf_cv_file", fileNameProperty="pdfCv")
	 *
	 * var File $pdfCvFile
	 */
	private $pdfCvFile;


    public function __toString()
    {
        return $this->getCategory() . ' - ' . $this->getDepartment() . ' - ' . $this->getCenter();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getCategory(): ?ProfessionalCategory
    {
        return $this->category;
    }

    public function setCategory(?ProfessionalCategory $category): self
    {
        $this->category = $category;

        return $this;
    }

    public function getDepartment(): ?Department
    {
        return $this->department;
    }

    public function setDepartment(?Department $department): self
    {
        $this->department = $department;

        return $this;
    }

    public function getCenter(): ?Center
    {
        return $this->center;
    }

    public function setCenter(?Center $center): self
    {
        $this->center = $center;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getGender(): ?string
    {
        return $this->gender;
    }

    public function setGender(string $gender): self
    {
        $this->gender = $gender;

        return $this;
    }

    public function getBirthdate(): ?\DateTimeInterface
    {
        return $this->birthdate;
    }

    public function setBirthdate(?\DateTimeInterface $birthdate): self
    {
        $this->birthdate = $birthdate;

        return $this;
    }

    public function getAvatar(): ?string
    {
        return (is_null($this->avatar)) ? '/assets/imgs/noavatar.png' : $this->avatar;
    }

    public function setAvatar(?string $avatar): self
    {
        $this->avatar = $avatar;

        return $this;
    }

    public function getResume(): ?string
    {
        return $this->resume;
    }

    public function setResume(?string $resume): self
    {
        $this->resume = $resume;

        return $this;
    }


    /**
     * @return File
     */
    public function getResumeFile ()
    {
        return $this->resumeFile;
    }


    /**
     * @param mixed $resumeFile
     */
    public function setResumeFile ($resumeFile): void
    {
        $this->resumeFile = $resumeFile;
        if ($resumeFile)
        {
            $this->updatedAt = new \DateTime();
        }
    }

    public function getDivision(): ?string
    {
        return $this->division;
    }

    public function setDivision(?string $division): self
    {
        $this->division = $division;

        return $this;
    }

    public function getPdfCv(): ?string
    {
        return $this->pdfCv;
    }

    public function setPdfCv(?string $pdfCv): self
    {
        $this->pdfCv = $pdfCv;

        return $this;
    }
	/**
	 * @return File
	 */
	public function getPdfCvFile()
	{
		return $this->pdfCvFile;
	}

	/**
	 * @param mixed $pdfCvFile
	 */
	public function setPdfCvFile($pdfCvFile): void
	{
		$this->pdfCvFile = $pdfCvFile;
	}

    public static function getAvailableFields()
    {
        $reflection = new \ReflectionClass(UserExtra::class);
        $properties = $reflection->getDefaultProperties();
        unset($properties['id']);
        unset($properties['createdAt']);
        unset($properties['createdBy']);
        unset($properties['updatedAt']);
        unset($properties['updatedBy']);
        unset($properties['deletedAt']);
        unset($properties['deletedBy']);
        return array_keys($properties);
    }
}
