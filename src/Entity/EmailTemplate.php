<?php

namespace App\Entity;

use App\Repository\EmailTemplateRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Gedmo;

/**
 * @ORM\Entity(repositoryClass=EmailTemplateRepository::class)
 * @ORM\HasLifecycleCallbacks()
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class EmailTemplate
{
    use AtAndBy;

    public const EMAIL_STATUS_PENDING = 'pending';
    public const EMAIL_STATUS_SENDING = 'sending';
    public const EMAIL_STATUS_SENT = 'sent';

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private $subject;

    /**
     * @ORM\Column(type="text")
     */
    private $body;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $sentAt;

    /**
     * @ORM\Column(type="datetime", nullable=true)
     */
    private $completedAt;

    /**
     * @ORM\OneToMany(targetEntity=EmailRecipient::class, mappedBy="template")
     */
    private $recipients;

    /**
     * @ORM\Column(type="string", length=255)
     */
    private string $status = self::EMAIL_STATUS_PENDING;


    public function __construct ()
    {
        $this->recipients = new ArrayCollection();
    }


    public function __toString ()
    {
        return $this->getSubject();
    }


    public function getId (): ?int
    {
        return $this->id;
    }


    public function getSubject (): ?string
    {
        return $this->subject;
    }


    public function setSubject (string $subject): self
    {
        $this->subject = $subject;

        return $this;
    }


    public function getBody (): ?string
    {
        return $this->body;
    }


    public function setBody (string $body): self
    {
        $this->body = $body;

        return $this;
    }


    public function getSentAt (): ?\DateTimeInterface
    {
        return $this->sentAt;
    }


    public function setSentAt (?\DateTimeInterface $sentAt): self
    {
        $this->sentAt = $sentAt;

        return $this;
    }


    public function getCompletedAt (): ?\DateTimeInterface
    {
        return $this->completedAt;
    }


    public function setCompletedAt (?\DateTimeInterface $completedAt): self
    {
        $this->completedAt = $completedAt;

        return $this;
    }


    /**
     * @return Collection|EmailRecipient[]
     */
    public function getRecipients (): Collection
    {
        return $this->recipients;
    }


    public function addRecipient (EmailRecipient $recipient): self
    {
        if (!$this->recipients->contains($recipient))
        {
            $this->recipients[] = $recipient;
            $recipient->setTemplate($this);
        }

        return $this;
    }


    public function removeRecipient (EmailRecipient $recipient): self
    {
        if ($this->recipients->removeElement($recipient))
        {
            // set the owning side to null (unless already changed)
            if ($recipient->getTemplate() === $this)
            {
                $recipient->setTemplate(null);
            }
        }

        return $this;
    }


    public function isSent (): bool
    {
        return !is_null($this->getSentAt());
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }


    public function getPendingRecipients()
    {
        $criteria = Criteria::create()
            ->andWhere(Criteria::expr()->isNull('sentAt'));
        return $this->getRecipients()->matching($criteria);
    }
}
