<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ForumLikes;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method ForumLikes|null find($id, $lockMode = null, $lockVersion = null)
 * @method ForumLikes|null findOneBy(array $criteria, array $orderBy = null)
 * @method ForumLikes[]    findAll()
 * @method ForumLikes[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ForumLikesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ForumLikes::class);
    }
}
