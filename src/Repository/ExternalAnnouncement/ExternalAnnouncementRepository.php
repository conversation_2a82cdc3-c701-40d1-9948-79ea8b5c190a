<?php

declare(strict_types=1);

namespace App\Repository\ExternalAnnouncement;

use App\Entity\Announcements;

interface ExternalAnnouncementRepository
{
    public const TYPE_FILE = 'file';
    public const TYPE_API = 'api';

    public function getClientName(): string;

    public function getType(): string;

    public function getAnnouncements(ExternalAnnouncementCriteria $criteria): Announcements;

    public function getAnnouncementsByFile(string $path): Announcements;

    public function sendAnnouncement(Announcements $announcements): void;
}
