<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\ChallengeAnswers;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method ChallengeAnswers|null find($id, $lockMode = null, $lockVersion = null)
 * @method ChallengeAnswers|null findOneBy(array $criteria, array $orderBy = null)
 * @method ChallengeAnswers[]    findAll()
 * @method ChallengeAnswers[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ChallengeAnswersRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ChallengeAnswers::class);
    }
}
