<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\User;
use App\Entity\UserVcmsProject;
use App\Entity\VcmsProject;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method UserVcmsProject|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserVcmsProject|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserVcmsProject[]    findAll()
 * @method UserVcmsProject[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserVcmsProjectRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserVcmsProject::class);
    }

    public function getByUserAndProject(User $user, VcmsProject $project)
    {
        return $this->createQueryBuilder('uvp')
            ->andWhere('uvp.user = :user')
            ->andWhere('uvp.project = :project')
            ->setParameters([
                'user' => $user,
                'project' => $project,
            ])
            ->getQuery()
            ->getOneOrNullResult();
    }
}
