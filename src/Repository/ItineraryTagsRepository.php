<?php

namespace App\Repository;

use App\Entity\Filter;
use App\Entity\Itinerary;
use App\Entity\ItineraryTags;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ItineraryTags>
 *
 * @method ItineraryTags|null find($id, $lockMode = null, $lockVersion = null)
 * @method ItineraryTags|null findOneBy(array $criteria, array $orderBy = null)
 * @method ItineraryTags[]    findAll()
 * @method ItineraryTags[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ItineraryTagsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ItineraryTags::class);
    }

    public function add(ItineraryTags $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ItineraryTags $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
