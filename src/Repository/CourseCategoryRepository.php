<?php

namespace App\Repository;

use App\Entity\CourseCategory;
use App\Entity\CourseCategoryTranslation;
use App\Entity\TypeCourse;
use App\Entity\User;
use App\Service\Course\GlobalFilter;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method CourseCategory|null find($id, $lockMode = null, $lockVersion = null)
 * @method CourseCategory|null findOneBy(array $criteria, array $orderBy = null)
 * @method CourseCategory[]    findAll()
 * @method CourseCategory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CourseCategoryRepository extends ServiceEntityRepository
{
    private GlobalFilter $globalFilter;
    public function __construct (ManagerRegistry $registry, GlobalFilter $globalFilter)
    {
        $this->globalFilter = $globalFilter;
        parent::__construct($registry, CourseCategory::class);
    }


    public function getNextSort ()
    {
        $maxSort = $this->createQueryBuilder('c')
            ->select('max(c.sort)')
            ->getQuery()
            ->getSingleScalarResult();

        return (int)$maxSort + 1;
    }


    public function getParents ($notIn = [])
    {
        $query = $this->createQueryBuilder('c')
            ->andWhere('c.parent IS NULL');

        if (!empty($notIn))
        {
            $query->andWhere($query->expr()->notIn('c.id', $notIn));
        }

        return $query->orderBy('c.sort', 'ASC')
            ->getQuery()
            ->getResult();
    }


    public function getList ()
    {
        $categories = $this->findAll();
        $list       = [];
        foreach ($categories as $category)
        {
            $list[$category->getId()] = $category->getHierarchicalName();
        }
        asort($list);

        return $list;
    }

    public function getListCourseCategoryTranslate(?string $locale = null): array
    {
        $categories = $this->findBy(['active' => true]);
        $cagoriesCourse  = [];

        foreach ($categories as $category)
        {
            $name = $category->getName();

            if(!empty($locale)){
                /** @var CourseCategoryTranslation $translations */
                $translations = $category->translate($locale, true);
                if(strlen(trim($translations->getName())) > 0) {
                    $name = $translations->getName();
                }
            }

            $idTypesCourse = [];

            foreach ($category->getTypeCourse() as $typeCourse) {
                $idTypesCourse[] = $typeCourse->getId();
            }

            $cagoriesCourse[] = [
                'id' => $category->getId(),
                'name' => $name,
                'idTypesCourse' => count($idTypesCourse) > 0 ? $idTypesCourse : [TypeCourse::TYPE_TELEFORMACION]
            ];
        }

        return $cagoriesCourse;
    }

    public function getCourseCategoriesTypeCourse()
    {
        $categories = $this->findAll();

        $cagoriesCourse = [];
        foreach ($categories as $category) {
            $idTypesCourse = [];

            foreach ($category->getTypeCourse() as $typeCourse) {
                $idTypesCourse[] = $typeCourse->getId();
            }

            $cagoriesCourse[] = [
                'id' => $category->getId(),
                'name' => $category->getName(),
                'idTypesCourse' => count($idTypesCourse) > 0 ? $idTypesCourse : [TypeCourse::TYPE_TELEFORMACION]
            ];
        }

        return $cagoriesCourse;
    }

    public function getCourseCategoryByQuery($query) 
    {
        $qb = $this->createQueryBuilder('cc')
                    ->select('cc.id', 'cc.name', 'cc.active');

        if (!empty($query)) $qb->andWhere("cc.name LIKE :query")->setParameter('query', "%$query%");

        return $qb->orderBy('cc.sort', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param User $user
     * @return CourseCategory[]
     */
    public function getCourseCategoriesByUser(User $user): array
    {
        return $this->globalFilter->getUserCategories($user->getId()); 
    }
}
