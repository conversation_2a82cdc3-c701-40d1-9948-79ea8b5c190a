<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\RoleplayBeginning;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<RoleplayBeginning>
 *
 * @method RoleplayBeginning|null find($id, $lockMode = null, $lockVersion = null)
 * @method RoleplayBeginning|null findOneBy(array $criteria, array $orderBy = null)
 * @method RoleplayBeginning[]    findAll()
 * @method RoleplayBeginning[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RoleplayBeginningRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RoleplayBeginning::class);
    }

    public function add(RoleplayBeginning $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(RoleplayBeginning $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getMaxOrder($projectId)
    {
        $qb = $this->createQueryBuilder('b');
        $qb->select('MAX(b.order) as max_order')
            ->where('b.project = :project')
            ->setParameter('project', $projectId);

        try {
            $result = $qb->getQuery()->getSingleResult();
        } catch (NoResultException|NonUniqueResultException $e) {
            $result = ['max_order' => 0];
        }

        return $result['max_order'] + 1;
    }
}
