<?php

namespace App\Security\OAuth2;

use League\OAuth2\Client\Provider\AbstractProvider;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use League\OAuth2\Client\Token\AccessToken;

abstract class CustomAbstractProvider extends AbstractProvider
{
    protected function fetchUserFilters(string $method, string $url, AccessToken $token, array $options = [])
    {
        $request = $this->getAuthenticatedRequest($method, $url, $token, $options);
        $response = $this->getParsedResponse($request);
        if (false === is_array($response)) {
            throw new \RuntimeException(
                'Invalid response received from Authorization Server. Expected JSON.'
            );
        }
        return $response;
    }

    public abstract function getUserFullInformationUrl($id): string;

    /**
     * @param AccessToken $token
     * @param mixed $id
     * @return array
     * @throws IdentityProviderException
     */
    public function fetchUserFullInformation(AccessToken $token, $id): array
    {
        $url = $this->getUserFullInformationUrl($id);
        $request = $this->getAuthenticatedRequest(AbstractProvider::METHOD_GET, $url, $token);
        $response = $this->getParsedResponse($request);

        if (false === is_array($response)) {
            throw new \RuntimeException(
                'Invalid response received from Authorization Server. Expected JSON.'
            );
        }

        return $response;
    }
}
