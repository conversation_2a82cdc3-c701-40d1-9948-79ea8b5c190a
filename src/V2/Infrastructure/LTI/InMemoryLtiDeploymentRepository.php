<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI;

use App\V2\Domain\LTI\Exceptions\LtiDeploymentNotFoundException;
use App\V2\Domain\LTI\Exceptions\LtiException;
use App\V2\Domain\LTI\LtiDeployment;
use App\V2\Domain\LTI\LtiDeploymentCollection;
use App\V2\Domain\LTI\LtiDeploymentCriteria;
use App\V2\Domain\LTI\LtiDeploymentRepository;
use App\V2\Domain\Shared\Collection\CollectionException;

class InMemoryLtiDeploymentRepository implements LtiDeploymentRepository
{
    private LtiDeploymentCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new LtiDeploymentCollection([]);
    }

    public function put(LtiDeployment $deployment): void
    {
        try {
            $result = $this->findOneBy(
                LtiDeploymentCriteria::createEmpty()
                    ->filterByRegistrationId($deployment->getRegistrationId())
                    ->filterByDeploymentId($deployment->getDeploymentId())
            );

            if (!$result->getId()->equals($deployment->getId())) {
                throw LtiException::deploymentIdMustBeUniqueInRegistrationContext();
            }
        } catch (LtiDeploymentNotFoundException) {
        }

        $items = $this->collection->allIndexedById();
        $items[$deployment->getId()->value()] = $deployment;
        $this->collection->replace($items);
    }

    public function findOneBy(LtiDeploymentCriteria $criteria): LtiDeployment
    {
        $result = $this->filterByCriteria($criteria);

        if ($result->isEmpty()) {
            throw new LtiDeploymentNotFoundException();
        }

        return $result->first();
    }

    public function findBy(LtiDeploymentCriteria $criteria): LtiDeploymentCollection
    {
        return $this->filterByCriteria($criteria);
    }

    public function delete(LtiDeployment $deployment): void
    {
        $data = $this->collection->allIndexedById();
        unset($data[$deployment->getId()->value()]);
        $this->collection->replace($data);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(LtiDeploymentCriteria $criteria): LtiDeploymentCollection
    {
        $collection = $this->collection->filter(
            fn (LtiDeployment $deployment) => (
                null === $criteria->getId() || $criteria->getId()->equals($deployment->getId())
            ) && (
                null === $criteria->getIds() || $criteria->getIds()->contains($deployment->getId())
            ) && (
                null === $criteria->getRegistrationId()
                || $criteria->getRegistrationId()->equals($deployment->getRegistrationId())
            ) && (
                null === $criteria->getDeploymentId()
                    || $criteria->getDeploymentId() === $deployment->getDeploymentId()
            )
        );

        return $collection->map(
            callback: fn (LtiDeployment $deployment) => new LtiDeployment(
                id: $deployment->getId(),
                registrationId: $deployment->getRegistrationId(),
                name: $deployment->getName(),
                deploymentId: $deployment->getDeploymentId(),
            )
        );
    }
}
