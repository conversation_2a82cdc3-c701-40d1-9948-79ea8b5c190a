<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\LTI;

use App\V2\Domain\LTI\Exceptions\LtiException;
use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\LTI\LtiRegistration;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationCriteria;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Domain\Shared\Collection\CollectionException;

class InMemoryLtiRegistrationRepository implements LtiRegistrationRepository
{
    private LtiRegistrationCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new LtiRegistrationCollection([]);
    }

    public function put(LtiRegistration $registration): void
    {
        try {
            $result = $this->findOneBy(
                LtiRegistrationCriteria::createEmpty()
                    ->filterByClientId($registration->getClientId())
            );

            if (!$result->getId()->equals($registration->getId())) {
                throw LtiException::clientIdMustBeUnique();
            }
        } catch (LtiRegistrationNotFoundException) {
        }

        $items = $this->collection->allIndexedById();
        $items[$registration->getId()->value()] = clone $registration;
        $this->collection->replace($items);
    }

    public function findOneBy(LtiRegistrationCriteria $criteria): LtiRegistration
    {
        $result = $this->filterByCriteria($criteria);

        if ($result->isEmpty()) {
            throw new LtiRegistrationNotFoundException();
        }

        return $result->first();
    }

    public function findBy(LtiRegistrationCriteria $criteria): LtiRegistrationCollection
    {
        return $this->filterByCriteria($criteria);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(LtiRegistrationCriteria $criteria): LtiRegistrationCollection
    {
        $collection = $this->collection->filter(
            fn (LtiRegistration $registration): bool => (
                null === $criteria->getId()
                || $registration->getId()->equals($criteria->getId())
            )
                && (
                    null === $criteria->getClientId()
                    || $registration->getClientId() === $criteria->getClientId()
                )
                && (
                    null === $criteria->getSearchString()
                    || str_contains($registration->getName(), $criteria->getSearchString())
                )
        );

        return $collection->map(
            fn (LtiRegistration $registration) => new LtiRegistration(
                id: $registration->getId(),
                name: $registration->getName(),
                clientId: $registration->getClientId(),
            )
        );
    }

    public function delete(LtiRegistration $ltiRegistration): void
    {
        $items = $this->collection->allIndexedById();
        unset($items[$ltiRegistration->getId()->value()]);
        $this->collection->replace($items);
    }
}
