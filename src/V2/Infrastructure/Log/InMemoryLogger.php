<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Log;

use App\V2\Application\Log\Logger;

class InMemoryLogger implements Logger
{
    private array $logMessages = [];

    public function error(string $message, ?\Throwable $exception = null, array $extraData = []): void
    {
        $this->logMessages[] = array_merge(
            ['message' => $message],
            $exception ? ['exception' => $exception] : [],
            $extraData,
        );
    }

    public function getLogMessages(): array
    {
        return $this->logMessages;
    }
}
