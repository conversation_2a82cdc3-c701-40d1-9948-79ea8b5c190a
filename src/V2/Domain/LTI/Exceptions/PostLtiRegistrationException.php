<?php

declare(strict_types=1);

namespace App\V2\Domain\LTI\Exceptions;

class PostLtiRegistrationException extends \Exception
{
    public const string PREFIX = 'Post LTI Registration error: ';

    public static function fromPrevious(\Throwable $previous): self
    {
        return new self(self::PREFIX . $previous->getMessage(), $previous->getCode(), $previous);
    }

    public static function clientIdMustBeUnique(string $clientId): self
    {
        return new self(
            self::PREFIX . "Client ID: $clientId must be unique",
            LtiException::CODE
        );
    }
}
