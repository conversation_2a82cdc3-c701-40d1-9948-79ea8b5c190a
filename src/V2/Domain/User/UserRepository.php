<?php

declare(strict_types=1);

namespace App\V2\Domain\User;

use App\Entity\User;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\User\Exception\UserNotFoundException;

interface UserRepository
{
    /**
     * @throws InfrastructureException
     */
    public function put(User $user): void;

    /**
     * @throws InfrastructureException
     * @throws UserNotFoundException
     */
    public function findOneBy(UserCriteria $criteria): User;

    /**
     * Find users by criteria.
     */
    public function findBy(UserCriteria $criteria): UserCollection;

    /**
     * Count users by criteria.
     */
    public function countBy(UserCriteria $criteria): int;
}
