<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\Course\Creator;

use App\V2\Application\Hydrator\HydratorCollection;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Course\Creator\CourseCreatorHydrationCriteria;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Hydrator\HydratorException;

class CourseCreatorHydratorCollection extends HydratorCollection
{
    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof CourseCreatorCollection || !$criteria instanceof CourseCreatorHydrationCriteria) {
            throw new HydratorException();
        }

        parent::hydrate($collection, $criteria);
    }
}
