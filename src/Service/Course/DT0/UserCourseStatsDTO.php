<?php

declare(strict_types=1);

namespace App\Service\Course\DT0;

use App\Entity\Course;
use App\Entity\User;

class UserCourseStatsDTO extends BaseStatsDTO
{
    public Course $course;
    public User $user;

    public function __construct(
        Course $course,
        User $user,
        ?\DateTime $dateFrom = null,
        ?\DateTime $dateTo = null,
        ?int $announcementId = null,
        ?bool $courseStartedOnTime = null,
        ?bool $courseFinishedOnTime = null
    ) {
        parent::__construct($dateFrom, $dateTo, $announcementId, $courseStartedOnTime, $courseFinishedOnTime);
        $this->course = $course;
        $this->user = $user;
    }

    public static function create(array $data): self
    {
        return new self(
            $data['course'],
            $data['user'],
            $data['dateFrom'] ?? null,
            $data['dateTo'] ?? null,
            $data['announcementId'] ?? null,
            $data['courseStartedOnTime'] ?? null,
            $data['courseFinishedOnTime'] ?? null
        );
    }
}
