<?php

declare(strict_types=1);

namespace App\Service\Course\Common;

use App\Controller\Admin\UserCrudController;
use App\Entity\Announcement;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\CourseCategoryTranslation;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Service\StatsUser\ResultGameService;
use App\Utils\TimeUtils;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class StatsService
{
    private EntityManagerInterface $em;


    protected TranslatorInterface $trans;

    private string $baseDir;

    public function __construct(
        EntityManagerInterface $em,
        KernelInterface $kernel,
        TranslatorInterface $translator
    ) {
        $this->em = $em;
        $this->baseDir = $kernel->getProjectDir() . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'courses';
        $this->trans = $translator;
    }

    public function getStats(Course $course, ?Announcement $announcement = null): array
    {
        return [
            'type' => 'course xd',
            'course' => $course->getId(),
            'announcement' => $announcement,
        ];
    }


    public function getBaseDir(): string
    {
        return $this->baseDir;
    }

    /**
     * Get count finished chapter and totalTimeSepent chapter.
     *
     * @return array If null return []
     */
    public function chapterGetTotalUsersFinished(Chapter $chapter, array $params): array
    {
        $data = [
            'finished' => 0,
            'totalTimeSpent' => 0
        ];
        if ($params['findUsers'] && empty($params['usersIds'])) {
            return $data;
        }

        try {
            $result = $this->em->getRepository(UserCourseChapter::class)
                ->getTotalUserFinishedChapters(
                    $chapter,
                    $params['findUsers'],
                    $params['usersIds'],
                    $params['finishedInTime'],
                    $params['dateFrom'],
                    $params['dateTo'],
                    $params['announcementId']
                );

            $data['finished'] = $result['finished'] ?? 0;
            $data['totalTimeSpent'] = $result['totalTimeSpent'] ?? 0;
        } catch (NoResultException | NonUniqueResultException $e) {
            $data['finished'] = 0;
            $data['totalTimeSpent'] = 0;
        }

        return $data;
    }

    public function chapterGetTotalUsersStarted(Chapter $chapter, array $params): int
    {
        if ($params['findUsers'] && empty($params['usersIds'])) {
            return 0;
        }

        try {
            $inProgress = $this->em->getRepository(UserCourseChapter::class)
                ->chapterGetTotalUsersStarted(
                    $chapter,
                    $params['findUsers'],
                    $params['usersIds'],
                    $params['finishedInTime'],
                    $params['dateFrom'],
                    $params['dateTo'],
                    $params['announcementId']
                );
        } catch (NoResultException | NonUniqueResultException $e) {
            $inProgress = 0;
        }

        return $inProgress;
    }

    public function getUserDataArray(Course $course, array $params): array
    {
        /** @var CourseCategory $courseCategory */
        $courseCategory = $params['courseCategory'];
        /** @var CourseCategoryTranslation $categoryTranslation */
        $categoryTranslation = $courseCategory->translate($course->getLocale());
        /** @var User $user */
        $user = $params['user'];
        /** @var UserCourse $userCourse */
        $userCourse = $params['userCourse'];
        $courseType = $params['courseType'];
        $adminUrlGenerator = $params['adminUrlGenerator'];

        $startDate = $params['startDate'] ? $params['startDate']->format('d-m-Y H:i:s') : '-';
        $finishDate = $params['finishDate'] ? $params['finishDate']->format('d-m-Y H:i:s') : '-';

        $uData = [
            'id' => $user->getId(),
            'firstname' => $user->getFirstname(),
            'lastname' => $user->getLastname(),
            'email' => $user->getEmail(),
            'chapterStarted' => 0,
            'totalChapters' => \count($params['chapters']),
            'status' => $params['status'],
            'chapterDetails' => [],
            'start' => $startDate,
            'end' => $finishDate,
            'type' => $courseType ? $courseType->getName() : null,
            'category' => $categoryTranslation->getName() ?: $courseCategory->getName(),
            'survey' => $userCourse ? $userCourse->getValuedAt() ? $userCourse->getValuedAt()->format('d-m-Y H:i:s') : '-' : '-',
            'urlView' => !\is_null($adminUrlGenerator) ? $adminUrlGenerator->setController(UserCrudController::class)->setAction(Crud::PAGE_DETAIL)
                ->setEntityId($user->getId())
                ->generateUrl() : null,
        ];

        return $uData;
    }

    public function curseTotalTimeAllChapter(Course $course, array $params): int
    {
        $courseTotalTime = 0;
        foreach ($course->getChapters() as $chapter) {
            $timeSpent = $this->em->getRepository(UserCourseChapter::class)
                ->getTimeByChapter($chapter, $params['userIds'], $params['announcement']);
            $courseTotalTime += $timeSpent['totalTime'];
        }

        return $courseTotalTime;
    }

    public function courseExcelDetailsGetInfoArray(Course $course, array $params): array
    {
        $userIds = $params['userIds'];
        $average_time = '-';
        $totalStarted = $params['totalStarted'];
        $totalFinished = $params['totalFinished'];
        $nps = $params['nps'];
        $totalUsers = $params['totalUsers'];

        $courseTotalTime = $this->curseTotalTimeAllChapter($course, [
            'userIds' => $userIds,
            'announcement' => $params['announcement'],
        ]);

        if (\count($userIds) > 0 && $courseTotalTime > 0 && $totalStarted > 0 && $totalFinished > 0) {
            $average_time = TimeUtils::formatTime(round(($courseTotalTime * ($totalFinished + $totalStarted)) /
                (\count($userIds) * ($totalFinished + $totalStarted)), 0));
        }

        // Calculos correspondientes..
        $notStarted = \count($userIds) > 0
            ? round(\count($userIds) - $totalStarted - $totalFinished, 1) : 0;
        $notStarted_percent = \count($userIds) > 0
            ? round(((\count($userIds) - $totalStarted - $totalFinished) * 100) /
                \count($userIds), 1) . '%' : '0%';
        $started_percent = $totalUsers > 0 ? round($totalStarted * 100 / $totalUsers, 1) . '%' : '0%';
        $finished_percent = $totalUsers > 0 ? round($totalFinished * 100 / $totalUsers, 1) . '%' : '0%';

        return [
            'id' => $course->getId(),
            'name' => $course->getName(),
            'nActivities' => $course->getChapters()->count(),
            'nUsers' => \count($userIds),
            'notStarted' => $notStarted,
            'notStarted_percent' => $notStarted_percent,
            'started' => $totalStarted ?? 0,
            'started_percent' => $started_percent,
            'finished' => $totalFinished ?? 0,
            'finished_percent' => $finished_percent,
            'time_total' => $courseTotalTime > 0 ? TimeUtils::formatTime($courseTotalTime) : '-',
            'average_time' => $average_time,
            'rating' => $nps ? $nps['nps'] : '-',
            'created_by' => $course->getCreatedBy()->getEmail(),
            'created_at' => $course->getCreatedAt()->format('d/m/Y H:i:s'),
        ];
    }

    public function courseExcelDetailsGetInfoPeopleArray(Course $course, array $params): array
    {
        $results = $params['results'];
        $timeStarted = $params['timeStarted'];
        $timeFinished = $params['timeFinished'];
        $nps = $params['nps'];

        $courseTotalTime = isset($results['courseTotalTime']) ? $results['courseTotalTime'] : 0;
        $notStarted = $course->getChapters()->count() - $timeStarted - $timeFinished;

        return [
            'id' => $course->getId(),
            'name' => $course->getName(),
            'nActivities' => $course->getChapters()->count(),
            'notStarted' => $notStarted > 0 ? $notStarted : '0',
            'started' => '' != $timeStarted ? $timeStarted : '0',
            'finished' => '' != $timeFinished ? $timeFinished : '0',
            'time_total' => $courseTotalTime > 0 ? TimeUtils::formatTime($courseTotalTime) : '-',
            'rating' => $nps ? $nps['nps'] : '-',
            'created_by' => $course->getCreatedBy()->getEmail(),
            'created_at' => $course->getCreatedAt()->format('d/m/Y H:i:s'),
        ];
    }

    public function getLastStartDateCourse(Course $course)
    {
        $result = $this->em->getRepository(UserCourse::class)->createQueryBuilder('uc')
            ->select('uc.startedAt')
            ->where('uc.course = :course')
            ->setParameter('course', $course)
            ->orderBy('uc.startedAt', 'DESC')
            ->getQuery()
            ->getArrayResult();

        if ($result) {
            return $result[0]['startedAt']->format('d/m/Y');
        } else {
            return $course->getCreatedAt()->format('d/m/Y');
        }
    }

    public function getComplateNameUser($user_id): string
    {
        $user = $this->em->getRepository(User::class)->find($user_id);
        $completeName = $user->getId();
        $completeName .= '-' . $this->str_without_accents($user->getFullName());

        return $completeName;
    }


    public function getCourseSaveFilesDirectoryName(Course $course): string
    {
        return $this->baseDir . DIRECTORY_SEPARATOR . $this->getCourseDirectoryName($course);
    }

    public function getCourseDirectoryName(Course $course): string
    {
        return $course->getId() . '-' . $this->str_without_accents($course->getName());
    }

    public function getUserSaveFileDirectoryName(Course $course, $user_id): string
    {
        $DirectoryComplete = $this->getComplateNameUser($user_id);
        $DirectoryComplete .= '-curso-' . $this->getCourseDirectoryName($course);

        return $this->baseDir . DIRECTORY_SEPARATOR . $DirectoryComplete;
    }

    public function str_without_accents($str, $charset = 'utf-8')
    {
        $str = htmlentities($str, ENT_NOQUOTES, $charset);

        $str = str_replace('/', '-', $str);
        $str = str_replace('[', '-', $str);
        $str = str_replace(']', '', $str);
        $str = str_replace('\\', '-', $str);
        $str = str_replace(' ', '-', $str);
        $str = preg_replace('#&([A-za-z])(?:acute|cedil|caron|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
        $str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
        $str = preg_replace('#&[^;]+;#', '', $str); // supprime les autres caractères

        $str = strtolower($str);

        return $str;   // or add this : mb_strtoupper($str); for uppercase :)
    }
}
