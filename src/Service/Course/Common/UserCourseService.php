<?php

declare(strict_types=1);

namespace App\Service\Course\Common;

use App\Controller\Admin\UserCrudController;
use App\Entity\Announcement;
use App\Entity\Chapter;
use App\Entity\Course;
use App\Entity\Filter;
use App\Entity\Itinerary;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Repository\ItineraryRepository;
use App\Service\Course\DT0\UserCourseStatsDTO;
use App\Service\SettingsService;
use App\Service\StatsUser\ResultGameService;
use App\Utils\UserCourse\CourseStatus;
use App\Utils\UserCourse\DateRange;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;

class UserCourseService
{
    private RequestStack $requestStack;
    private ParameterBagInterface $params;
    private EntityManagerInterface $em;
    private ResultGameService $resultGameService;
    private AdminUrlGenerator $adminUrlGenerator;
    private LoggerInterface $logger;
    private SettingsService $settings;
    private Security $security;

    public const STARTED_IN_TIME = 1;
    public const FINISHED_IN_TIME = 2;

    public function __construct(
        RequestStack $requestStack,
        ParameterBagInterface $params,
        EntityManagerInterface $em,
        ResultGameService $resultGameService,
        AdminUrlGenerator $adminUrlGenerator,
        LoggerInterface $logger,
        SettingsService $settings,
        Security $security,
    ) {
        $this->requestStack = $requestStack;
        $this->params = $params;
        $this->em = $em;
        $this->resultGameService = $resultGameService;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->logger = $logger;
        $this->settings = $settings;
        $this->security = $security;
    }

    /**
     * Gets the user data for a specific course from the request content.
     */
    public function getUserData(Course $course, User $user, ?Announcement $announcement): array
    {
        $content = $this->requestStack->getCurrentRequest()->request->all();

        return $this->getContentUserCourse($content, $course, $user, $announcement);
    }

    /**
     * Processes content to obtain user data related to a course.
     */
    public function getContentUserCourse(array $content, Course $course, User $user, ?Announcement $announcement): array
    {
        try {
            $courseStatus = CourseStatus::fromContent($content, $announcement);
            $dateRange = DateRange::fromContent($content, $announcement);

            $userCourseStatsDTO = UserCourseStatsDTO::create([
                'course' => $course,
                'user' => $user,
                'dateFrom' => $dateRange->dateFrom,
                'dateTo' => $dateRange->dateTo,
                'courseStartedOnTime' => $courseStatus->courseStartedOnTime,
                'courseFinishedOnTime' => $courseStatus->courseFinishedOnTime,
                'announcementId' => $announcement ? $announcement->getId() : null,
            ]);

            $userCourse = $this->em->getRepository(UserCourse::class)
                ->userCourseGetData($userCourseStatsDTO);

            return $this->buildUserData($user, $course, $userCourse);
        } catch (NonUniqueResultException $e) {
            $this->logger->error('Error retrieving user course data', ['exception' => $e]);

            return [];
        }
    }

    private function buildUserData(User $user, Course $course, ?UserCourse $userCourse): array
    {
        $chapters = $course->getChapters();
        $userCourseData = $this->getUserDataArray($user, $course, $chapters, $userCourse);
        $startedChaptersCount = 0;

        foreach ($chapters as $chapter) {
            $userCourseData['chapterDetails'][] = $this->getUserChaptersData($chapter, $userCourse, $startedChaptersCount);
        }

        $userCourseData['chapterStarted'] = $startedChaptersCount;

        return $userCourseData;
    }

    public function getUserChaptersData($chapter, $userCourse, &$started): array
    {
        // Get user course chapter data
        $userCourseChapter = $this->getUserCourseChapter($userCourse, $chapter);
        $chapterStatus = $userCourseChapter ? $userCourseChapter->getStatus() : UserCourseChapter::STATUS_NO_STARTED;

        if (UserCourseChapter::STATUS_FINISHED == $chapterStatus) {
            ++$started;
        }

        $attempts = $this->getAttemptsData($userCourseChapter);
        $timeSpent = $userCourseChapter ? $userCourseChapter->getTimeSpent() : 0;

        $chapterData = [
            'id' => $chapter->getId(),
            'name' => $chapter->getTitle(),
            'type' => $this->getChapterType($chapter),
            'icon' => $chapter->getType()->getIcon(),
            'start' => $this->formatDate($userCourseChapter ? $userCourseChapter->getStartedAt() : null),
            'end' => $this->formatDate($userCourseChapter ? $userCourseChapter->getFinishedAt() : null),
            'status' => $chapterStatus,
            'image' => $this->getChapterImage($chapter),
            'timeSpent' => $timeSpent,
            'attempts' => $attempts,
        ];

        return $chapterData;
    }

    private function getUserCourseChapter($userCourse, $chapter): ?UserCourseChapter
    {
        return $userCourse
            ? $this->em->getRepository(UserCourseChapter::class)->findOneBy([
                'userCourse' => $userCourse,
                'chapter' => $chapter,
            ])
            : null;
    }

    private function getChapterType(Chapter $chapter): string
    {
        $chapterType = $chapter->getType();
        $chapterTypeTranslation = $chapterType->translate($chapter->getCourse()->getLocale());

        return $chapterTypeTranslation->getName() ?: $chapterType->getNormalized();
    }

    private function getChapterImage($chapter): ?string
    {
        return $chapter->getImage()
            ? $this->params->get('app.chapter_uploads_path') . '/' . $chapter->getImage()
            : null;
    }

    private function formatDate(?\DateTimeInterface $date): ?string
    {
        return $date ? $date->format('c') : null;
    }

    private function getAttemptsData(?UserCourseChapter $userCourseChapter): array
    {
        if (!$userCourseChapter) {
            return [];
        }

        return array_map(function ($result) {
            $date = $result['date'] instanceof \DateTimeInterface
                ? $result['date']
                : \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $result['date']);

            return [
                'questions' => $result['questions'],
                'state' => $result['state'],
                'start' => $date->format('c'),
                'end' => $date->modify("+{$result['timeTotal']} seconds")->format('c'),
                'timeTotal' => $result['timeTotal'],
            ];
        }, $this->resultGameService->getResultGameAttempts($userCourseChapter));
    }

    private function getUserDataArray($user, $course, $chapters, ?UserCourse $userCourse = null): array
    {
        $category = $this->getCourseCategory($course);
        $courseType = $this->getCourseTypeName($course);
        $startedAt = $userCourse ? $userCourse->getStartedAt() : null;
        $finishedAt = $userCourse ? $userCourse->getFinishedAt() : null;

        $status = $userCourse ? $userCourse->getStatus() : UserCourse::STATUS_NO_STARTED;

        $uData = [
            'id' => $user->getId(),
            'firstname' => $user->getFirstname(),
            'lastname' => $user->getLastname(),
            'email' => $user->getEmail(),
            'chapterStarted' => 0,
            'totalChapters' => \count($chapters),
            'status' => $status,
            'chapterDetails' => [],
            'start' => $startedAt ? $startedAt->format('c') : null,
            'end' => $finishedAt ? $finishedAt->format('c') : null,
            'type' => $courseType,
            'category' => $category,
            'survey' => $userCourse ? ($userCourse->getValuedAt() ? $userCourse->getValuedAt()->format('c') : null) : null,
            'urlView' => $this->generateAdminUrl($user),
        ];

        return $uData;
    }

    private function getCourseCategory(Course $course): string
    {
        $category = $course->getCategory();
        $translation = $category->translate($course->getLocale());

        return $translation->getName() ?: $category->getName();
    }

    private function getCourseTypeName(Course $course): ?string
    {
        $type = $course->getTypeCourse();

        return $type ? $type->getName() : null;
    }

    private function generateAdminUrl(User $user): ?string
    {
        if (!$this->adminUrlGenerator) {
            return null;
        }

        return $this->adminUrlGenerator
            ->setController(UserCrudController::class)
            ->setAction(Crud::PAGE_DETAIL)
            ->setEntityId($user->getId())
            ->generateUrl();
    }

    /**
     * Get all users ids.
     *
     * @param Course $course  Course
     * @param array  $content Content
     */
    public function getAllUsersIds(Course $course, array $content = []): array
    {
        if ($course->getTranslation() && $course->getTranslation()->getId()) {
            $course = $course->getTranslation();
        }

        $usersIds = [];
        $categories = $this->getCategories($content);

        $userStatus = $content['userStatus'] ?? 'all';
        $userNameMail = $content['userNameMail'] ?? null;

        if (!empty($content['userId'])) {
            $usersIds[] = $content['userId'];
        } else {
            $manager = $this->getManager($content);
            $usersIds = $this->courseGetAllUsersIds($course, $content['source'] ?? [], $manager);

            if (empty($usersIds)) {
                return [];
            }

            if (!empty($categories)) {
                $filteredUsersIds = $this->em->getRepository(User::class)->getUsersIdsByCategories($categories, $manager);
                $usersIds = array_intersect($usersIds, $filteredUsersIds);
            }

            if (empty($usersIds)) {
                return [];
            }

            $activeUsersIds = $this->em->getRepository(User::class)->getUserIdsByActive($userStatus);
            $usersIds = array_intersect($usersIds, $activeUsersIds);

            if (empty($usersIds)) {
                return [];
            }

            if (
                !empty($content['dateFrom'])
                || !empty($content['dateTo'])
            ) {
                $courseStartedIntime = !empty($content['dateFrom']) ? true : false;
                $courseFinishedIntime = !empty($content['dateTo']) ? true : false;

                $usersIdsInDateRange = $this->getUserIdsByDateRangeActivityInCourse(
                    $course,
                    $courseStartedIntime,
                    $courseFinishedIntime,
                    $content['dateFrom'] ?? null,
                    $content['dateTo'] ?? null,
                );
                $usersIds = array_intersect($usersIds, $usersIdsInDateRange);
            }

            if ($userNameMail) {
                $usersIdsInUserNameMail = $this->em->getRepository(User::class)->getUserIdsByNameEmail($userNameMail);
                $usersIds = array_intersect($usersIds, $usersIdsInUserNameMail);
            }

            if (!empty($content['userCourseStatus'])) {
                $userStatusCourse = $content['userCourseStatus'] ?? 'all';
                $userCourseStatusFiltered = $this->em->getRepository(UserCourse::class)->getUserStatusCourseFiltered($userStatusCourse, $usersIds, $course);
                $usersIds = array_intersect($usersIds, $userCourseStatusFiltered);
            }
        }

        return $usersIds;
    }

    public function getCategories(array $content): array
    {
        $categories = [];
        foreach ($content as $name => $id) {
            if (false !== strpos($name, 'filter')) {
                $separated = explode('-', $name);
                $categories[$separated[1]] = [$id];
            }
        }

        return $categories;
    }

    /**
     * Get all users ids by course.
     *
     * @param Course $course Course
     * @param array  $source Source
     */
    public function courseGetAllUsersIds(
        Course $course,
        array $source = [],
        ?User $manager = null
    ): array {
        $usersIds = [];

        if ($course->getTranslation() && $course->getTranslation()->getId()) {
            $course = $course->getTranslation();
        }

        if (empty($source) || \in_array('open', $source)) {
            $usersInOpenCourse = $this->em->getRepository(User::class)->findUsersInOpenCourse($course, $manager);
            $usersIds = array_merge($usersIds, $usersInOpenCourse);
        }

        if (empty($source) || \in_array('itinerary', $source)) {
            $usersIds = array_merge($usersIds, $this->findUsersInItineraryByCourse($course, $manager));
        }

        if (empty($source) || \in_array('filter', $source)) {
            $usersIds = array_merge($usersIds, $this->findUserByCourseFilters($course, $manager));
        }

        return empty($usersIds) ? [] : array_unique($usersIds);
    }

    /**
     * Find users in itinerary by course.
     *
     * @param Course $course Course
     */
    public function findUsersInItineraryByCourse(Course $course, ?User $manager = null): array
    {
        $usersIds = [];
        /** @var ItineraryRepository $itineraryRepository */
        $itineraryRepository = $this->em->getRepository(Itinerary::class);

        try {
            $itineraries = $this->em->getRepository(Itinerary::class)->getItinerariesByCourse($course);
        } catch (\Exception $e) {
            throw new \RuntimeException('Failed', 500, $e);
        }

        foreach ($itineraries as $itinerary) {
            $users = $itineraryRepository->getUsersIdsFromItineraryFilters(itinerary: $itinerary, manager: $manager);
            $users = (empty($users)) ? $itineraryRepository->getUsersFromItineraryUser(itinerary: $itinerary, manager: $manager) : array_merge($users, $itineraryRepository->getUsersFromItineraryUser($itinerary, manager: $manager));

            foreach ($users as $u) {
                $usersIds[] = (int) $u['id'];
            }
        }

        return $usersIds;
    }

    /**
     * Find users by course filters.
     *
     * @param Course $course Course
     */
    public function findUserByCourseFilters(Course $course, ?User $manager = null): array
    {
        $filters = $this->em->getRepository(Filter::class)->getFiltersByCourse($course);
        $categories = [];
        foreach ($filters as $filter) {
            if (!\array_key_exists($filter['categoryId'], $categories)) {
                $categories[$filter['categoryId']] = [];
            }
            $categories[$filter['categoryId']][] = $filter['id'];
        }

        return empty($categories) ? [] : $this->em->getRepository(User::class)->getUsersIdsByCategories($categories, $manager);
    }

    private function getUserIdsByDateRangeActivityInCourse(
        Course $course,
        bool $startedInTime,
        bool $finishedInTime,
        ?string $dateFrom = null,
        ?string $dateTo = null
    ): array {
        $startedInTimeUserIds = [];
        $finishedInTimeUserIds = [];

        if (!empty($dateFrom)) {
            $dateFrom = \DateTimeImmutable::createFromFormat('Y-m-d', $dateFrom);
        }

        if (!empty($dateTo)) {
            $dateTo = \DateTimeImmutable::createFromFormat('Y-m-d', $dateTo);
        }

        if ($startedInTime) {
            $startedInTimeUserIds = $this->em->getRepository(UserCourse::class)->getUserIdsByDateRangeActivityInCourse(
                $course,
                self::STARTED_IN_TIME,
                $dateFrom,
                $dateTo
            );
        }

        if ($finishedInTime) {
            $finishedInTimeUserIds = $this->em->getRepository(UserCourse::class)->getUserIdsByDateRangeActivityInCourse(
                $course,
                self::FINISHED_IN_TIME,
                $dateFrom,
                $dateTo
            );
        }

        if ($startedInTime && !$finishedInTime) {
            return $startedInTimeUserIds;
        }
        if (!$startedInTime && $finishedInTime) {
            return $finishedInTimeUserIds;
        }
        if ($startedInTime && $finishedInTime) {
            return array_intersect($startedInTimeUserIds, $finishedInTimeUserIds);
        }
    }

    /**
     * Get total users who completed this course.
     *
     * @param bool $findUsers If true, find by passed usersIds
     *
     * @return int If $userIds is empty returns 0
     */
    public function courseGetTotalUsersStarted(Course $course, bool $findUsers = false, array $usersIds = [], $announcement = null): int
    {
        $repository = $this->em->getRepository(UserCourse::class);

        return $repository->courseGetTotalUsersStarted($course, $findUsers, $usersIds, $announcement);
    }

    /**
     * Get total users who completed this course.
     *
     * @param bool $findUsers If true, find by passed usersIds
     *
     * @return int If $userIds is empty returns 0
     */
    public function courseGetTotalUsersFinished(Course $course, bool $findUsers = false, array $usersIds = [], $announcement = null): int
    {
        $repository = $this->em->getRepository(UserCourse::class);

        return $repository->courseTotalUsersFinished($course, $findUsers, $usersIds, $announcement);
    }

    /**
     * Get total chapter who complete this course started.
     *
     * @return int if $chapterIds is empty return 0
     */
    public function courseGetTotalChaptersStarted(Course $course, array $usersIds = [], $announcement = null): int
    {
        $repository = $this->em->getRepository(UserCourseChapter::class);

        return $repository->courseGetTotalChapterStarted($course, $usersIds, $announcement);
    }

    /**
     * Get total who complete this course finished.
     *
     * @return int If $chapterIds is empty return 0
     */
    public function courseGetTotalChaptersFinished(Course $course, array $usersIds = [], $announcement = null): int
    {
        $repository = $this->em->getRepository(UserCourseChapter::class);

        return $repository->courseGetTotalChapterFinished($course, $usersIds, $announcement);
    }

    public function courseDataDetailsStats(Course $course)
    {
        $courseType = $course->getTypeCourse();
        $courseCategory = $course->getCategory();

        return [
            'name' => $course->getName(),
            'image' => $this->settings->get('app.course_uploads_path') . '/' . $course->getImage(),
            'category' => $courseCategory->getName(),
            'type' => $courseType->getName(),
        ];
    }

    public function getUsersFilteredByManager(User $manager, $users, $matchAllFilters = false): array
    {
        $managerFilters = $manager->getFilters()->toArray();

        $filteredUsers = array_filter($users, function ($user) use ($matchAllFilters, $managerFilters) {
            $userFilters = $this->em->getRepository(User::class)->find($user['id'])->getFilter()->toArray();

            if (\is_null($userFilters)) {
                return false;
            }

            $hasMatch = array_intersect($managerFilters, $userFilters);

            return $matchAllFilters ? \count($hasMatch) === \count($managerFilters) : \count($hasMatch) > 0;
        });

        return $filteredUsers;
    }

    private function getManager(array $content): ?User
    {
        $user = $this->security->getUser();
        if (!$user && isset($content['createdById'])) {
            $user = $this->em->getRepository(User::class)->find($content['createdById']);
        }

        return $user && $user->isManager() ? $user : null;
    }
}
