<?php

declare(strict_types=1);

namespace App\Service\Course;

use App\Entity\CourseCategory;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\ResultSetMapping;

class GlobalFilter
{
    private EntityManagerInterface $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }
    private string $courseFilters = "
        SELECT cf.course_id, GROUP_CONCAT(cf.filter_id) as filters
        FROM (
            SELECT course_id, CONCAT('-', filter_id, '-') as filter_id 
            FROM course_filter 
            ORDER BY course_id, filter_id) cf
        GROUP BY cf.course_id";

    public function getUserFilters(): string
    {
        return "
            SELECT u.user_id, GROUP_CONCAT(u.filters) as filters
            FROM (SELECT user_id, CONCAT('', filter_id, '-') as filters FROM user_filter 
            WHERE user_id = :USER_ID ORDER BY filter_id) u
        ";
    }

    private function getCoursesByUserId(): string
    {
        $userFilterSQ = $this->getUserFilters();

        return "
            SELECT course_filter.course_id
            FROM ($userFilterSQ) user_filter
            JOIN ($this->courseFilters) course_filter ON 
                course_filter.filters LIKE CONCAT('%', REPLACE(user_filter.filters, ',', '%'), '%')
            
            UNION 
            
            SELECT course_id
            FROM user_course
            WHERE user_id = :USER_ID 
        ";
    }

    /**
     * Get all categories based on user filters.
     *
     * @return CourseCategory[]
     */
    public function getUserCategories(int $userId = 0): array
    {
        $subquery = $this->getCoursesByUserId();
        $query = "
            SELECT 
                cc.id as category_id
            FROM ($subquery) ucf
            JOIN course c ON c.id = ucf.course_id
            JOIN course_category cc ON cc.id = c.category_id
            GROUP BY cc.id
        ";
        $rsm = new ResultSetMapping();
        $rsm->addScalarResult('category_id', 'category_id', 'integer');

        $result = $this->em->createNativeQuery($query, $rsm)
            ->setParameters(['USER_ID' => $userId])
            ->getResult();
        $ids = [];
        foreach ($result as $r) {
            $ids[] = $r['category_id'];
        }
        $ids = array_unique($ids);

        $qb = $this->em->getRepository(CourseCategory::class)->createQueryBuilder('cc');

        return $qb->where($qb->expr()->in('cc.id', $ids))
            ->getQuery()->getResult();
    }

    private function getCoursesData($userId = 0, $filterValue = '')
    {
        $subquery = $this->getCoursesByUserId();
        $query = "
            SELECT 
                c.id as course_id, 
                c.name as course_name, 
                cc.id as category_id, 
                cc.`name` as category_name,
                cs.slug, 
                cs.name as section_name
            FROM ($subquery) ucf
            JOIN course c ON c.id = ucf.course_id
            JOIN course_category cc ON cc.id = c.category_id
            JOIN course_section_course_category cscc ON cscc.course_category_id = cc.id
            JOIN course_section cs ON cs.id = cscc.course_section_id
            WHERE c.name LIKE :FILTER OR cc.name LIKE :FILTER
        ";
        $rsm = new ResultSetMapping();
        $rsm->addScalarResult('course_id', 'course_id', 'integer');
        $rsm->addScalarResult('course_name', 'course_name');
        $rsm->addScalarResult('category_id', 'category_id', 'integer');
        $rsm->addScalarResult('category_name', 'category_name');
        $rsm->addScalarResult('slug', 'slug');
        $rsm->addScalarResult('section_name', 'section_name');

        return $this->em->createNativeQuery($query, $rsm)
            ->setParameters(['FILTER' => "%$filterValue%", 'USER_ID' => $userId])
            ->getResult();
    }

    public function getFilterData($userId = 0, $filterValue = ''): array
    {
        $courses = $this->getCoursesData($userId, $filterValue);
        $groupData = [];
        foreach ($courses as $course) {
            $groupKey = "c{$course['category_id']}";
            if (empty($groupData[$groupKey])) {
                $groupData[$groupKey] = [
                    'name' => $course['category_name'],
                    'link' => "section/{$course['section_name']}/{$course['slug']}",
                    'items' => [],
                ];
            }

            $groupData[$groupKey]['items'][] = [
                'name' => $course['course_name'],
                'link' => "course/{$course['course_id']}",
            ];
        }

        return array_values($groupData);
    }

    public function cleanFilters(Array &$content): void{
        $cleaned = [];
        foreach ($content as $k => $value) {
            if (empty($value)) {
                continue;
            }
            $cleaned[$k] = $value;
        }
        $content = $cleaned;
    }

    public function findUsersFilter(Array $content, bool &$findUsers): void{
        $findUsers = false;
        foreach ($content as $k => $v) {
            if (false !== strpos($k, 'filter')) {
                $findUsers = true;
                break;
            }
        }
    }
}
