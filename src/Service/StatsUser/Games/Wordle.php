<?php

declare(strict_types=1);

namespace App\Service\StatsUser\Games;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\OrdenarMenormayor as EntityWordle;
use App\Entity\UserCourseChapter;
use App\Service\SettingsService;
use App\Service\StatsUser\Games\Traits\ScoreAttempGameTrait;
use Doctrine\ORM\EntityManagerInterface;

class Wordle implements GamesStragegyResultInterface
{
    use ScoreAttempGameTrait;

    private $em;
    private $settings;
    private UserCourseService $userCourseService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserCourseService $userCourseService
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userCourseService = $userCourseService;
    }

    public function getResultGame(UserCourseChapter $userCourseChapter): array
    {
        $attempts = [];
        $guessWordRepository = $this->em->getRepository(EntityWordle::class);

        if (!empty($userCourseChapter->getData()['attempts'])) {
            foreach ($userCourseChapter->getData()['attempts'] as $key => $attempt) {
                $questions = [];
                $timeTotalAttempt = 0;
                $lastDateInQuestions = null;

                if (empty($attempt)) {
                    continue;
                }

                foreach ($attempt as $at) {
                    $questionFind = $guessWordRepository->find($at['questionId']);

                    foreach ($at['attempts'] as $question) {
                        if (!$questionFind) {
                            continue;
                        }

                        $answerUser = $this->getAnswersUser($question['letters']);
                        $timeInQuestion = $question['time'] ? ceil($question['time']) : 0;
                        $lastDateInQuestions = $question['date'] ?? $userCourseChapter->getUpdatedAt()->format('Y-m-d H:i:s');
                        $questions[] = [
                            'id' => $questionFind->getId(),
                            'question' => $questionFind->getTitle(),
                            'correct' => $question['correct'] ?? false,
                            'answers' => [[
                                'answer' => $questionFind->getWordsArray(),
                                'userAnswer' => $answerUser,
                                'correct' => $this->isCorrectTheAnswerUser($questionFind->getWordsArray(), $answerUser),
                                'incorrect' => !$this->isCorrectTheAnswerUser($questionFind->getWordsArray(), $answerUser),
                            ]],
                        ];

                        $timeTotalAttempt += $timeInQuestion ?? 0;
                    }
                }

                if (!$questions) {
                    continue;
                }

                $attempForCalculateScore = array_merge(['answers' => $attempt], $this->getTotalQuestionAndTime($userCourseChapter));
                $attempts[] = [
                    'attempt' => $key + 1,
                    'timeTotal' => ceil($timeTotalAttempt),  // Agregamos el tiempo total del intento al array de resultados
                    'state' => $this->getStateGameAttemp($userCourseChapter, $this->userCourseService, $attempForCalculateScore),
                    'date' => $lastDateInQuestions,
                    'questions' => $questions,
                ];
            }
        }

        return $attempts;
    }

    private function getAnswersUser($letters): ?string
    {
        if (empty($letters)) {
            return null;
        }

        $word = [];
        foreach ($letters as $letter) {
            $word[] = $letter['letter'];
        }

        return implode('', $word);
    }

    private function isCorrectTheAnswerUser($word, $answerUser): bool
    {
        if (empty($word) || empty($answerUser)) {
            return false;
        }

        return strtolower($word) === strtolower($answerUser);
    }

    private function getTotalQuestionAndTime(UserCourseChapter $userCourseChapter): array
    {
        $questions = $this->em->getRepository(EntityWordle::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
        $totalQuestions = count($questions) ?? 0;
        $timeTotal = 0;
        foreach ($questions as $question) {
            $timeTotal += $question->getTime();
        }

        return [
            'totalQuestions' => $totalQuestions,
            'timeTotal' => $timeTotal,
        ];
    }
}
