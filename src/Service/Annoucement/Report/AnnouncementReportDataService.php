<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Report;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementNotification;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\AnnouncementUserDigitalSignature;
use App\Entity\ChatChannel;
use App\Entity\ChatChannelUser;
use App\Entity\ChatMessage;
use App\Entity\ChatServer;
use App\Entity\Course;
use App\Entity\HistoryDeliveryTask;
use App\Entity\Nps;
use App\Entity\TaskCourse;
use App\Entity\TaskUser;
use App\Entity\User;
use App\Entity\UserTime;
use App\Enum\ChapterType as EnumChapterType;
use App\Service\Annoucement\Admin\AnnouncementUserService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\ResultSetMappingBuilder;
use Doctrine\ORM\UnexpectedResultException;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class AnnouncementReportDataService
{
    private EntityManagerInterface $em;
    protected LoggerInterface $logger;
    private AnnouncementUserService $announcementUserService;
    private TranslatorInterface $translator;
    private SettingsService $settings;

    public function __construct(EntityManagerInterface $em, LoggerInterface $logger, AnnouncementUserService $announcementUserService, TranslatorInterface $translator, SettingsService $settings)
    {
        $this->em = $em;
        $this->logger = $logger;
        $this->announcementUserService = $announcementUserService;
        $this->translator = $translator;
        $this->settings = $settings;
    }

    /**
     * @param AnnouncementGroup[] $groups
     */
    private function getAnnouncementGroupsIds(array $groups): array
    {
        $ids = [];
        foreach ($groups as $group) {
            $ids[] = $group->getId();
        }

        return $ids;
    }

    /**
     * @param AnnouncementGroup[] $announcementGroups
     */
    public function groupInfoData(
        Announcement $announcement,
        array $announcementGroups,
        bool $bConfirmedUsers = false,
        bool $bNumberSessions = false
    ): array {
        $groupIds = array_map(function ($group) {
            return $group->getId();
        }, $announcementGroups);

        $qb = $this->em->getRepository(AnnouncementGroup::class)->createQueryBuilder('ag')
            ->select([
                'ag.id',
                'ag.code',
                'a.code AS announcementName',
                'c.name AS courseName',
                'COALESCE(COUNT(DISTINCT au.id), 0) AS totalUsers',
                "CONCAT(t.firstName, ' ', t.lastName) AS tutorName",
                'at.email AS tutorEmail',
            ])
            ->join('ag.announcement', 'a')
            ->leftJoin('ag.announcementUsers', 'au')
            ->leftJoin('a.course', 'c')
            ->leftJoin('ag.announcementTutor', 'at')
            ->join('at.tutor', 't')
            ->where('ag.announcement = :announcement')
            ->setParameter('announcement', $announcement)
            ->groupBy('ag.id');

        if (!empty($announcementGroups)) {
            $qb->andWhere('ag.id IN (:groupIds)')
               ->setParameter('groupIds', $groupIds);
        }

        if ($bConfirmedUsers) {
            $qb->leftJoin('ag.announcementUsers', 'au_confirmed', Join::WITH, 'au_confirmed.announcementGroup = ag AND au_confirmed.isConfirmationAssistance = true')
                ->addSelect('COALESCE(count(DISTINCT au_confirmed.id), 0) as totalConfirmed');
        }

        if ($bNumberSessions) {
            $qb->leftJoin('ag.announcementGroupSessions', 'ags')
                ->addSelect('COALESCE(count(DISTINCT ags.id), 0) as totalSessions');
        }

        $result = $qb->getQuery()->getResult();

        return $result;
    }

    /**
     * @param AnnouncementGroup[] $announcementGroups
     */
    public function groupInfoUsersData(
        Announcement $announcement,
        array $announcementGroups = [],
        bool $bConfirmedAssistance = false,
        bool $bAccountValidated = false
    ): array {
        if (!empty($announcementGroups) && !$this->areValidAnnouncementGroups($announcementGroups)) {
            throw new \InvalidArgumentException('Todos los elementos de $announcementGroups deben ser instancias de AnnouncementGroup.');
        }

        $groupIds = array_map(function (AnnouncementGroup $group) {
            return $group->getId();
        }, $announcementGroups);

        $qb = $this->em->getRepository(AnnouncementUser::class)->createQueryBuilder('au')
            ->select([
                'u.id AS userId',
                "COALESCE(JSON_UNQUOTE(JSON_EXTRACT(u.meta, '$.HRP')), '-') AS meta",
                'u.firstName',
                'u.lastName',
                'u.email',
            ])
            ->join('au.user', 'u')
            ->where('au.announcement = :announcement')
            ->setParameter('announcement', $announcement);

        if ($bConfirmedAssistance) {
            $qb->addSelect("
                CASE 
                    WHEN au.isConfirmationAssistance = true THEN 'Sí' 
                    ELSE 'No' 
                END AS isConfirmationAssistance
            ");
        }

        if ($bAccountValidated) {
            $qb->addSelect("
                CASE 
                    WHEN u.validated = true THEN 'Sí' 
                    ELSE 'No' 
                END AS isAccountValidated
            ");
        }

        if (!empty($announcementGroups)) {
            $qb->andWhere('au.announcementGroup IN (:groupIds)')
               ->setParameter('groupIds', $groupIds);
        }

        $result = $qb->getQuery()->getArrayResult();

        return $result;
    }

    private function areValidAnnouncementGroups(array $announcementGroups): bool
    {
        foreach ($announcementGroups as $group) {
            if (!$group instanceof AnnouncementGroup) {
                return false;
            }
        }

        return true;
    }

    public function groupInfoSessionsData(
        Announcement $announcement,
        array $announcementGroups = []
    ): array {
        if (!empty($announcementGroups) && !$this->areValidAnnouncementGroups($announcementGroups)) {
            throw new \InvalidArgumentException('Todos los elementos de $announcementGroups deben ser instancias de AnnouncementGroup.');
        }

        $groupIds = array_map(function (AnnouncementGroup $group) {
            return $group->getId();
        }, $announcementGroups);

        $qb = $this->em->getRepository(AnnouncementGroupSession::class)->createQueryBuilder('ags')
            ->select([
                'ags.id AS sessionId',
                'ags.session_number as sessionNumber',
                "DATE_FORMAT(ags.startAt, '%d-%m-%Y %H:%i:%s') AS startAt",
                "DATE_FORMAT(ags.finishAt, '%d-%m-%Y %H:%i:%s') AS finishAt",
                'ags.entryMargin',
                'ags.exitMargin',
                'ags.place',
                "CASE
                    WHEN :currentTime < ags.startAt THEN 'PENDING'
                    WHEN :currentTime > ags.finishAt THEN 'FINISHED'
                    ELSE 'IN_PROGRESS'
                END AS status"
            ])
            ->innerJoin('ags.announcementGroup', 'ag')
            ->innerJoin('ag.announcement', 'a')
            ->where('ag.announcement = :announcement')
            ->setParameter('announcement', $announcement)
            ->setParameter('currentTime', new \DateTimeImmutable('now'));

        if (!empty($groupIds)) {
            $qb->andWhere('ag.id IN (:groupIds)')
                ->setParameter('groupIds', $groupIds);
        }

        $result = $qb->getQuery()->getArrayResult();

        return $result;
    }

    public function groupInfoSessionsDataRaw(
        Announcement $announcement,
        array $announcementGroups = []
    ): array {
        if (!empty($announcementGroups) && !$this->areValidAnnouncementGroups($announcementGroups)) {
            throw new \InvalidArgumentException('Todos los elementos de $announcementGroups deben ser instancias de AnnouncementGroup.');
        }

        $groupIds = array_map(function (AnnouncementGroup $group) {
            return $group->getId();
        }, $announcementGroups);

        $qb = $this->em->createQueryBuilder();

        $qb->select([
            'ags.id AS sessionId',
            'CONCAT(\'Número de sesión \', ags.session_number) AS sessionNumber',
            'DATE_FORMAT(ags.startAt, \'%d-%m-%Y %H:%i:%s\') AS startAt',
            'DATE_FORMAT(ags.finishAt, \'%d-%m-%Y %H:%i:%s\') AS finishAt',
            'CONCAT(ags.entryMargin, \'%\') AS entryMargin',
            'CONCAT(ags.exitMargin, \'%\') AS exitMargin',
            'ags.place AS place',
            'CASE
                WHEN :currentTime < ags.startAt THEN \'Pendiente\'
                WHEN :currentTime > ags.finishAt THEN \'Finalizado\'
                ELSE \'En progreso\'
            END AS status',
            'COALESCE(JSON_EXTRACT(ags.studentAssistance, \'$\'), \'[]\') AS studentAssistance',
            'COALESCE(JSON_EXTRACT(ags.assistance, \'$\'), \'[]\') AS tutorAssistance',
            'ag.id AS groupId'
        ])
            ->from('App\Entity\AnnouncementGroupSession', 'ags')
            ->innerJoin('ags.announcementGroup', 'ag')
            ->innerJoin('ag.announcement', 'a')
            ->where('a.id = :announcement')
            ->setParameter('announcement', $announcement->getId())
            ->setParameter('currentTime', (new \DateTimeImmutable('now'))->format('Y-m-d H:i:s'));

        if (!empty($groupIds)) {
            $qb->andWhere($qb->expr()->in('ag.id', ':groupIds'))
                ->setParameter('groupIds', $groupIds);
        }

        $query = $qb->getQuery();
        $result = $query->getArrayResult();

        $groupMap = [];
        foreach ($announcementGroups as $group) {
            $groupMap[$group->getId()] = $group;
        }

        foreach ($result as &$row) {
            $groupId = $row['groupId'] ?? null;
            $studentAssistanceJson = $row['studentAssistance'] ?? '[]';
            $tutorAssistanceJson = $row['tutorAssistance'] ?? '[]';
            $studentAssistance = json_decode($studentAssistanceJson, true) ?: [];
            $tutorAssistance = json_decode($tutorAssistanceJson, true) ?: [];

            $studentAssistanceMap = [];
            foreach ($studentAssistance as $item) {
                if (isset($item['id'])) {
                    $studentAssistanceMap[$item['id']] = $item;
                }
            }

            $tutorAssistanceMap = [];
            foreach ($tutorAssistance as $item) {
                if (isset($item['id'])) {
                    $tutorAssistanceMap[$item['id']] = $item;
                }
            }

            $totalUsers = 0;
            $userIDs = [];
            if ($groupId && isset($groupMap[$groupId])) {
                /** @var AnnouncementGroup $group */
                $group = $groupMap[$groupId];
                $announcementUsers = $group->getAnnouncementUsers();
                $totalUsers = \count($announcementUsers);

                $announcementUsersArray = $announcementUsers->toArray();

                $userIDs = array_map(function ($announcementUser) {
                    return $announcementUser->getUser()->getId();
                }, $announcementUsersArray);
            }

            $totalAssistance = 0;

            foreach ($userIDs as $userId) {
                $assisted = false;

                if (isset($studentAssistanceMap[$userId])
                    && isset($studentAssistanceMap[$userId]['assistance'])
                    && true === $studentAssistanceMap[$userId]['assistance']) {
                    $assisted = true;
                } elseif (isset($tutorAssistanceMap[$userId])
                          && isset($tutorAssistanceMap[$userId]['assistance'])
                          && true === $tutorAssistanceMap[$userId]['assistance']) {
                    $assisted = true;
                }

                if ($assisted) {
                    ++$totalAssistance;
                }
            }

            $attendancePercentage = ($totalUsers > 0) ? ($totalAssistance / $totalUsers) * 100 : 0;

            $row['_studentAssistanceRaw'] = $row['studentAssistance'];
            $row['_tutorAssistanceRaw'] = $row['tutorAssistance'];

            $row['totalUsers'] = (string) $totalUsers;
            $row['totalAssistance'] = (string) $totalAssistance;

            $row['assistance'] = (string) round($attendancePercentage, 2) . '%';

            unset($row['studentAssistance'], $row['tutorAssistance'], $row['groupId']);
        }

        return $result;
    }

    public function assistanceSessionsDataRaw(
        array $dataSessions
    ): array {
        $result = [];

        $decodedSessions = [];

        $allUserIds = [];

        foreach ($dataSessions as $row) {
            $sessionId = $row['sessionId'] ?? null;
            if (!$sessionId) {
                continue;
            }

            if (!\array_key_exists($sessionId, $result)) {
                $result[$sessionId] = [];
            }

            $studentAssistance = isset($row['_studentAssistanceRaw'])
                ? json_decode($row['_studentAssistanceRaw'], true)
                : [];
            $tutorAssistance = isset($row['_tutorAssistanceRaw'])
                ? json_decode($row['_tutorAssistanceRaw'], true)
                : [];

            $decodedSessions[$sessionId][] = [
                'studentAssistance' => $studentAssistance,
                'tutorAssistance' => $tutorAssistance,
            ];

            foreach ($studentAssistance as $studentItem) {
                if (!empty($studentItem['id']) && !empty($studentItem['assistance'])) {
                    $allUserIds[] = $studentItem['id'];
                }
            }
            foreach ($tutorAssistance as $tutorItem) {
                if (!empty($tutorItem['id']) && !empty($tutorItem['assistance'])) {
                    $allUserIds[] = $tutorItem['id'];
                }
            }
        }

        $allUserIds = array_unique($allUserIds);

        $userMap = [];
        if (!empty($allUserIds)) {
            /** @var User[] $users */
            $users = $this->em->getRepository(User::class)->findBy(['id' => $allUserIds]);
            foreach ($users as $user) {
                $userMap[$user->getId()] = $user;
            }
        }

        foreach ($decodedSessions as $sessionId => $assistArray) {
            foreach ($assistArray as $assist) {
                $studentAssistanceMap = $this->buildAssistanceMap($assist['studentAssistance']);
                $tutorAssistanceMap = $this->buildAssistanceMap($assist['tutorAssistance']);

                $combinedUserIds = array_unique(array_merge(
                    array_keys($studentAssistanceMap),
                    array_keys($tutorAssistanceMap)
                ));

                foreach ($combinedUserIds as $userId) {
                    if (!isset($userMap[$userId])) {
                        continue;
                    }

                    $studentAssisted = !empty($studentAssistanceMap[$userId]['assistance']);
                    $tutorAssisted = !empty($tutorAssistanceMap[$userId]['assistance']);
                    $assisted = $studentAssisted || $tutorAssisted;

                    if ($assisted) {
                        $user = $userMap[$userId];

                        $controlTutor = $tutorAssistanceMap[$userId]['percent'] ?? '';

                        $controlUsuario = isset($tutorAssistanceMap[$userId]) ? 'Si' : 'No';

                        $firmaDig = $this->checkDigitalSignature($sessionId, $userId);

                        $result[$sessionId][] = [
                            'userId' => $userId,
                            'userCode' => $user->getMeta()['HRP'] ?? '-',
                            'userName' => $user->getFirstName(),
                            'userLastName' => $user->getLastName(),
                            'userEmail' => $user->getEmail(),
                            'controlUsuario' => $controlUsuario,
                            'controlTutor' => $controlTutor,
                            'firmaDigital' => $firmaDig ? 'Si' : 'No',
                        ];
                    }
                }
            }
        }

        return $result;
    }

    private function buildAssistanceMap(array $assistanceData): array
    {
        $assistanceMap = [];
        foreach ($assistanceData as $item) {
            if (!empty($item['id']) && isset($item['assistance']) && true === $item['assistance']) {
                $assistanceMap[$item['id']] = $item;
            }
        }

        return $assistanceMap;
    }

    private function checkDigitalSignature(int $sessionId, int $userId): bool
    {
        // Construimos el QueryBuilder
        $qb = $this->em->createQueryBuilder();

        $qb->select('COUNT(s.id)')
            ->from(AnnouncementUserDigitalSignature::class, 's')
            ->join('s.announcementUser', 'aus')
            ->join('aus.user', 'u')
            ->join('s.announcementGroupSession', 'ags')
            // Verificamos la sesión concreta
            ->where($qb->expr()->eq('ags.id', ':sessionId'))
            // Verificamos el usuario concreto
            ->andWhere($qb->expr()->eq('u.id', ':userId'))
            ->setParameter('sessionId', $sessionId)
            ->setParameter('userId', $userId);

        // Obtenemos la Query de Doctrine
        $query = $qb->getQuery();

        // Logueamos los parámetros
        $params = $query->getParameters(); // Es un ArrayCollection

        // Ejecutamos la consulta para obtener el número de firmas
        $count = (int) $query->getSingleScalarResult();

        // Devolvemos true si hay al menos una, false en caso contrario
        return $count > 0;
    }

    public function groupInfoForumData(
        Announcement $announcement,
        array $announcementGroups = []
    ): array {
        // Validar que todos los elementos del array sean instancias de AnnouncementGroup
        if (!empty($announcementGroups) && !$this->areValidAnnouncementGroups($announcementGroups)) {
            throw new \InvalidArgumentException('Todos los elementos de $announcementGroups deben ser instancias de AnnouncementGroup.');
        }

        // Extraer IDs de los grupos
        $groupIds = array_map(fn (AnnouncementGroup $group) => $group->getId(), $announcementGroups);

        // Construir la consulta
        $qb = $this->em->getRepository(ChatChannel::class)->createQueryBuilder('cc')
            ->select([
                'cc.id AS channelId',
                'cc.code AS channelCode',
                'COUNT(DISTINCT tc.id) AS threads',
                'COUNT(DISTINCT u.id) AS n_users',
                'COUNT(cm.id) AS n_comments',
                'SUM(CASE WHEN u.id = tutor.id THEN 1 ELSE 0 END) AS n_tutor_comments'
            ])
            ->leftJoin('cc.chatThreads', 'tc')
            ->leftJoin('tc.chatMessages', 'cm')
            ->leftJoin('cm.user', 'u')
            ->leftJoin('cc.announcementGroup', 'ag') // Definir el alias 'ag'
            ->leftJoin('ag.announcementTutor', 'tutor') // Corregir el JOIN con 'tutor'
            ->where('cc.type = :channelType')
            ->andWhere('cc.announcement = :announcement')
            ->setParameter('channelType', ChatChannel::TYPE_FORUM)
            ->setParameter('announcement', $announcement)
            ->groupBy('cc.id');

        // Filtrar por grupos si se proporcionan
        if (!empty($groupIds)) {
            $qb->andWhere('cc.announcementGroup IN (:groupIds)')
                ->setParameter('groupIds', $groupIds);
        }

        // Ejecutar la consulta y obtener resultados
        $result = $qb->getQuery()->getArrayResult();

        return $result;
    }

    public function onSiteGroupInfo_sessionsSheetData(Course $course, array $groups): array
    {
        $sessions = [];
        foreach ($groups as $group) {
            foreach ($group->getAnnouncementGroupSessions() as $announcementGroupSession) {
                $sessions[] = [
                    'id' => $announcementGroupSession->getId(),
                    'name' => $course->getName(),
                    'startAt' => $announcementGroupSession->getStartAt()->format('c'),
                    'entryMargin' => $announcementGroupSession->getEntryMargin(),
                    'finishAt' => $announcementGroupSession->getFinishAt()->format('c'),
                    'exitMargin' => $announcementGroupSession->getExitMargin(),
                    'status' => $announcementGroupSession->getStatus(),
                    'assistancePercent' => 'UNDEFINED'
                ];
            }
        }

        return $sessions;
    }

    /**
     * @param AnnouncementGroup[] $announcementGroups
     */
    public function onSiteAssistance_infoSheetData(Announcement $announcement, array $announcementGroups): array
    {
        $qb = $this->em->getRepository(AnnouncementGroup::class)->createQueryBuilder('ag')
            ->select('ag.id', 'ag.code', 'a.code as announcementName', 'count(au.id) as totalUsers')
            ->addSelect('count(ags.id) as totalSessions')
            ->addSelect('concat(t.firstName, t.lastName) as tutorName', 'at.email as tutorEmail')
            ->join('ag.announcement', 'a')
            ->leftJoin('ag.announcementUsers', 'au')
            ->leftJoin('ag.announcementGroupSessions', 'ags')
            ->leftJoin('ag.announcementTutor', 'at')
            ->join('at.tutor', 't')
            ->where('ag.announcement = :announcement')
            ->setParameter('announcement', $announcement)
            ->addGroupBy('ag.id')
        ;

        if (\count($announcementGroups) > 0) {
            $qb->andWhere($qb->expr()->in('ag.id', $this->getAnnouncementGroupsIds($announcementGroups)));
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * @param AnnouncementGroup[] $announcementGroups
     */
    public function onSiteAssistance_usersSheetData(Announcement $announcement, array $announcementGroups): array
    {
        $qb = $this->em->getRepository(AnnouncementUser::class)
            ->createQueryBuilder('au')
            ->select('u.id', 'u.code', 'u.firstName', 'u.lastName', 'u.email', 'au.isConfirmationAssistance', 'u.validated')
            ->join('au.user', 'u')
            ->where('au.announcement =:announcement')
            ->setParameter('announcement', $announcement);
        if (\count($announcementGroups) > 0) {
            $qb->join('au.announcementGroup', 'ag')
                ->andWhere($qb->expr()->in('ag.id', $this->getAnnouncementGroupsIds($announcementGroups)));
        }

        return $qb->getQuery()
            ->getResult();
    }

    /**
     * @param AnnouncementGroup[] $announcementGroups
     */
    public function connections_tutorInfoSheetData(Announcement $announcement, array $announcementGroups): array
    {
        $qb = $this->em->getRepository(AnnouncementTutor::class)
            ->createQueryBuilder('at')
            ->select('u.id', 'CONCAT(u.firstName, u.lastName) as name', 'at.email')
            ->addSelect('count(DISTINCT connections.id) as totalConnections')
            ->join('at.tutor', 'u')
            ->leftJoin('at.announcementTutorConnections', 'connections')
            ->where('at.announcement =:announcement')
            ->setParameter('announcement', $announcement)
            ->addGroupBy('at.id');

        if (\count($announcementGroups) > 0) {
            $qb->leftJoin('at.announcementGroup', 'ag')
                ->andWhere($qb->expr()->in('ag.id', $this->getAnnouncementGroupsIds($announcementGroups)));
        }

        return $qb->getQuery()
            ->getResult();
    }

    public function connections_tutorConnectionsSheetData(array $announcementGroups): array
    {
        $data = [];

        if (\count($announcementGroups) > 0) {
            foreach ($announcementGroups as $group) {
                $tutor = $group->getAnnouncementTutor();
                if (!$tutor) {
                    continue;
                }

                $userTutor = $tutor->getTutor();
                if (!$userTutor) {
                    continue;
                }

                foreach ($tutor->getAnnouncementTutorConnections() as $connection) {
                    $timeInSeconds = $connection->getTime() ?? 0;
                    $formattedTime = gmdate('H:i:s', $timeInSeconds);

                    /** @var \DateTimeImmutable $createdAt */
                    $createdAt = $connection->getCreatedAt();

                    $rowData = [
                        'id' => $connection->getId(),
                        'date' => $createdAt->format('d-m-Y'),
                        'ip' => $connection->getIp(),
                        'time' => $formattedTime,
                        'start' => $createdAt->format('H:i:s'),
                        'finish' => $createdAt->modify("+$timeInSeconds seconds")->format('H:i:s'),
                    ];

                    $data[] = $rowData;
                }
            }
        }

        return $data;
    }

    public function connections_participantConnectionsSheetData(array $announcementGroups): array
    {
        $data = [];

        if (\count($announcementGroups) > 0) {
            foreach ($announcementGroups as $group) {
                $announcementUsers = $group->getAnnouncementUsers();

                foreach ($announcementUsers as $announcementUser) {
                    $data[] = $this->announcementUserService->getConexionsAnnouncementUser($announcementUser, true);
                }
            }
        }

        return $data;
    }

    /**
     * @param AnnouncementGroup[] $announcementGroups
     */
    public function getChatReportData(Announcement $announcement, array $announcementGroups): array
    {
        /** @var ChatServer $server */
        $server = $this->em->getRepository(ChatServer::class)->getServer(
            ChatServer::TYPE_ANNOUNCEMENT,
            $announcement->getId(),
            true
        );
        $data = [];

        foreach ($announcementGroups as $announcementGroup) {
            // Per groups, this is the public channel
            $mainChatGroup = $this->em->getRepository(ChatChannel::class)->getChannelByEntityType(
                $server,
                Announcement::CHAT_CHANNEL_GROUP_CHAT,
                $announcementGroup->getId(),
                AnnouncementGroup::TYPE_ANNOUNCEMENT_GROUP
            );
            if (!$mainChatGroup) {
                continue;
            }
            try {
                $totalUsersInChat = $this->em->getRepository(ChatChannelUser::class)->createQueryBuilder('ccu')
                    ->select('count(ccu.id) as total')
                    ->where('ccu.channel = :channel')
                    ->setParameter('channel', $mainChatGroup)
                    ->getQuery()
                    ->getSingleScalarResult();
            } catch (UnexpectedResultException $e) {
                $totalUsersInChat = 0;
            }

            try {
                $nChats = $this->em->getRepository(ChatMessage::class)->createQueryBuilder('cm')
                    ->where('cm.channel = :channel')
                    ->setParameter('channel', $mainChatGroup)
                    ->getQuery()
                    ->getSingleScalarResult();
            } catch (UnexpectedResultException $e) {
                $nChats = 0;
            }

            $data[] = [
                'id' => $mainChatGroup->getId(),
                'code' => $announcementGroup->getCode(),
                'nChats' => $nChats,
                'nUsers' => $totalUsersInChat
            ];
        }

        return $data;
    }

    public function getAllMessages(ChatChannel $channel)
    {
        $currentTimezone = date_default_timezone_get();
        date_default_timezone_set('UTC'); // Set the timezone to utc before getting the data

        $data = $this->em->getRepository(ChatMessage::class)->createQueryBuilder('cm')
            ->addSelect('cm.id')
            ->addSelect('u.id as userId', 'u.code as userCode', 'u.firstName', 'u.lastName', 'u.email')
            ->addSelect('cm.createdAt', 'cm.message')
            ->join('cm.user', 'u')
            ->where('cm.channel = :channel')
            ->setParameter('channel', $channel)
            ->addOrderBy('cm.createdAt', 'ASC')
            ->getQuery()
            ->getResult()
        ;

        date_default_timezone_set($currentTimezone);

        return $data;
    }

    public function getAllMaterialsInfo(Announcement $announcement): array
    {
        $typeMapping = [
            1 => 'PDF',
            2 => 'VIDEO',
            3 => 'COMPRESSED',
            4 => 'IMAGE',
            5 => 'OFFICE',
            6 => 'TXT',
            7 => 'AUDIO',
        ];

        $data = [];
        foreach ($announcement->getMaterialCourses() as $materialCourse) {
            $type = $materialCourse->getTypeMaterial();
            $translatedType = $typeMapping[$type] ?? (false !== array_search($type, $typeMapping) ? $type : 'UNKNOWN');

            $data[] = [
                'id' => $materialCourse->getId(),
                'name' => $materialCourse->getOriginalName(),
                'type' => $translatedType,
                'date' => $materialCourse->getCreatedAt()->format('d-m-Y H:i:s'),
                'published' => $materialCourse->isIsVisible() ? 'Sí' : 'No',
                'download' => $materialCourse->getIsDownload() ? 'Sí' : 'No',
            ];
        }

        return $data;
    }

    public function getTasksInfo(Announcement $announcement): array
    {
        $data = [];
        foreach ($announcement->getTaskCourses() as $taskCourse) {
            $data[] = [
                'id' => $taskCourse->getId(),
                'title' => $taskCourse->getTitle(),
                'startAt' => $taskCourse->getStartDate()->format('c'),
                'finishAt' => $taskCourse->getDateDelivery()->format('c'),
                'files' => $taskCourse->getFilesTasks()->count(),
                'published' => $taskCourse->isIsVisible() ? 'YES' : 'NO'
            ];
        }

        return $data;
    }

    /**
     * @param AnnouncementGroup[] $announcementGroups
     */
    public function getTasksInfo_userActivity(Announcement $announcement, array $announcementGroups): array
    {
        $tasks = [];
        /** @var TaskCourse $taskCourse */
        foreach ($this->em->getRepository(TaskCourse::class)->findBy(['announcement' => $announcement]) as $taskCourse) {
            $taskGroups = $taskCourse->getTaskCourseGroups();
            $gIds = [];
            foreach ($taskGroups as $taskGroup) {
                $gIds[] = $taskGroup->getId();
            }

            $tasks[$taskCourse->getId()] = [
                'task' => $taskCourse,
                'groups' => $gIds// If empty apply to all groups
            ];
        }

        $announcementData = [];
        foreach ($announcementGroups as $group) {
            $groupData = [];

            foreach ($tasks as $task) {
                if (!empty($task['groups']) && !\in_array($group->getId(), $task['groups'])) {
                    continue; // Task not available for the current group
                }

                $taskInfo = $this->em->getRepository(AnnouncementUser::class)
                    ->createQueryBuilder('au')
                    ->select('u.id', 'u.code', 'u.firstName', 'u.lastName', 'u.email')
                    ->addSelect('t.id as taskId', 't.title as taskTitle', 't.startDate', 't.dateDelivery')
                    ->addSelect('hdt.createdAt as dateUserDelivery', 'IFNULL(hdt.state, -1)')
                    ->join('au.user', 'u')
                    ->leftJoin('u.taskUsers', 'tu')
                    ->leftJoin('tu.historyDeliveryTasks', 'hdt')
                    ->leftJoin('tu.task', 't')
                    ->andWhere('tu.task = :task')
                    ->andWhere('au.announcementGroup = :group')
                    ->setParameter('task', $tasks['task'])
                    ->setParameter('group', $group)
                    ->getQuery()
                    ->getResult()
                ;

                $data = [];
                foreach ($taskInfo as $info) {
                    $data[] = [
                        'userId' => $info['id'],
                        'userCode' => $info['code'],
                        'firstName' => $info['firstName'],
                        'lastName' => $info['lastName'],
                        'email' => $info['email'],
                        'taskId' => $info['taskId'],
                        'taskTitle' => $info['taskTitle'],
                        'startDate' => $info['startDate'],
                        'deadline' => $info['dateDelivery'],
                        'timeSpent' => $this->getTasksTimeSpent($info['startDate'], $info['dateUserDelivery']),
                        'status' => $info['state'] >= 0 ? HistoryDeliveryTask::DESCRIPTIVES_STATE[$info['state']] : 'SIN ENTREGAR'
                    ];
                }

                $groupData = array_merge($groupData, $data);
            }

            $announcementData[$group->getId()] = $groupData;
        }

        return $announcementData;
    }

    private function getTasksTimeSpent(\DateTime $start, ?\DateTime $userDelivered = null): string
    {
        if (null == $userDelivered) {
            return '--';
        }
        $timeSpentDiff = $userDelivered->diff($start);

        return $timeSpentDiff->d . ' dias, ' . $timeSpentDiff->h . ':' . $timeSpentDiff->m;
    }

    /**
     * @param AnnouncementGroup[] $announcementGroups
     */
    public function getStudentsConnectionReport(Announcement $announcement, array $announcementGroups): array
    {
        $qb = $this->em->getRepository(UserTime::class)
            ->createQueryBuilder('ut')
            ->leftJoin('ut.user', 'u')
            ->leftJoin('u.announcements', 'au')
            ->leftJoin('au.announcementGroup', 'ag')
            ->where('ut.announcement = :announcement')
            ->setParameter('announcement', $announcement)
        ;
        $qb->andWhere($qb->expr()->in('ag.id', $this->getAnnouncementGroupsIds($announcementGroups)));
        $qb->select('ut.id as connectionId', 'ut.createdAt', 'ut.ip', 'ut.time', 'ut.createdAt', 'ut.createdAt as finishAt')
            ->addSelect('u.id as userId', 'u.code as userCode', 'u.firstName', 'u.lastName', 'u.email')
        ;

        $data = $qb->getQuery()->getResult();

        return $data;
    }

    private function transformChapterRow(array $row, string $locale): array
    {
        if (isset($row['time_spent'])) {
            $seconds = (int) $row['time_spent'];
            $row['time_spent'] = gmdate('H:i:s', $seconds);
        }

        if (isset($row['chapter_type'])) {
            switch ($row['chapter_type']) {
                case EnumChapterType::TYPE_GAME:
                    $row['chapter_type'] = $this->translator->trans(
                        'chapter_type.games_test',
                        [],
                        'chapters',
                        $locale
                    );
                    break;
                case EnumChapterType::TYPE_CONTENT:
                    $row['chapter_type'] = $this->translator->trans(
                        'chapter_type.content',
                        [],
                        'chapters',
                        $locale
                    );
                    break;
            }
        }

        return $row;
    }

    public function activity_infoSheetData(array $announcementGroups): array
    {
        $sql = '
            SELECT 
                c.id AS chapter_id,
                c.title AS chapter_title,
                ct.name AS chapter_type_name,
                ct.type AS chapter_type,
                DATE_FORMAT(a.start_at, \'%d-%m-%Y %H:%i:%s\') AS start_at,
                DATE_FORMAT(a.finish_at, \'%d-%m-%Y %H:%i:%s\') AS finish_at,
                CONCAT(
                    ROUND((
                        COUNT(DISTINCT CASE WHEN ucc.finished_at IS NOT NULL THEN u.id END) 
                        * 100.0 
                        / COUNT(DISTINCT u.id)
                    ), 2), \' %\'
                ) AS approval_percentage, 
                SUM(ucc.time_spent) AS time_spent
            FROM 
                chapter c
            INNER JOIN 
                chapter_type ct ON ct.id = c.type_id
            INNER JOIN 
                course cou ON cou.id = c.course_id
            INNER JOIN 
                announcement a ON a.course_id = cou.id
            INNER JOIN 
                announcement_group ag ON ag.announcement_id = a.id
            INNER JOIN 
                announcement_user au ON au.announcement_group_id = ag.id
            LEFT JOIN 
                user_course uc ON uc.course_id = cou.id AND uc.user_id = au.user_id
            INNER JOIN 
                user u ON u.id = uc.user_id
            LEFT JOIN 
                user_course_chapter ucc ON ucc.user_course_id = uc.id AND ucc.chapter_id = c.id
            WHERE 
                ag.id IN (:announcementGroups)
            GROUP BY 
                c.id, cou.id, cou.name, c.title
            ORDER BY 
                c.id ASC, cou.id ASC
        ';

        $rsm = new ResultSetMappingBuilder($this->em);
        $rsm->addScalarResult('chapter_id', 'chapter_id');
        $rsm->addScalarResult('chapter_title', 'chapter_title');
        $rsm->addScalarResult('chapter_type_name', 'chapter_type_name');
        $rsm->addScalarResult('chapter_type', 'chapter_type');
        $rsm->addScalarResult('start_at', 'start_at');
        $rsm->addScalarResult('finish_at', 'finish_at');
        $rsm->addScalarResult('approval_percentage', 'approval_percentage');
        $rsm->addScalarResult('time_spent', 'time_spent');

        $query = $this->em->createNativeQuery($sql, $rsm);
        $query->setParameter('announcementGroups', $announcementGroups, \Doctrine\DBAL\Connection::PARAM_STR_ARRAY);

        try {
            $results = $query->getResult();
            $locale = $this->settings->get('app.defaultLanguage'); // Ej: 'es'

            foreach ($results as &$row) {
                $row = $this->transformChapterRow($row, $locale);
            }

            return $results;
        } catch (\Doctrine\ORM\ORMException $e) {
            $this->logger->error('[AnnouncementReportDataService] activityInfoInfoSheetDataRawSQL() Error: ' . $e->getMessage());
            throw $e;
        }
    }

    public function activity_detailedInfoSheetData(array $announcementGroups): array
    {
        $announcementGroupIds = array_map(function ($group) {
            if (\is_object($group) && method_exists($group, 'getId')) {
                return $group->getId();
            }

            return $group;
        }, $announcementGroups);

        $sql = "
            SELECT
                u.id AS user_id, 
                JSON_UNQUOTE(JSON_EXTRACT(u.meta, '$.HRP')) AS hrp,
                u.first_name, 
                u.last_name,
                u.email, 
                c.id AS chapter_id, 
                c.title AS chapter_title,
                ct.name AS chapter_type_name,
                ct.type AS chapter_type,
                DATE_FORMAT(ucc.started_at, '%d-%m-%Y %H:%i:%s') AS started_at,
                DATE_FORMAT(ucc.finished_at, '%d-%m-%Y %H:%i:%s') AS finished_at,
                ucc.time_spent AS time_spent,
                CASE 
                    WHEN ucc.finished_at IS NOT NULL THEN 'Finalizado'
                    WHEN ucc.finished_at IS NULL AND (ucc.data IS NULL OR JSON_LENGTH(ucc.data) = 0) THEN 'Sin empezar'
                    WHEN ucc.finished_at IS NULL AND JSON_LENGTH(ucc.data) > 0 THEN 'En proceso'
                END AS status
            FROM 
                chapter c
            INNER JOIN 
                chapter_type ct ON ct.id = c.type_id
            INNER JOIN 
                course cou ON cou.id = c.course_id
            INNER JOIN 
                announcement a ON a.course_id = cou.id
            INNER JOIN 
                announcement_group ag ON ag.announcement_id = a.id
            INNER JOIN 
                announcement_user au ON au.announcement_group_id = ag.id
            LEFT JOIN 
                user_course uc ON uc.course_id = cou.id AND uc.user_id = au.user_id
            INNER JOIN 
                user u ON u.id = uc.user_id
            LEFT JOIN 
                user_course_chapter ucc ON ucc.user_course_id = uc.id AND ucc.chapter_id = c.id
            WHERE 
                ag.id IN (:announcementGroups)
            ORDER BY 
                u.id ASC, c.id ASC
        ";

        $rsm = new ResultSetMappingBuilder($this->em);
        $rsm->addScalarResult('user_id', 'user_id');
        $rsm->addScalarResult('hrp', 'hrp');
        $rsm->addScalarResult('first_name', 'first_name');
        $rsm->addScalarResult('last_name', 'last_name');
        $rsm->addScalarResult('email', 'email');
        $rsm->addScalarResult('chapter_id', 'chapter_id');
        $rsm->addScalarResult('chapter_title', 'chapter_title');
        $rsm->addScalarResult('chapter_type_name', 'chapter_type_name');
        $rsm->addScalarResult('chapter_type', 'chapter_type');
        $rsm->addScalarResult('started_at', 'started_at');
        $rsm->addScalarResult('finished_at', 'finished_at');
        $rsm->addScalarResult('status', 'status');
        $rsm->addScalarResult('time_spent', 'time_spent');

        $query = $this->em->createNativeQuery($sql, $rsm);
        $query->setParameter(
            'announcementGroups',
            $announcementGroupIds,
            \Doctrine\DBAL\Connection::PARAM_INT_ARRAY
        );

        try {
            $results = $query->getResult();
            $locale = $this->settings->get('app.defaultLanguage');

            foreach ($results as &$row) {
                $row = $this->transformChapterRow($row, $locale);
            }

            return $results;
        } catch (\Doctrine\ORM\ORMException $e) {
            $this->logger->error(
                '[AnnouncementReportDataService] activity_detailedInfoSheetData() Error: ' . $e->getMessage()
            );
            throw $e;
        }
    }

    public function notification_infoSheetData(Announcement $announcement, array $announcementGroups): array
    {
        $em = $this->em;
        $announcementGroupIds = $this->getAnnouncementGroupsIds($announcementGroups);

        $qb = $em->getRepository(AnnouncementNotification::class)
            ->createQueryBuilder('an')
            ->select('an.id', "DATE_FORMAT(an.sendAt, '%d-%m-%Y %H:%i:%s') AS sendAt", 'an.text')
            ->innerJoin('an.announcementNotificationGroups', 'ang')
            ->where('an.announcement = :announcement')
            ->andWhere('ang.announcementGroup IN (:groupIds)')
            ->setParameter('announcement', $announcement)
            ->setParameter('groupIds', $announcementGroupIds);

        $data = $qb->getQuery()->getResult();

        foreach ($data as &$item) {
            if (isset($item['text'])) {
                $item['text'] = strip_tags($item['text']);
            }
        }

        return $data;
    }

    public function material_infoSheetData(Announcement $announcement, array $announcementGroups): array
    {
        $em = $this->em;

        $announcementGroupIds = $this->getAnnouncementGroupsIds($announcementGroups);

        $qb = $em->getRepository(AnnouncementNotification::class)
            ->createQueryBuilder('an')
            ->select('an.id', 'an.sendAt', 'an.text')
            ->innerJoin('an.announcementNotificationGroups', 'ang')
            ->where('an.announcement = :announcement')
            ->andWhere('ang.announcementGroup IN (:groupIds)')
            ->setParameter('announcement', $announcement)
            ->setParameter('groupIds', $announcementGroupIds);

        $data = $qb->getQuery()->getResult();

        return $data;
    }

    public function task_infoSheetData(Announcement $announcement, array $announcementGroups): array
    {
        $em = $this->em;

        $announcementGroupIds = $this->getAnnouncementGroupsIds($announcementGroups);

        if (empty($announcementGroupIds)) {
            return [];
        }

        $qb = $em->getRepository(TaskCourse::class)
            ->createQueryBuilder('tc')
            ->select([
                'tc.id AS taskId',
                'tc.title',
                'tc.startDate',
                'tc.dateDelivery',
                'COUNT(DISTINCT ft.id) AS filesTaskCount',
                'tc.isVisible'
            ])
            ->innerJoin('tc.taskCourseGroups', 'tcg')
            ->leftJoin('tc.filesTasks', 'ft')
            ->where('tc.announcement = :announcement')
            ->andWhere('tcg.announcementGroup IN (:groupIds)')
            ->groupBy('tc.id')
            ->setParameter('announcement', $announcement)
            ->setParameter('groupIds', $announcementGroupIds);

        $data = $qb->getQuery()->getResult();

        return $data;
    }

    public function task_activitySheetData(Announcement $announcement, array $announcementGroups): array
    {
        $em = $this->em;

        $announcementGroupIds = $this->getAnnouncementGroupsIds($announcementGroups);

        if (empty($announcementGroupIds)) {
            return [];
        }

        $qb = $em->getRepository(TaskUser::class)
            ->createQueryBuilder('tu')
            ->select([
                'u.id AS userId',
                "COALESCE(JSON_UNQUOTE(JSON_EXTRACT(u.meta, '$.HRP')), '-') AS meta",
                'u.firstName',
                'u.lastName',
                'u.email',

                'tc.id AS taskId',
                'tc.title',
                "DATE_FORMAT(tc.startDate, '%d-%m-%Y %H:%i:%s') AS startDate",
                "DATE_FORMAT(tc.dateDelivery, '%d-%m-%Y %H:%i:%s') AS dateDelivery",
                'hdt.state AS latestState'
            ])
            ->innerJoin('tu.user', 'u')
            ->innerJoin('tu.task', 'tc')
            ->innerJoin('tc.taskCourseGroups', 'tcg')
            ->leftJoin(
                'tu.historyDeliveryTasks',
                'hdt',
                'WITH',
                'hdt.id = (
                    SELECT MAX(hdt3.id)
                    FROM App\Entity\HistoryDeliveryTask hdt3
                    WHERE hdt3.taskUser = tu
                )'
            )
            ->where('tc.announcement = :announcement')
            ->andWhere('tcg.announcementGroup IN (:groupIds)')
            ->setParameter('announcement', $announcement)
            ->setParameter('groupIds', $announcementGroupIds)
            ->groupBy('tu.id');

        $data = $qb->getQuery()->getResult();

        // $descriptiveStates = HistoryDeliveryTask::DESCRIPTIVES_STATE;
        $descriptiveStatesEs = [
            HistoryDeliveryTask::STATE_PENDING => 'Pendiente',
            HistoryDeliveryTask::STATE_DELIVERED => 'Entregado',
            HistoryDeliveryTask::STATE_REVISION => 'En revisión',
            HistoryDeliveryTask::STATE_REJECTED => 'Rechazado',
            HistoryDeliveryTask::STATE_APPROVED => 'Aprobado',
        ];

        foreach ($data as &$record) {
            if (isset($record['latestState'])) {
                $state = (int) $record['latestState'];
                $record['latestState'] = $descriptiveStatesEs[$state] ?? 'Desconocido';
            } else {
                $record['latestState'] = 'Sin estado';
            }
        }

        return $data;
    }

    public function survey_infoSheetData(Announcement $announcement, array $announcementGroups): array
    {
        $em = $this->em;

        $announcementGroupIds = $this->getAnnouncementGroupsIds($announcementGroups);

        if (empty($announcementGroupIds)) {
            return [];
        }

        $subquery = $em->getRepository(AnnouncementUser::class)
        ->createQueryBuilder('au')
        ->select('COUNT(DISTINCT u.id)')
        ->innerJoin('au.user', 'u')
        ->innerJoin('au.announcementGroup', 'ag')
        ->where('ag.id IN (:groupIds)')
        ->setParameter('groupIds', $announcementGroupIds)
        ->getQuery()
        ->getSingleScalarResult();

        $qb = $em->getRepository(Nps::class)
            ->createQueryBuilder('n')
            ->select([
                's.id AS survey_id',
                's.name AS survey_name',
                'COUNT(DISTINCT nq.id) AS nps_question_count',
                'u_creator.firstName AS creator_first_name',
                's.active AS is_active',
                '(COUNT(DISTINCT u.id) * 100.0 / :totalUsers) AS percentage_completed'
            ])

            ->innerJoin('n.question', 'nq')
            ->innerJoin('nq.survey', 's')
            ->innerJoin('s.surveyAnnouncements', 'sa')
            ->innerJoin('sa.announcement', 'a', 'WITH', 'a = n.announcement')
            ->innerJoin('a.announcementGroups', 'ag')
            ->leftJoin('n.user', 'u')
            ->leftJoin('s.createdBy', 'u_creator')

            ->leftJoin(
                AnnouncementUser::class,
                'au2',
                Join::WITH,
                'au2.user = u AND au2.announcementGroup IN (:groupIds)'
            )

            ->where('sa.announcement = :announcementId')
            ->andWhere('ag.id IN (:groupIds)')
            ->andWhere('au2.id IS NOT NULL')

            ->setParameter('announcementId', $announcement->getId())
            ->setParameter('groupIds', $announcementGroupIds)
            ->setParameter('totalUsers', $subquery);

        $data = $qb->getQuery()->getArrayResult();

        return $data;
    }

    public function survey_activitySheetData(Announcement $announcement, array $announcementGroups): array
    {
        $em = $this->em;

        $announcementGroupIds = $this->getAnnouncementGroupsIds($announcementGroups);

        if (empty($announcementGroupIds)) {
            return [];
        }

        $qb = $em->getRepository(Nps::class)
            ->createQueryBuilder('n')
            ->select([
                'u.id AS user_id',
                "COALESCE(JSON_UNQUOTE(JSON_EXTRACT(u.meta, '$.HRP')), '-') AS user_code",
                'u.firstName AS user_first_name',
                'u.lastName AS user_last_name',
                'u.email AS user_email',

                'nq.id AS question_id',
                'nq.question AS question_text',
                'nq.type AS question_type',
                'n.value AS response',
                'DATE_FORMAT(n.createdAt, \'%d-%m-%Y %H:%i:%s\') AS date_opened'
            ])
            ->innerJoin('n.question', 'nq')
            ->innerJoin('nq.survey', 's')
            ->innerJoin('s.surveyAnnouncements', 'sa')
            ->innerJoin('sa.announcement', 'a', 'WITH', 'a = n.announcement')
            ->innerJoin('a.announcementGroups', 'ag')
            ->leftJoin('n.user', 'u')

            ->leftJoin(
                AnnouncementUser::class,
                'au2',
                Join::WITH,
                'au2.user = u AND au2.announcementGroup IN (:groupIds)'
            )

            ->where('sa.announcement = :announcementId')
            ->andWhere('ag.id IN (:groupIds)')
            ->andWhere('au2.id IS NOT NULL')

            ->setParameter('announcementId', $announcement->getId())
            ->setParameter('groupIds', $announcementGroupIds)

            ->getQuery()
            ->getArrayResult();

        $questionTypeMap = [
            'nps' => 'Valoración',
            'text' => 'Respuesta abierta',
            'checkbox' => 'Selección multiple',
            'radio' => 'Selección de respuesta',
            'switch' => 'Selector de Respuesta Sí/No'
        ];

        $data = [];
        foreach ($qb as $row) {
            $questionType = !empty($row['question_type']) ? strtolower($row['question_type']) : '';
            $mappedQuestionType = isset($questionTypeMap[$questionType]) ? $questionTypeMap[$questionType] : $row['question_type'];

            $data[] = [
                'user_id' => $row['user_id'],
                'user_code' => $row['user_code'],
                'user_first_name' => $row['user_first_name'],
                'user_last_name' => $row['user_last_name'],
                'user_email' => $row['user_email'],
                'question_id' => $row['question_id'],
                'question_text' => $row['question_text'],
                'question_type' => $mappedQuestionType,
                'response' => $row['response'],
                'date_opened' => $row['date_opened']
            ];
        }

        return $data;
    }
}
