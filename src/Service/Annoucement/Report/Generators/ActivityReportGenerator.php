<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Report\Generators;

use App\Service\Annoucement\Report\AnnouncementContainer;
use App\Service\Annoucement\Report\AnnouncementReportConstants;
use App\Service\Annoucement\Report\AnnouncementReportDataService;
use App\Utils\SpreadsheetUtil;
use Psr\Log\LoggerInterface;

class ActivityReportGenerator
{
    private AnnouncementReportDataService $announcementReportDataService;
    private AnnouncementReportConstants $announcementReportConstants;
    private LoggerInterface $logger;

    public function __construct(
        AnnouncementReportDataService $announcementReportDataService,
        AnnouncementReportConstants $announcementReportConstants,
        LoggerInterface $logger
    ) {
        $this->announcementReportDataService = $announcementReportDataService;
        $this->announcementReportConstants = $announcementReportConstants;
        $this->logger = $logger;
    }

    public function generate(
        AnnouncementContainer $announcementContainer,
        string $announcementDir,
        callable $initSheetCallback,
        bool $isOnlineMode
    ): void {
        $headersInfoActivity = $isOnlineMode ?
        $this->announcementReportConstants::ONLINE_ACTIVITIES_HEADERS['info'] :
            [];

        $headersActivityDetail = $isOnlineMode ?
        $this->announcementReportConstants::ONLINE_ACTIVITIES_HEADERS['activities'] :
            [];

        $report = new SpreadsheetUtil('Reporte de actividades formativas', 'Info de actividades');

        $initSheetCallback($report, 'Info de actividades', $headersInfoActivity);
        $dataInfoActivity = $this->announcementReportDataService->activity_infoSheetData(
            $announcementContainer->announcementGroups
        );
        $report->fromArray($dataInfoActivity, '--', 'A2')->alignAllLeft();

        $initSheetCallback($report, 'Actividad formativa', $headersActivityDetail);
        $dataTutorConnections = $this->announcementReportDataService->activity_detailedInfoSheetData(
            $announcementContainer->announcementGroups
        );
        $report->fromArray($dataTutorConnections, '--', 'A2')->alignAllLeft();

        $report->saveReport($announcementDir);
    }
}
