<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Admin;

use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementUser;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class AnnouncementParticipantService
{
    private $em;
    private $logger;

    public function __construct(EntityManagerInterface $em, LoggerInterface $logger)
    {
        $this->em = $em;
        $this->logger = $logger;
    }

    public function processParticipants($sheetParticipantes, array $formaciones)
    {
        $lastRowParticipantes = $sheetParticipantes->getHighestDataRow();

        for ($row = 2; $row <= $lastRowParticipantes; ++$row) {
            $codificacionCurso = $sheetParticipantes->getCell("A$row")->getValue();
            if (!$codificacionCurso || !isset($formaciones[$codificacionCurso])) {
                continue;
            }

            $email = $sheetParticipantes->getCell("F$row")->getValue();

            if ($email) {
                $user = $this->em->getRepository(User::class)->findOneBy(['email' => $email]);
                if ($user) {
                    $this->assignUserToAnnouncementGroup($user, $formaciones[$codificacionCurso]['group']);
                }
            }
        }

        $this->removeEmptyGroups($formaciones);
    }

    public function removeEmptyGroups(array $formaciones)
    {
        foreach ($formaciones as $formacion) {
            $group = $formacion['group'];

            $participants = $this->em->getRepository(AnnouncementUser::class)->findBy(['announcementGroup' => $group]);

            if (empty($participants)) {
                $announcementTutor = $group->getAnnouncementTutor();

                if (null !== $announcementTutor) {
                    $this->em->remove($announcementTutor);
                }

                $sessions = $this->em->getRepository(AnnouncementGroupSession::class)->findBy(['announcementGroup' => $group]);
                foreach ($sessions as $session) {
                    $this->em->remove($session);
                }

                $this->em->remove($group);
            }
        }

        $this->em->flush();
    }

    public function assignUserToAnnouncementGroup($user, $group)
    {
        $announcement = $group->getAnnouncement();
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
            'announcement' => $announcement,
            'user' => $user
        ]);

        if (!$announcementUser) {
            $announcementUser = (new AnnouncementUser())
                ->setAnnouncement($announcement)
                ->setAnnouncementGroup($group)
                ->setUser($user);
            $this->em->persist($announcementUser);
            $this->em->flush();
        }
    }
}
