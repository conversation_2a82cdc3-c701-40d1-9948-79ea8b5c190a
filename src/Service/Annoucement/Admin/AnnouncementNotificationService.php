<?php

namespace App\Service\Annoucement\Admin;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementNotification;
use App\Entity\AnnouncementNotificationGroup;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\Notification;
use App\Entity\TaskCourse;
use App\Enum\NotificationState;
use App\Repository\AnnouncementGroupRepository;
use App\Service\Notification\EmailNotificationService;
use App\Service\SettingsService;
use DateTime;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class AnnouncementNotificationService
{


    private EntityManagerInterface $em;
    private $emailNotificationService;
    private Security $security;
    private TranslatorInterface $translator;
    private AnnouncementGroupRepository $announcementGroupRepository;
    private SettingsService $settings;


    ///private AnnouncementUserService $announcementUserService;


    public function __construct(
        EntityManagerInterface      $em,
        EmailNotificationService    $emailNotificationService,
        Security                    $security,
        TranslatorInterface         $translator,
        AnnouncementGroupRepository $announcementGroupRepository,
        SettingsService       $settings
    ) {
        $this->em = $em;
        $this->emailNotificationService = $emailNotificationService;
        $this->security = $security;
        $this->translator = $translator;
        $this->announcementGroupRepository = $announcementGroupRepository;
        $this->settings = $settings;
    }


    public function getAnnouncementNotification(Announcement $announcement): array
    {
        $announcementNotifications = $this->em->getRepository(AnnouncementNotification::class)
            ->findBy(['isActive' => true, 'announcement' => $announcement], ['id' => 'DESC']);

        $notifications = [];

        foreach ($announcementNotifications as $announcementNotification) {
            $groups = [];
            $announcementNotificationGroup = $this->em->getRepository(AnnouncementNotificationGroup::class)->findBy(['announcementNotification' => $announcementNotification]);

            foreach ($announcementNotificationGroup as $group) {
                $groupName = $this->generateGroupName($group->getNumGroup());
                $groups[] = [
                    'id' => $group->getAnnouncementGroup()->getId(),
                    'name' => $groupName,
                    'numGroup' => $group->getNumGroup()
                ];
            }

            $notifications[] = [
                'id' => $announcementNotification->getId(),
                'announcement' => $announcementNotification->getAnnouncement()->getId(),
                'text' => $announcementNotification->getText(),
                'state' => $announcementNotification->getStateDescriptive(),
                'starAt' => $announcementNotification->getSendAt(),
                'hour' => $announcementNotification->getSendAt()->format('H:i:s'),
                'groups' => $groups,
                'createdBy' => $announcementNotification->getCreatedBy()->getId(),
            ];
        }

        return   $this->getAnnouncementNotificationByTutor($announcement, $notifications);
    }

    public function generateGroupName(int $numGroup): string
    {
        return $this->translator->trans('announcements.common.group', [], 'messages') . ' ' . $numGroup;
    }

    private function getAnnouncementNotificationByTutor(Announcement $announcement, $announcementNotifications)
    {
        $user = $this->security->getUser();
        $isTutor = $this->shouldFilterByTutor($this->security->getUser());
        $groups = $this->getGroupByAnnouncement($announcement);

        $groupsTutor = [];
        foreach ($groups as $group) {
            $groupsTutor[] = $group['id'];
        }

        $notifications = [];

        foreach ($announcementNotifications as $announcementNotification) {
            $announcementNotificationClass = $this->em->getRepository(AnnouncementNotification::class)->findOneBy(['id' => $announcementNotification['id'], 'announcement' => $announcement]);
            $announcementNotificationGroup = $this->em->getRepository(AnnouncementNotificationGroup::class)->findBy(['announcementNotification' => $announcementNotificationClass], ['numGroup' => 'ASC']);

            $groupsName = [];
            $idsGroups = [];
            if ($announcementNotificationGroup) {
                foreach ($announcementNotificationGroup as $announcementNotificationGroup) {
                    $nameGroup = $this->generateGroupName($announcementNotificationGroup->getNumGroup());

                    $groupsName[] = $nameGroup;
                    $idsGroups[] = $announcementNotificationGroup->getAnnouncementGroup()->getId();
                }
            } else {
                $groupsName[] = "No Group";
            }

            $groupsNameImplode = implode(", ", $groupsName);

            $taskMatchesTutorGroup = false;
            if (count(array_intersect($idsGroups, $groupsTutor)) > 0  || $announcementNotification['createdBy'] === $user->getId()) {
                $taskMatchesTutorGroup = true;
            }

            if ($isTutor && !$taskMatchesTutorGroup) {
                continue; // Skip this task if the tutor has no matching groups and the task has groups
            }

            $notifications[] = [
                'id' => $announcementNotification['id'],
                'announcement' => $announcementNotification['announcement'],
                'text' => $announcementNotification['text'],
                'state' => $announcementNotification['state'],
                'starAt' => $announcementNotification['starAt'],
                'hour' => $announcementNotification['hour'],
                'groups' => $announcementNotification['groups'],
                'groupsName' => $groupsNameImplode,
                'createdBy' => $announcementNotification['createdBy'],
            ];
        }

        return $notifications;
    }

    public function getGroupByAnnouncement(Announcement $announcement)
    {
        $user = $this->security->getUser();
        $groups = [];
        $numGroup = 0;

        $announcementGroup = $this->shouldFilterByTutor($user) ?
            $this->announcementGroupRepository->getGroupsByTutor($announcement, $user) :
            $this->em->getRepository(AnnouncementGroup::class)->findBy(['announcement' => $announcement]);

        foreach ($announcementGroup as $group) {
            $numGroup++;
            $groupName = $this->generateGroupName($numGroup);
            $groups[] = [
                'id' => $group->getId(),
                'name' => $groupName,
                'numGroup' => $numGroup,
            ];
        }

        return $groups;
    }

    public function saveOrUpdateNotification(Request $request): void
    {
        $content = json_decode($request->getContent(), true);
        $idAnnouncement = $content['idAnnouncement'] ?? 0;
        $idNotification = $content['idNotification'] ?? 0;

        $announcementNotification = $this->getOrCreateAnnouncementNotification($idAnnouncement, $idNotification);

        $this->updateAnnouncementNotificationData($request, $announcementNotification);
    }

    private function getOrCreateAnnouncementNotification($idAnnouncement, $idNotification): AnnouncementNotification
    {
        if (!empty($idNotification)) {
            $announcementNotification = $this->em->getRepository(AnnouncementNotification::class)->findOneBy(['id' => $idNotification, 'announcement' => $idAnnouncement]);
            if (!$announcementNotification) {
                $announcementNotification = new AnnouncementNotification();
            }
        } else {
            $announcementNotification = new AnnouncementNotification();
        }

        return $announcementNotification;
    }

    public function deleteAnnouncementNotification($id): bool
    {
        $notification = $this->em->getRepository(AnnouncementNotification::class)->find($id);
        if (!$notification) return FALSE;
        $this->em->remove($notification);
        $this->em->flush();
        return TRUE;
    }

    private function updateAnnouncementNotificationData(Request $request, AnnouncementNotification $announcementNotification)
    {
        $content = json_decode($request->getContent(), true);
        $idAnnouncement = $content['idAnnouncement'] ?? 0;
        $text = $content['text'] ?? 0;
        $sentAt = $content['sentAt'] ?? 0;
        $groups = $content['groups'] ?? [];

        $announcement = $this->em->getRepository(Announcement::class)->find($idAnnouncement);
        $timezone = $announcement->getTimezone() ?? $this->settings->get('app.default_timezone');
        $tz = new \DateTimeZone($timezone);

        $announcementNotification->setAnnouncement($announcement);
        $announcementNotification->setText($text);
        $sentAtDateTime = new \DateTimeImmutable($sentAt, $tz);

        $announcementNotification->setSendAt($sentAtDateTime);

        // Calcula y establece el estado directamente en updateAnnouncementNotificationData
        $currentState = $groups ? $this->calculateNotificationState($sentAtDateTime) : NotificationState::PENDING;
        $announcementNotification->setState($currentState);

        $this->setAnnouncementNotificationGroup($groups, $announcementNotification);
        $this->deleteAnnouncementNotificationGroup($groups, $announcementNotification);
        $this->em->persist($announcementNotification);
        $this->em->flush();

        if ($currentState === NotificationState::SENT) {
            $this->createNotificationForUser($announcementNotification, $groups);
        }
    }

    private function setAnnouncementNotificationGroup($groups, AnnouncementNotification $announcementNotification)
    {
        if (!empty($groups)) {
            foreach ($groups as $group) {
                $announcementNotificationGroup = $this->em->getRepository(AnnouncementNotificationGroup::class)->findOneBy(['announcementNotification' => $announcementNotification, 'announcementGroup' => $group['id']]);
                $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($group['id']);

                if (!$announcementNotificationGroup) {
                    $announcementNotificationGroup = new AnnouncementNotificationGroup();
                }

                $announcementNotificationGroup->setAnnouncementNotification($announcementNotification)
                    ->setAnnouncementGroup($announcementGroup)
                    ->setNumGroup($group['numGroup'] ?? 1);

                $this->em->persist($announcementNotificationGroup);
            }
        }
    }

    private function deleteAnnouncementNotificationGroup($groups, AnnouncementNotification $announcementNotification)
    {
        $announcementsNotificationGroup = $this->em->getRepository(AnnouncementNotificationGroup::class)->findBy(['announcementNotification' => $announcementNotification]);

        foreach ($announcementsNotificationGroup as $announcementNotificationGroup) {
            $found = false;
            foreach ($groups as $group) {
                if ($announcementNotificationGroup->getAnnouncementGroup()->getId() === $group['id']) {
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $this->em->remove($announcementNotificationGroup);
            }
        }
    }

    private function calculateNotificationState(\DateTimeImmutable $sentAt, \DateTimeZone $tz = null)
    {
        $currentTime = new \DateTimeImmutable('now', $tz);

        return ($currentTime > $sentAt) ?
            NotificationState::SENT :
            NotificationState::PENDING;
    }

    private function createNotificationForUser(AnnouncementNotification $announcementNotification, $groups)
    {
        $announcement = $announcementNotification->getAnnouncement();

        if (!empty($groups)) {
            foreach ($groups as $group) {
                $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($group['id']);
                $announcementUsers = $this->em->getRepository(AnnouncementUser::class)->findBy(['announcement' => $announcement, 'announcementGroup' => $announcementGroup]);

                foreach ($announcementUsers as $announcementUser) {
                    $user = $announcementUser->getUser();
                    $this->emailNotificationService->insertNotification(
                        $announcementNotification->getAnnouncement()->getCourse()->getName(),
                        $announcementNotification->getText(),
                        'announcement',
                        $user->getId(),
                        null,
                        [
                            'announcement' => [
                                'idAnnouncement' => $announcement->getId(),
                                'idAnnouncementUser' => $announcementUser->getId()
                            ]
                        ]
                    );
                }
            }
        }
    }

    public function createNoticationAboutAnnoucement(Announcement $announcement)
    {
        $parameters = [];

        $startAt = new DateTime($announcement->getStartAt()->format('Y-m-d H:i:s'));
        $finishAt = new DateTime($announcement->getFinishAt()->format('Y-m-d H:i:s'));
        //sumar una hora
        $startAt->modify('+1 hour');
        $finishAt->modify('+1 hour');
        $attributes[] = [
            '%course%' => $announcement->getCourse()->getName(),
            '%startAt%' => $startAt->format('Y-m-d H:i:s'),
            '%endAt%' => $finishAt->format('Y-m-d H:i:s'),
        ];

        $announcementUsers = $this->getUsersAnnouncement($announcement);
        $parameters['type'] = 'announcement';
        $parameters['translationTitle'] = 'notification.announcement.notification_user_title';
        $parameters['translationText'] = 'notification.announcement.notification_user_message';
        $parameters['attributes'] = $attributes;


        foreach ($announcementUsers as $announcementUser) {
            $parameters['userId'] = $announcementUser->getUser()->getId();
            $this->emailNotificationService->insertNotificationWithTranslation($parameters);
        }
    }

    public function createNotificationChangeStateTask(TaskCourse $taskCourse, array $settings)
    {
        $userTutor = $this->security->getUser();
        $attributes[] = [
            '%course%' => $taskCourse->getCourse()->getName(),
            '%task%' => $taskCourse->getTitle(),
            '%user%' => $userTutor->getFullName(),
        ];

        $type = $settings['state'] == 4 ? 'success' : 'error';
        $translationTitle = $settings['state'] == 4 ? 'notification.announcement.task_approved_title' : 'notification.announcement.task_reject_title';
        $translationText = $settings['state'] == 4 ? 'notification.announcement.task_approved_message' : 'notification.announcement.task_reject_message';

        $parameters = [];
        $parameters['type'] = $type;
        $parameters['translationTitle'] = $translationTitle;
        $parameters['translationText'] = $translationText;
        $parameters['attributes'] = $attributes;
        $parameters['userId'] = $settings['userId'];

        $this->emailNotificationService->insertNotificationWithTranslation($parameters);
    }

    public function createNoticationCancelationEventAnnoucement(Announcement $announcement)
    {
        $parameters = [];
        $startAt = new DateTime($announcement->getStartAt()->format('Y-m-d H:i:s'));
        $finishAt = new DateTime($announcement->getFinishAt()->format('Y-m-d H:i:s'));
        //sumar una hora
        $startAt->modify('+1 hour');
        $finishAt->modify('+1 hour');

        $attributes[] = [
            '%course%' => $announcement->getCourse()->getName(),
            '%startAt%' => $startAt->format('Y-m-d H:i:s'),
            '%endAt%' => $finishAt->format('Y-m-d H:i:s'),
        ];

        $announcementUsers = $this->getUsersAnnouncement($announcement);
        $parameters['type'] = 'announcement';
        $parameters['translationTitle'] = 'notification.announcement.cancelation_announcement';
        $parameters['translationText'] = 'notification.announcement.cancelation_announcement_message';
        $parameters['attributes'] = $attributes;


        foreach ($announcementUsers as $announcementUser) {
            $parameters['userId'] = $announcementUser->getUser()->getId();
            $this->emailNotificationService->insertNotificationWithTranslation($parameters);
        }
    }


    private function getUsersAnnouncement(Announcement $announcement)
    {
        $user = $this->security->getUser();
        $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)
            ->findOneBy(['announcement' => $announcement, 'tutor' => $user]);

        if ($announcementTutor && $announcementTutor->getAnnouncementGroup()) {
            $announcementUsers = $this->em->getRepository(AnnouncementUser::class)
                ->findBy(['announcement' => $announcement, 'announcementGroup' => $announcementTutor->getAnnouncementGroup()]);
        } else {
            $announcementUsers = $this->em->getRepository(AnnouncementUser::class)
                ->findBy(['announcement' => $announcement]);
        }

        return $announcementUsers;
    }

    private function shouldFilterByTutor($user)
    {
        return $user->isTutor() && !($user->isAdmin() || $user->isManager());
    }
}
