<?php

namespace App\Service\Annoucement\Admin;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementUser;
use App\Entity\AnnouncementUserDigitalSignature;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;

class AnnouncementGroupSessionService
{
    private EntityManagerInterface $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }


    /**
     * @param Announcement $announcement
     * @param User|AnnouncementUser|AnnouncementGroup $param
     * @return AnnouncementGroupSession|null
     * @throws \Exception
     */
    public function getCurrentSession(Announcement $announcement, $param): ?AnnouncementGroupSession
    {
        $tempParam = $param;
        if ($tempParam instanceof User) {
            $tempParam = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['user' => $tempParam, 'announcement' => $announcement]);
        }
        if ($tempParam instanceof AnnouncementUser) {
            $tempParam = $tempParam->getAnnouncementGroup();
        }
        if (!($tempParam instanceof AnnouncementGroup)) throw new \RuntimeException("Param must be: User|AnnouncementUser|AnnouncementGroup");

        $sessions = $tempParam->getAnnouncementGroupSessions();

        foreach ($sessions as $session) {
            $currentTime = new \DateTimeImmutable('now', $session->getStartAt()->getTimezone());
            if ($currentTime < $session->getStartAt()) continue;
            if ($currentTime > $session->getFinishAt()) continue;
            return $session;
        }

        return null;
    }

    /**
     * @param Announcement $announcement
     * @param AnnouncementGroupSession $session
     * @param User|AnnouncementUser $user
     * @return AnnouncementUserDigitalSignature|null
     */
    public function getSessionSignature(Announcement $announcement, AnnouncementGroupSession $session, $user): ?AnnouncementUserDigitalSignature
    {
        if ($user instanceof User) {
            $user = $this->em->getRepository(AnnouncementUser::class)->findOneBy(['user' => $user]);
        }
        if (!($user instanceof AnnouncementUser)) throw new \RuntimeException("User must be of type AnnouncementUser");
        return $this->em->getRepository(AnnouncementUserDigitalSignature::class)->findOneBy([
            'announcementGroupSession' => $session,
            'announcementUser' => $user
        ], ['createdAt' => 'DESC']);
    }
}
