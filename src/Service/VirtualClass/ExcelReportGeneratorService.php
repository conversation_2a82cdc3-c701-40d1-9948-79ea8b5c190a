<?php

namespace App\Service\VirtualClass;

use App\Utils\FileUtils;

use App\Entity\ClassroomVirtual;
use App\Entity\ClassroomVirtualResult;

use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Psr\Log\LoggerInterface;

use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;

class ExcelReportGeneratorService 
{
    private $em;
    private $logger;
    private $translator;
    private $fileUtils;

    public function __construct(
        EntityManagerInterface $em,
        LoggerInterface        $logger,
        TranslatorInterface    $translator,
        FileUtils              $fileUtils
    ) {

        $this->em = $em;
        $this->logger = $logger;
        $this->translator = $translator;
        $this->fileUtils = $fileUtils;
    }

    public function generateExcelReportAnnouncement(array $reportData, array $complementaryData, string $reportName)
    {
        $spreadsheet = new Spreadsheet();
        for($i=0; $i<count($reportData); $i++){ 
            if($i==0) {
                $sheet = $spreadsheet->getActiveSheet();
            }else{
                $sheet =$spreadsheet->createSheet($i);
            }
            $number=$i+1;
            $sheet->setTitle('Asistencia '.$number);

            $this->SetColumnExcelWith($sheet);
            $this->setBodyExcelStyle($sheet);
            $this->setHeaderExcel($sheet, $reportData[$i]->header, $complementaryData[$i]);
            $this->setBodyExcel($sheet, $reportData[$i]->body, $reportData[$i]->header->duration);
        }

        $writer = new Xlsx($spreadsheet);
        $writer->save($reportName);
        
        return $reportName;
    }

    public function generateExcelReportVirtualClass($reportData, $complementaryData, $reportName)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Asistencia 1');

        $this->SetColumnExcelWith($sheet);
        $this->setBodyExcelStyle($sheet);
        $this->setHeaderExcel($sheet, $reportData->header, $complementaryData);
        $this->setBodyExcel($sheet, $reportData->body, $reportData->header->duration);

        $writer = new Xlsx($spreadsheet);
        $writer->save($reportName);

        return $reportName;
    }

    private function setHeaderExcel($sheet, $headerReport, $complementaryData): void
    {
        $convocatoria = 'Convocatoria: '.$complementaryData->courseName;
        $tutor = 'Tutor: '.$complementaryData->tutor;
        $inicio = explode(" ", $headerReport->start_time);
        $finalizacion = explode(" ", $headerReport->end_time);
        $this->SetHeaderExcelStyle($sheet);

        $sheet->setCellValue("B2", $convocatoria);
        $sheet->setCellValue("B4", 'Fecha reunión');
        $sheet->setCellValue("D4", $inicio[0]);
        $sheet->setCellValue("C5", 'Hora de inicio');
        $sheet->setCellValue("D5", $inicio[1]);
        $sheet->setCellValue("C6", 'Hora de finalización');
        $sheet->setCellValue("D6", $finalizacion[1]);
        $sheet->setCellValue("B7", 'Duración (minutos)');
        $sheet->setCellValue("D7", $headerReport->duration);
        $sheet->setCellValue("B8", 'Participantes');
        $sheet->setCellValue("D8", $headerReport->participants);       
        $sheet->setCellValue("B10", $tutor);
    }    
    
    private function setBodyExcel($sheet, $bodyReport, $duration): void
    {
        $fila = 13;
        for($i=0; $i<count($bodyReport); $i++){ 
            $PercentMinutesAttended = ($bodyReport[$i]->duration) / $duration;
            $assistance = $PercentMinutesAttended >= Classroomvirtual::MINIMUM_ATTENDANCE_TIME ? true : false;            

            $sheet->setCellValue("A".$fila, $bodyReport[$i]->name);
            $sheet->setCellValue("C".$fila, $bodyReport[$i]->join_time);
            $sheet->setCellValue("D".$fila, $bodyReport[$i]->leave_time);
            $sheet->setCellValue("E".$fila, $bodyReport[$i]->duration);
            $assistance? $sheet->setCellValue("F".$fila, 'ü'): $sheet->setCellValue("F".$fila, 'û');     


            $sheet->mergeCells('A'.$fila.':B'.$fila);
            $this->SetBordercellExcel($sheet, 'A'.$fila.':B'.$fila);
            $this->SetBordercellExcel($sheet, 'C'.$fila);
            $this->SetBordercellExcel($sheet, 'D'.$fila);
            $this->SetBordercellExcel($sheet, 'E'.$fila);
            $this->SetBordercellExcel($sheet, 'F'.$fila);
            $sheet->getStyle('F'.$fila)->getFont()->setName('Wingdings');

            $this->SetCenterCellExcel($sheet, 'C'.$fila.':F'.$fila);

            $fila++;
        }
    }

    private function setBodyExcelStyle($sheet): void
    {
        $sheet->setCellValue("A12", 'Nombre y apellidos');
        $sheet->setCellValue("C12", 'Se unió a la reunión');
        $sheet->setCellValue("D12", 'Salió de la reunión');
        $sheet->setCellValue("E12", 'Duración');
        $sheet->setCellValue("F12", 'Asistencia');
    }

    private function SetHeaderExcelStyle($sheet): void
    {
        $sheet->getStyle('D5:D8')->getAlignment()->setHorizontal('left');       
        $sheet->getStyle('B2:C10')->getFont()->setBold(true);
        $sheet->getStyle('A12:F12')->getFont()->setBold(true);
        $sheet->getStyle('A12:F12')->getAlignment()->setHorizontal('center');
        $sheet->mergeCells('A12:B12');
        //color de fondo
        $sheet->getStyle('A12:F12')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setARGB('7DFE68');
        //Bordes
        $this->SetBordercellExcel($sheet, 'A12:B12');
        $this->SetBordercellExcel($sheet, 'C12');
        $this->SetBordercellExcel($sheet, 'D12');
        $this->SetBordercellExcel($sheet, 'E12');
        $this->SetBordercellExcel($sheet, 'F12');
    }

    private function SetBordercellExcel($sheet, $rango): void
    {        
        $styleArray = array(
                'borders' => array(
                'outline' => array(
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => array('argb' => '00000000'),
                ),
            ),
        );       
        $sheet ->getStyle($rango)->applyFromArray($styleArray);
    }

    private function SetColumnExcelWith($sheet): void
    {
        $sheet->getColumnDimension('A')->setWidth(35);
        $sheet->getColumnDimension('B')->setWidth(2);
        $sheet->getColumnDimension('C')->setWidth(20);
        $sheet->getColumnDimension('D')->setWidth(20);
        $sheet->getColumnDimension('E')->setWidth(12);
        $sheet->getColumnDimension('F')->setWidth(12);
    }

    private function SetCenterCellExcel($sheet, $rango): void
    {
        $sheet->getStyle($rango)->getAlignment()->setHorizontal('center'); 
    }


}