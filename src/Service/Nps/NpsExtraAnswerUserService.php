<?php

declare(strict_types=1);

namespace App\Service\Nps;

use App\Entity\Nps;
use App\Entity\NpsQuestion;
use App\Modules\Survey\Repository\SurveyCourseModuleRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Security;

class NpsExtraAnswerUserService
{
    protected EntityManagerInterface $em;
    protected Security $security;
    protected SurveyCourseModuleRepository $surveyCourseModuleRepository;

    public function __construct(
        EntityManagerInterface $em,
        Security $security,
        SurveyCourseModuleRepository $surveyCourseModuleRepository
    ) {
        $this->em = $em;
        $this->security = $security;
        $this->surveyCourseModuleRepository = $surveyCourseModuleRepository;
    }

    private function getUser()
    {
        return $this->security->getUser();
    }

    public function addExtraToItems(array $surveyData): array
    {
        $extraItems = [];

        foreach ($surveyData as $item) {
            $question = $this->em->getRepository(Nps::class)->findOneBy(['id' => $item['id']])->getQuestion();
            $survey = $this->em->getRepository(NpsQuestion::class)->find($question)->getSurvey();

            $questionsSurvey = $this->em->getRepository(NpsQuestion::class)->findBy(['survey' => $survey, 'main' => 0]);
            $locale = $this->getUser() ? $this->getUser()->getLocale() : null;
            $questions = $this->surveyCourseModuleRepository->getStructureQuestion($questionsSurvey, $locale);

            $newQuestions = [];
            foreach ($questions as $question) {
                $nps = $this->em->getRepository(Nps::class)->findOneBy([
                    'question' => $question['id'],
                    'user' => $item['user_id'],
                    'course' => $item['user_course_id'] ?? null,
                ]);

                switch ($question['type']) {
                    case NpsQuestion::TYPE_CHECKBOX:
                        $question['answers'] = $nps ? $this->getAnswersTypeCheckbox($question, $nps) : [];
                        break;

                    case NpsQuestion::TYPE_RADIO:
                        $question['answers'] = $nps ? $this->getAnswersTypeRadio($question, $nps) : [];
                        break;

                    case NpsQuestion::TYPE_SWITCH:
                        $question['answers'] = $nps ? $this->getAnswersTypeSwitch($nps) : false;
                        break;

                    case NpsQuestion::TYPE_TEXT:
                        $question['answers'] = $nps ? $nps->getValue() : '';
                        break;

                    case NpsQuestion::TYPE_NPS:
                        $question['answers'] = $nps ? $nps->getValue() : '';
                        break;
                }

                $newQuestions[] = $question;
            }

            $item['extra'] = $newQuestions;

            array_push($extraItems, $item);
        }

        return $extraItems;
    }

    private function getAnswersTypeCheckbox($question, Nps $nps)
    {
        $answers = [];

        $npsValue = $nps ? explode(',', $nps->getValue()) : [];

        foreach ($question['answers'] as $answer) {
            $answers[] = [
                'id' => $answer['id'],
                'value' => $answer['value'],
                'checked' => \in_array($answer['id'], $npsValue)
            ];
        }

        return $answers;
    }

    private function getAnswersTypeRadio($question, Nps $nps)
    {
        foreach ($question['answers'] as $answer) {
            $answers[] = [
                'id' => $answer['id'],
                'value' => $answer['value'],
                'extra' => $nps->getValue(),
                'checked' => $answer['id'] == $nps->getValue()
            ];
        }

        return $answers;
    }

    private function getAnswersTypeSwitch(Nps $nps)
    {
        $answers = $nps ? $nps->getValue() : false;

        return $answers;
    }
}
