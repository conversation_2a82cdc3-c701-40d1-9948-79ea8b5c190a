<?php

declare(strict_types=1);

namespace App\Service\User\Authentication;

use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\User;
use App\Entity\UserExtra;
use App\Service\Email\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class StarTeam
{
    private const int FILTER_CATEGORY_DIVISION = 1;
    private const int FILTER_CATEGORY_COUNTRY = 2;
    private const int FILTER_CATEGORY_CENTER = 3;
    private const int FILTER_CATEGORY_DEPARTMENT = 4;
    private const int FILTER_CATEGORY_POSITION = 5;
    private const int FILTER_CATEGORY_TYPE = 6;

    private HttpClientInterface $client;
    private string $tokenUrl;
    private string $tokenKey;
    private string $userUrl;
    private string $userKey;
    private string $apiUsername;
    private string $apiPass;
    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private EmailService $mailer;

    private array $filterCategories = [];
    private ManagerRegistry $managerRegistry;

    /**
     * StarTeam constructor.
     */
    public function __construct(
        HttpClientInterface $client,
        string $tokenUrl,
        string $tokenKey,
        string $userUrl,
        string $userKey,
        string $apiUsername,
        string $apiPass,
        EntityManagerInterface $em,
        LoggerInterface $starteamLogger,
        EmailService $mailer,
        ManagerRegistry $managerRegistry
    ) {
        $this->client = $client;
        $this->tokenUrl = $tokenUrl;
        $this->tokenKey = $tokenKey;
        $this->userUrl = $userUrl;
        $this->userKey = $userKey;
        $this->apiUsername = $apiUsername;
        $this->apiPass = $apiPass;
        $this->em = $em;
        $this->logger = $starteamLogger;
        $this->mailer = $mailer;
        $this->managerRegistry = $managerRegistry;

        $this->loadFilterCategories();
    }

    private function loadFilterCategories(): void
    {
        $filterCategories = $this->em->getRepository(FilterCategory::class)->findAll();
        foreach ($filterCategories as $filterCategory) {
            $this->filterCategories[$filterCategory->getId()] = $filterCategory;
        }
    }

    /**
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     */
    public function checkToken(string $token)
    {
        $url = $this->tokenUrl . $token;

        $response = $this->starTeamCall($url, $this->tokenKey, 'GET');

        if (200 == $response->getStatusCode()) {
            $data = json_decode($response->getContent());
            $this->logger->info('StarTeam Token Check: ' . $token);
            $this->logger->info('Employee ID: ' . $data->employeeDataRS->employeeData->employeeId);

            return $this->getUser($data->employeeDataRS->employeeData->employeeId);
        } else {
            return false;
        }
    }

    /**
     * @throws TransportExceptionInterface
     */
    private function starTeamCall(string $url, string $key, string $method, array $parameters = []): ResponseInterface
    {
        $paramsString = json_encode($parameters);

        return $this->client->request(
            $method,
            $url,
            [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Content-Length' => \strlen($paramsString),
                    'Ocp-Apim-Subscription-Key' => $key,
                    'Username' => $this->apiUsername,
                    'Password' => $this->apiPass,
                ],
                'body' => $paramsString,
            ]
        );
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws \Exception
     */
    public function getUser($userCode)
    {
        $userUrl = $this->userUrl . $userCode . '/gestionet';
        $userResponse = $this->starTeamCall($userUrl, $this->userKey, 'GET');

        if (200 == $userResponse->getStatusCode()) {
            $this->logger->info('User Response: ' . $userResponse->getContent());

            $userData = json_decode($userResponse->getContent());

            return $this->saveEmployee($userData->employee);
        }

        return false;
    }

    /**
     * @throws \Exception
     */
    public function saveEmployee($employee)
    {
        $userRepository = $this->em->getRepository(User::class);
        $filterRepository = $this->em->getRepository(Filter::class);

        $id = $employee->id;
        $uniqueId = $employee->uniqueId;

        $active = $employee->active;
        $firstName = $employee->firstName;
        $lastName = $employee->lastName;
        $gender = ('Female' == $employee->gender) ? 'F' : 'M';
        $birthdate = $employee->birthDate;
        $inactivityPeriod = $employee->inactivityPeriod;
        $email = $employee->personalEmail ?: 'user' . $id . '@user.com';

        $division = $employee->region;
        $structure = $employee->structure;
        $country = $employee->centerCountryCode;
        $categoryId = $employee->standardJobTitleId;
        $categoryName = $employee->standardJobTitle;
        $centerId = $employee->centerId;
        $centerName = $employee->centerName;
        $departmentId = $employee->departmentId;
        $departmentName = $employee->departmentName;
        $employeer = $employee->employeer;

        /**
         * @var User $user
         */
        $user = $userRepository->findOneBy(['code' => $id]);

        $registerKey = str_replace(['.', '-', ' '], '', $employee->identityDocument);

        if (!$user && '' != $registerKey) {
            $user = $userRepository->findOneBy([
                'registerKey' => $registerKey,
            ]);
        }

        if (!$user && $email) {
            $user = $userRepository->findOneBy([
                'email' => $email,
            ]);
        }

        if (!$user) {
            // Chequeamos que no lo hayan borrado
            $user = $userRepository->findWithDeletedByEmail($email);
            if ($user) {
                return false;
            }
            if (!$active) {
                return false;
            }

            $user = (new User())
                ->setIsActive(false)
                ->setValidated(1)
                ->setEmail($email)
                ->setPassword('12345678')
                ->setRoles(['ROLE_USER'])
                ->setOpen(true)
                ->setLocale($this->getDefaultLocale($country));
        }

        $extraFields = [
            'employeer' => $employeer,
        ];
        $user
            ->setIsActive(true)
            ->setValidated(true)
            ->setOpen(true)
            ->setCode((string) $id)
            ->setRegisterKey($registerKey)
            ->setFirstName($firstName)
            ->setLastName($lastName)
            ->setEmail($email)
            ->setMetaByLabel('HRP', $uniqueId)
            ->setMetaByLabel('extraFields', $extraFields);

        $existsUserWithEmail = $userRepository->findOneBy(['email' => $email]);
        if ($existsUserWithEmail && $existsUserWithEmail->getId() != $user->getId()) {
            $existsUserWithEmail->setEmail($existsUserWithEmail->getEmail() . '-duplicated-' . date('Y-m-d H:i:s'));
            $this->em->flush($existsUserWithEmail);
            $user->setEmail($email);
        }

        if (!$active) {
            $user
                ->setIsActive(false)
                ->setValidated(false);
        }

        // Start With Filters
        $filters = [];

        $divisionFilter = $this->getFilter($division, self::FILTER_CATEGORY_DIVISION);
        $countryFilter = $this->getFilter($country, self::FILTER_CATEGORY_COUNTRY);
        $centerFilter = $this->getFilter($centerId, self::FILTER_CATEGORY_CENTER, $centerName, $countryFilter);
        $departmentFilter = $this->getFilter($departmentId, self::FILTER_CATEGORY_DEPARTMENT, $departmentName);
        $positionFilter = $this->getFilter($categoryId, self::FILTER_CATEGORY_POSITION, $categoryName);
        $typeFilter = $this->getFilter(
            $structure ? 1 : 0,
            self::FILTER_CATEGORY_TYPE,
            $structure ? 'Estructura' : 'Hotel'
        );

        $filters = array_merge($filters, [
            $divisionFilter,
            $countryFilter,
            $centerFilter,
            $departmentFilter,
            $positionFilter,
            $typeFilter,
        ]);

        // If the filter is manually excluded on the admin,
        // it is not included in the filter array
        foreach ($filters as $filterKey => $filter) {
            if ($user->isCustomExcludedFilter($filter->getId())) {
                unset($filters[$filterKey]);
            }

            if ($user->isCustomAssignedFilter($filter->getId())) {
                $user->removeCustomFilter($filter->getId());
            }
        }

        $customFilters = $user->getCustomFilters();
        if (!empty($customFilters['assigned'])) {
            $customAssignedFilters = $customFilters['assigned'];
            foreach ($customAssignedFilters as $filterId) {
                $filter = $filterRepository->find($filterId);
                if ($filter) {
                    $filters[] = $filter;
                }
            }
        }

        $user->setFilters($filters);

        // Inactivity Period Check
        if ($inactivityPeriod) {
            if (null == $user->getMetaValueByLabel('inactivityPeriod')) {
                $user->setMetaByLabel('inactivityPeriod', date('Y-m-d H:i:s'));
                $user->setMetaByLabel('inactivityPeriodAccepted', null);
            }
        } else {
            if (null != $user->getMetaValueByLabel('inactivityPeriod')) {
                $inactivityPeriods = $user->getMetaValueByLabel('inactivityPeriods');
                if (null == $inactivityPeriods) {
                    $inactivityPeriods = [];
                }
                $inactivityPeriods[] = [
                    'inactivityPeriod' => $user->getMetaValueByLabel('inactivityPeriod'),
                    'inactivityPeriodAccepted' => $user->getMetaValueByLabel('inactivityPeriodAccepted'),
                ];
                $user->setMetaByLabel('inactivityPeriods', $inactivityPeriods);
                $user->removeMetaValueByLabel('inactivityPeriod');
                $user->removeMetaValueByLabel('inactivityPeriodAccepted');
            }
        }

        if (!$user->getExtra()) {
            $user->setExtra(new UserExtra());
        }

        $user->getExtra()
            ->setGender($gender)
            ->setBirthdate(new \DateTime($birthdate));

        //        echo 'ID: ' . $user->getId() . ' - Email: ' . $user->getEmail(). PHP_EOL;

        try {
            $this->em->persist($user);
            $this->em->flush();
        } catch (\Exception $e) {
            $this->managerRegistry->resetManager();
            $this->logger->error('Error saving user: ' . $e->getMessage());
            $this->logger->error('User Data: ' . json_encode($employee));

            $superAdminUsers = $userRepository->findByRole(User::ROLE_SUPER_ADMIN);

            foreach ($superAdminUsers as $admin) {
                $this->mailer->sendEmail(
                    $admin->getEmail(),
                    'Starteam - Error saving user',
                    'template_email/sso-error.html.twig',
                    [
                        'message' => $e->getMessage(),
                        'messageExtra' => 'ID: ' . $user->getId()
                            . ' - Code: ' . $user->getCode()
                            . ' - Email: ' . $user->getEmail()
                    ]
                );
            }
        }

        return $user;
    }

    private function getDefaultLocale(string $country): string
    {
        $locales = [
            'es' => ['ES', 'ESP', 'CUB', 'MEX', 'PER', 'DOM'],
            'pt' => ['BRA', 'CAB', 'POR'],
            'en' => ['USA', 'HUN', 'JAM', 'MAR', 'MTN', 'TUN'],
        ];

        foreach ($locales as $locale => $countries) {
            if (\in_array($country, $countries)) {
                return $locale;
            }
        }

        return 'es';
    }

    private function getFilter($code, $categoryId, $name = null, $parent = null)
    {
        $filterRepository = $this->em->getRepository(Filter::class);

        $filter = $filterRepository->findOneBy([
            'code' => $code,
            'filterCategory' => $this->filterCategories[$categoryId],
        ]);

        if (!$filter) {
            if (!$name) {
                $name = $code;
            }

            $filter = (new Filter())
                ->setName($name)
                ->setFilterCategory($this->filterCategories[$categoryId])
                ->setCode($code)
                ->setSource(Filter::SOURCE_REMOTE);

            if ($parent) {
                $filter->setParent($parent);
            }

            $this->em->persist($filter);
        }

        return $filter;
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws \Exception
     */
    public function userMaintenance($from, $to): void
    {
        set_time_limit(6000);
        $content = $this->getUsers($from, $to);

        foreach ($content->employees as $employee) {
            $this->saveEmployee($employee);
        }
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function getUsers(string $from, string $to)
    {
        $usersUrl = $this->userUrl . 'employees';
        $params = [
            'from' => $from,
            'to' => $to,
        ];
        $response = $this->starTeamCall($usersUrl, $this->userKey, 'POST', $params);

        return json_decode($response->getContent());
    }

    public function getUserData($userCode)
    {
        $userUrl = $this->userUrl . $userCode . '/gestionet';
        $userResponse = $this->starTeamCall($userUrl, $this->userKey, 'GET');

        if (200 == $userResponse->getStatusCode()) {
            $this->logger->info('User Response: ' . $userResponse->getContent());

            $userData = json_decode($userResponse->getContent());

            return $userData->employee;
        }

        return false;
    }
}
