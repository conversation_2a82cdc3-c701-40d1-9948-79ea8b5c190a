<?php

declare(strict_types=1);

namespace App\Enum;

final class ChapterType
{
    public const TYPE_GAME = 'game';
    public const TYPE_CONTENT = 'content';
    public const CONTENT_TYPE = 2;
    public const SCORM_ICON = 'scorm.svg';
    public const CONTENT_ICON = 'contents.svg';
    public const ROULETTE_ICON = 'roulette.svg';
    public const ROULETTE_WORD_ICON = 'lettersWheel.svg';
    public const TRUEORFALSE_ICON = 'truefalse.svg';
    public const ADIVINAIMAGEN_ICON = 'adivina.svg';
    public const ORDENARMENORMAYOR_ICON = 'jerarquiza.svg';
    public const MEMORYMATCH_ICON = 'parejas.svg';
    public const CATEGORIZED_ICON = 'categorized.svg';
    public const FILLGAPS_ICON = 'huecos.svg';
    public const GUESSWORD_ICON = 'ordena_letras.svg';
    public const WORDLE_ICON = 'palabra_secreta.svg';
    public const LETTERSOUP_ICON = 'sopa_letras.svg';
    public const VIDEOQUIZ_ICON = 'videoquiz.svg';
    public const X2_ICON = 'x2.svg';
    public const QUIZ_ICON = 'quiz.svg';
    public const PUZZLE_ICON = 'puzzle.svg';
    public const HIDE_WORD_ICON = 'hide-words.svg';
    public const PDF_ICON = 'pdf.svg';
    public const PPT_ICON = 'ppt.svg';
    public const VIDEO_ICON = 'video.svg';
    public const SLIDER_ICON = 'slider.svg';
    public const VCMS_ICON = 'vcms.svg';
    public const ROLEPLAY_ICON = 'roleplay.svg';
    public const LTI_ICON = 'lti.svg';

    // PlayUrl...
    public const SCORM_PLAYER_URL = '/scorm/';
    public const CONTENT_PLAYER_URL = '/contents/';
    public const GAMES_PLAYER_URL = '/games/?id=';
    public const VIDEO_URL = '/video/';
    public const PDF_URL = '/pdf/';
    public const PPT_URL = '/ppt/';
    public const SLIDER_PLAYER_URL = '/slider/';
    public const VCMS_URL_VIEW = '/vcms/visor/%s';
    public const ROLEPLAY_URL_VIEW = '/roleplay/visor/%s';
    public const LTI_PLAYER_URL = '/lti/%userId/%chapterId';

    // porcentaje
    public const PORCENTAJE_FOR_COMPLETE_GAME = 0.75;
    public const QUIZ_PORCENTAJE_FOR_COMPLETE = 0.70;
    public const GUESSWORD_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const MEMORYMATCH_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const TRUE_OR_FALSE_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const ADIVINA_IMAGEN_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const ROULETTE_WORD_TYPE_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const LETTERSOUP_PORCENTAJE_FOR_COMPLETE = 1; // El usuario tiene que completar todas las palabras
    public const FILLGAPS_PORCENTAJE_FOR_COMPLETE = 1;
    public const ORDENARMENORMAYOR_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const CATEGORIZE_PORCENTAJE_FOR_COMPLETE = 0.75;
    public const WORDLE_PORCENTAJE_FOR_COMPLETE = 1;
    public const DEFAULT_PORCENTAJE_FOR_COMPLETE = 0.70;

    // code for chapter type

    // Chapter Type content
    public const CODE_SCORM = 'SCORM';
    public const CODE_CONTENT = 'CONTENT';
    public const CODE_PDF = 'PDF';
    public const CODE_PPT = 'PPT';
    public const CODE_VIDEO = 'VIDEO';
    public const CODE_VCMS = 'VCMS';
    public const CODE_ROLE_PLAY = 'ROLE_PLAY';

    // Chapter Type games
    public const CODE_ROULETTE = 'ROULETTE'; // Ruleta, id 3 actualmente
    public const CODE_DOUBLE_OR_NOTHING = 'DOUBLE_OR_NOTHING'; // Doble o nada, id 4 actualmente
    public const CODE_QUIZ = 'QUIZ';    // Quiz, id 5 actualmente
    public const CODE_PUZZLE = 'PUZZLE'; // Puzzle, id 6 actualmente
    public const CODE_HIDE_WORDS = 'HIDE_WORDS'; // Adivina la palabra, id 7 actualmente
    public const CODE_LETTERS_WHEEL = 'LETTERS_WHEEL'; // Rueda de letras, id 11 actualmente
    public const CODE_TRUE_OR_FALSE = 'TRUE_OR_FALSE'; // Verdadero o falso, id 12 actualmente
    public const CODE_HIDDEN_PICTURE = 'HIDDEN_PICTURE'; // Adivinanza, id 13 actualmente
    public const CODE_HIGHER_LOWER = 'HIGHER_LOWER'; // De mayor a menor, id 14 actualmente
    public const CODE_MEMORY_MATCH = 'MEMORY_MATCH'; // Parejas, id 15 actualmente
    public const CODE_CATEGORIZED = 'CATEGORIZED'; // Donde encaja, -> id 16 actualmente
    public const CODE_FILL_GAPS = 'FILL_GAPS';    // Rellena huecos, id 17 actualmente
    public const CODE_GUESS_WORD = 'GUESS_WORD'; // Ordena letras, id 18 actualmente
    public const CODE_WORDLE = 'WORDLE'; // Enigma, id 19 actualmente
    public const CODE_SEARCH_WORD = 'SEARCH_WORD'; // Sopa de letras id 20 actualmente
    public const CODE_VIDEO_QUIZ = 'VIDEO_QUIZ'; // Videoquiz id 21 actualmente
}
