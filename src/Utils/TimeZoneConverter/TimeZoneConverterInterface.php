<?php

namespace App\Utils\TimeZoneConverter;

/**
 * Use this interface when history data is not set in UTC format
 * and the entity contains a timezone field
 */
interface TimeZoneConverterInterface
{
    public function toUtc(array $args = []);

    /**
     * Convert all DateTime objects from UTC timezone to timezone defined in the object
     * @return void
     * @throws \Exception
     */
    public function fromUtc(array $args = []);
}
