<?php

namespace App\Admin\Filter;


use App\Form\Type\Admin\Filter\UserExtraCategoryFilterType;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Contracts\Filter\FilterInterface;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\FieldDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\FilterDataDto;
use EasyCorp\Bundle\EasyAdminBundle\Filter\FilterTrait;

class UserExtraCategoryFilter implements FilterInterface
{
    use FilterTrait;

    public static function new (string $propertyName, $label = null): self
    {
        return (new self())
            ->setFilterFqcn(__CLASS__)
            ->setProperty($propertyName)
            ->setLabel($label)
            ->setFormType(UserExtraCategoryFilterType::class);
    }


    public function apply (QueryBuilder $queryBuilder, FilterDataDto $filterDataDto, ?FieldDto $fieldDto, EntityDto $entityDto): void
    {
        $queryBuilder
            ->leftJoin($filterDataDto->getEntityAlias() . '.extra', 'extra')
            ->andWhere('extra.category = :category')
            ->setParameter('category', $filterDataDto->getValue());
    }
}