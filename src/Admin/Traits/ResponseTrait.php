<?php

namespace App\Admin\Traits;


use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;

trait ResponseTrait
{

    protected function redirectToIndex ()
    {
        $url = $this->get(AdminUrlGenerator::class)
            ->unsetAll()
            ->setAction(Action::INDEX)
            ->generateUrl();

        return $this->redirect($url);
    }
}