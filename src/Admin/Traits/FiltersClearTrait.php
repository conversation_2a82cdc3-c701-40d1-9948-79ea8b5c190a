<?php

namespace App\Admin\Traits;

use App\Entity\Filter;
use App\Entity\User;

trait FiltersClearTrait
{
    protected function getConditions() {
        $conditions = json_decode($this->request->getCurrentRequest()->getContent(), true);

        return $this->cleanConditions($conditions);
    }

    private function cleanConditions($conditions, $generalStats = true) {
        $user = $this->security->getUser();
        if (empty($conditions['country'])) {
            if ($user->isAdmin() || $this->settings->get('app.user.useFilters')) {
                unset($conditions['country']);
            } else if ($user->getManage()) {
                $conditions['country'] = $user->getManage()->getCountries();
            }
        }

        if (empty($conditions['center'])) {
            if ($user->isAdmin() || $this->settings->get('app.user.useFilters')) {
                unset($conditions['center']);
            } else if ($user->getManage()) {
                $conditions['center'] = $user->getManage()->getCenters();
            }
        }

        if (!empty($conditions['dateFrom']))
            $conditions['dateFrom'] = \DateTime::createFromFormat('Y-m-d H:i', $conditions['dateFrom'] . ' 00:00')->format('Y-m-d H:i:s');

        if (!empty($conditions['dateTo']))
            $conditions['dateTo'] = \DateTime::createFromFormat('Y-m-d H:i', $conditions['dateTo'] . ' 23:59')->format('Y-m-d H:i:s');


        if (!empty($conditions['filters'])) {
            /**
             * @var Filter[] $filters
             */
            $filters = $this->em->getRepository(Filter::class)->getFiltersByArray($conditions['filters']);
            $categories = [];
            foreach ($filters as $filter) {
                $categories[$filter->getFilterCategory()->getId()][] = $filter->getId();
            }
            $conditions['filters'] = $categories;
        } elseif (!in_array("ROLE_ADMIN", $user->getRoles()) && $this->settings->get('app.user.useFilters') && $generalStats) {
            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $user->getUsername()]);
            $filters = $user->getFilters();
            $categories = [];

            foreach ($filters as $filter) {
                $categories[$filter->getFilterCategory()->getId()][] = $filter->getId();
            }
            if (!empty($categories)) {
                $conditions['filters'] = $categories;
            }
        }

        return $conditions;
    }
}