<?php

declare(strict_types=1);

namespace App\Lti;

use App\Entity\User;
use App\Entity\Course;
use App\Entity\Chapter;
use App\Entity\LtiTool;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use OAT\Library\Lti1p3Ags\Model\Score\Score;
use OAT\Library\Lti1p3Ags\Model\Score\ScoreInterface;
use App\Campus\Service\UserCourseChapter\UserCourseService;

class ScoreService
{
    private $em;
    private ScoreRepository $scoreRepository;

    public const ACTIVITY_PROGRESS_STATUS_COMPLETED = 'Completed';

    public function __construct($em, ScoreRepository $scoreRepository)
    {
        $this->em = $em;
        $this->scoreRepository = $scoreRepository;
    }

    public function handleRequest(
        int $userId,
        string $resourceLinkId,
        array $content = [],
        UserCourseService $userCourseService
    ) {
        $ltiChapter = $this->em->getRepository(LtiTool::class)->findChapterByIndentifier($resourceLinkId);
        if (!$ltiChapter) {
            throw new \RuntimeException('LTI Chapter is not configured with identifier:' . $resourceLinkId);
        }

        $score = $this->fromArray($resourceLinkId, $content);
        $this->validateScore($score);

        if (null === $score->getScoreGiven() || 0 === $score->getScoreGiven()) {
            // When scoreGiven is not present or null, clear the score for the user
            $this->scoreRepository->cleanScore($resourceLinkId, '', $score->getUserIdentifier());
            return;
        }

        $this->register(
            $userId,
            $resourceLinkId,
            $score,
            $userCourseService
        );

        $this->scoreRepository->save($score);
    }

    private function validateScore(ScoreInterface $score)
    {
        if ($score->getScoreGiven() < 0) {
            throw new \RuntimeException('Score must not be less than 0');
        }
        if (empty($score->getUserIdentifier())) {
            throw new \RuntimeException('userId must be present');
        }
    }

    private function register(
        int $userId,
        string $resourceLinkId,
        ScoreInterface $content,
        $userCourseService
    ) {
        $dataChapterLti = $this->em->getRepository(Chapter::class)->getByIdentifier(
            $resourceLinkId,
            $userId
        );
        if (\is_null($dataChapterLti) || empty($dataChapterLti)) {
            throw new \RuntimeException('lti chapter is not find');
        }
        $user = $this->em->getRepository(User::class)->find($userId);

        foreach ($dataChapterLti as $chapter) {
            $course = $chapter->getCourse();

            $userCourses = $this->em->getRepository(UserCourse::class)->findBy([
                'user' => $user,
                'course' => $course,
            ]);

            if (empty($userCourses)) {
                $userCourses[] = $this->createScoreReference($user, $course);
            }

            foreach ($userCourses as $userCourse) {
                if (empty($this->em->getRepository(Chapter::class)->getByIdentifier($resourceLinkId, $userId, true, true))) {
                    $this->createScore($userCourse, $chapter, $content);
                } else {
                    $this->updateScore($userCourse, $chapter, $content);
                }

                if (count($userCourse->getCourse()->getChapters()) === 1) {
                    $userCourse->setFinishedAt(new \DateTime());
                    $this->em->flush();
                }
                $userCourseService->setFinishUserCourse($userCourse);
            }
        }
    }

    private function createScore(UserCourse $userCourse, Chapter $chapter, ScoreInterface $content)
    {
        $entity = new UserCourseChapter();
        $entity->setUserCourse($userCourse);
        $entity->setChapter($chapter);
        $entity->setStartedAt($content->getTimestamp());
        $entity->setUpdatedAt($content->getTimestamp());
        $entity->setTimeSpent(0);
        $entity->setFinishedAt(
            self::ACTIVITY_PROGRESS_STATUS_COMPLETED === $content->getActivityProgressStatus() ? $content->getTimestamp() : null
        );
        $entity->setPoints($content->getScoreGiven());
        $this->em->persist($entity);
        $this->em->flush();
    }

    private function updateScore(UserCourse $userCourse, Chapter $chapter, ScoreInterface $content)
    {
        $userCourseChapter = $this->em->getRepository(UserCourseChapter::class)->findOneBy(
            [
                'userCourse' => $userCourse,
                'chapter' => $chapter
            ]
        );

        if (\is_null($userCourseChapter)) {
            $this->createScore($userCourse, $chapter, $content);
        } else {
            $userCourseChapter->setUpdatedAtValue();
            $userCourseChapter->setTimeSpent(0);
            $userCourseChapter->setFinishedAt(
                self::ACTIVITY_PROGRESS_STATUS_COMPLETED === $content->getActivityProgressStatus() ? $content->getTimestamp() : null
            );
            $userCourseChapter->setPoints($content->getScoreGiven() ?? null);
            $this->em->persist($userCourseChapter);
            $this->em->flush();
        }
    }

    private function createScoreReference(User $user, Course $course)
    {
        $userCourse = new UserCourse();
        $userCourse->setUser($user);
        $userCourse->setCourse($course);
        $userCourse->setStartedAtValue();
        $this->em->persist($userCourse);
        $this->em->flush();

        return $userCourse;
    }

    public function fromArray(string $resourceId, array $content): ScoreInterface
    {
        return new Score(
            $content['userId'],
            $content['activityProgress'],
            $content['gradingProgress'],
            $resourceId,
            (float) ($content['scoreGiven'] ?? null),
            $content['scoreMaximum'] ?? null,
            $content['comment'] ?? null,
            empty($content['timestamp']) ? null : new \DateTimeImmutable($content['timestamp'])
        );
    }
}
