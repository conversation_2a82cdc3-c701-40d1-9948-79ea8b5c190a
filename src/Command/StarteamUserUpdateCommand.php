<?php

declare(strict_types=1);

namespace App\Command;

use App\Repository\UserRepository;
use App\Service\User\Authentication\StarTeam;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class StarteamUserUpdateCommand extends Command
{
    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly StarTeam $starTeam,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('starteam:users:update')
            ->setDescription('Update users information in DB from StarTeam')
            ->addOption(
                'limit',
                null,
                InputOption::VALUE_REQUIRED,
                'Update only users not updated in the last hours',
                30,
            )
            ->addOption(
                'updatedAt',
                null,
                InputOption::VALUE_REQUIRED,
                'Update only users not updated in the last hours',
                24,
            );
    }

    /**
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $users = $this->getUsers(
            limit: (int) $input->getOption('limit'),
            updatedAt: (int) $input->getOption('updatedAt'),
        );

        foreach ($users as $user) {
            $starTeamUser = $this->starTeam->getUserData($user->getCode());

            if ($starTeamUser) {
                $this->starTeam->saveEmployee($starTeamUser);
            }
        }

        return Command::SUCCESS;
    }

    private function getUsers(int $limit, int $updatedAt): array
    {
        return $this->userRepository->createQueryBuilder('u')
            ->andWhere('u.isActive = 1')
            ->andWhere('u.code IS NOT NULL')
            ->andWhere('u.updatedAt < :updatedAt')
            ->setParameter('updatedAt', (new \DateTime())->modify('-' . $updatedAt . ' hours'))
            ->orderBy('u.id', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }
}
