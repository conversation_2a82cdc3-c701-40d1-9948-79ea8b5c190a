<?php

namespace App\Twig;

use App\Service\DateFormatter\DateFormatterService;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use Twig\TwigFunction;

/**
 * Twig extension for date formatting
 */
class DateFormatterExtension extends AbstractExtension
{
    private DateFormatterService $dateFormatter;

    public function __construct(DateFormatterService $dateFormatter)
    {
        $this->dateFormatter = $dateFormatter;
    }

    /**
     * {@inheritdoc}
     */
    public function getFilters(): array
    {
        return [
            new TwigFilter('custom_format_date', [$this, 'formatDate']),
            new TwigFilter('custom_format_datetime', [$this, 'formatDateTime']),
            new TwigFilter('format_date_pattern', [$this, 'formatWithPattern']),
            new TwigFilter('format_relative_date', [$this, 'formatRelativeDate']),
            new TwigFilter('format_date_range', [$this, 'formatDateRange']),
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction('get_month_name', [$this, 'getMonthName']),
            new TwigFunction('get_day_name', [$this, 'getDayName']),
            new TwigFunction('format_date_range', [$this, 'formatDateRange']),
        ];
    }

    /**
     * Format a date according to the user's locale
     *
     * @throws \InvalidArgumentException When locale is not supported
     */
    public function formatDate(\DateTimeInterface $date, string $format = 'medium', ?string $locale = null): string
    {
        return $this->dateFormatter->formatDate($date, $format, $locale);
    }

    /**
     * Format a date and time according to the user's locale
     *
     * @throws \InvalidArgumentException When locale is not supported
     */
    public function formatDateTime(
        \DateTimeInterface $dateTime,
        string $dateFormat = 'medium',
        string $timeFormat = 'short',
        ?string $locale = null
    ): string {
        return $this->dateFormatter->formatDateTime($dateTime, $dateFormat, $timeFormat, $locale);
    }

    /**
     * Format a date with a custom pattern
     *
     * @throws \InvalidArgumentException When locale is not supported
     */
    public function formatWithPattern(\DateTimeInterface $date, string $pattern, ?string $locale = null): string
    {
        return $this->dateFormatter->formatWithPattern($date, $pattern, $locale);
    }

    /**
     * Format a date with full format
     *
     * @throws \InvalidArgumentException When locale is not supported
     */
    public function formatRelativeDate(
        \DateTimeInterface $date,
        ?string $locale = null
    ): string {
        return $this->dateFormatter->formatRelativeDate($date, $locale);
    }

    /**
     * Format a date range
     */
    public function formatDateRange(
        \DateTimeInterface $startDate,
        \DateTimeInterface $endDate,
        string $format = 'medium',
        ?string $locale = null
    ): string {
        return $this->dateFormatter->formatDateRange($startDate, $endDate, $format, $locale);
    }

    /**
     * Get month name in the specified locale
     *
     * @throws \InvalidArgumentException When month number is invalid or locale is not supported
     */
    public function getMonthName(int $monthNumber, ?string $locale = null): string
    {
        return $this->dateFormatter->getMonthName($monthNumber, $locale);
    }

    /**
     * Get day name in the specified locale
     *
     * @throws \InvalidArgumentException When day number is invalid or locale is not supported
     */
    public function getDayName(int $dayNumber, ?string $locale = null, bool $abbreviated = false): string
    {
        return $this->dateFormatter->getDayName($dayNumber, $locale, $abbreviated);
    }
}
