<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\Chapter;
use App\Entity\OrdenarMenormayor;
use App\Enum\Games as EnumGameFormula;

class Enigma extends Game
{
    public function getQuestions($userCourseChapter): array
    {
        $wordleQuestions = $userCourseChapter->getChapter()->getOrdenarMenormayors();
        $questions = $this->getFormattedQuestions($wordleQuestions);

        return [
            'questions' => $questions,
        ];
    }

    private function getFormattedQuestions($wordleQuestions): array
    {
        $questions = [];

        foreach ($wordleQuestions as $question) {
            $wordsText = $question->getWordsArray();
            $letters = mb_strlen($wordsText, 'UTF-8'); // Utiliza mb_strlen para contar caracteres multibyte

            $questions[] = [
                'id' => $question->getId(),
                'time' => $question->getTime(),
                'question' => $question->getTitle(),
                'letters' => $letters,
            ];
        }

        return $questions;
    }

    public function check($userCourseChapter, $answers): array
    {
        $repository = $this->em->getRepository(OrdenarMenormayor::class);
        $expectedAnswer = $repository->find($answers->questionId);
        $sizeOfexpectedAnswer = count(mb_str_split($expectedAnswer->getWordsArray(), 1, 'UTF-8'));

        // create a new array with the expected answer
        $answerEmpty = array_fill(0, $sizeOfexpectedAnswer, 0);
        $answerLetters = isset($answers->word) ? mb_str_split($answers->word, 1, 'UTF-8') : $answerEmpty;
        $expectedLetters = mb_str_split(strtolower($expectedAnswer->getWordsArray()), 1, 'UTF-8');
        $letters = [];

        foreach ($answerLetters as $i => $answerLetter) {
            if (!\is_string($answerLetter)) {
                continue;
            }
            $expectedLetter = $expectedLetters[$i] ?? null;
            $correct = \is_string($answerLetter) && mb_strtolower($answerLetter, 'UTF-8') === mb_strtolower($expectedLetter, 'UTF-8');
            $present = \is_string($answerLetter) && \in_array(strtolower($answerLetter), $expectedLetters);

            $letters[] = [
                'letter' => $answerLetter,
                'correct' => $correct,
                'present' => $present,
            ];
        }

        return [
            'letters' => $letters,
        ];
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (!isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal'])
            || !isset($data['totalQuestions'])
            || !$data['totalQuestions'] > 0) {
            return 0;
        }

        $chapterType = $args->getType();
        $answers = $data['answers'];
        $maxTime = $data['timeTotal'];
        $nQuestions = $data['totalQuestions'];
        $nAnswers = \count($answers);
        $nAttempts = \array_key_exists('attempts', $data) ? \count($data['attempts']) : 1;
        $answerAttempts = 0;
        $correctAnswers = 0;
        $time = 0;

        if($nAnswers > 0){
            foreach ($answers as $answer) {
                foreach ($answer['attempts'] as $attempt) {
                    $time += $attempt['time'];
                    ++$answerAttempts;
                    if (\count($attempt['letters']) > 0) {
                        ++$correctAnswers;
                    }
                }
            }
        }

        $completionPercentage = $nAnswers > 0 ? ($correctAnswers / $nQuestions) : 0;
        if ($completionPercentage < $chapterType->getPercentageCompleted()) {
            return 0;
        }
        $remainingTime = ($maxTime - $time) > 0 ? ($maxTime - $time) : 1;
        $basePercentage = EnumGameFormula::BASE_HALF
            + (EnumGameFormula::BASE_QUARTER * ((($nQuestions * (EnumGameFormula::ENIGMA_MAX_TRY_PER_WORDLE - 1)) - ($answerAttempts - $nQuestions)) / ($nQuestions * (EnumGameFormula::ENIGMA_MAX_TRY_PER_WORDLE - 1))) + (EnumGameFormula::BASE_QUARTER * $remainingTime / $maxTime));
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

        return $attemptsCorrectionPercentage;
    }
}
