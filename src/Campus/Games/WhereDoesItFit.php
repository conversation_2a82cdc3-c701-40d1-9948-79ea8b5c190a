<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\Categorize as CategorizeEntity;
use App\Entity\CategorizeAnswers;
use App\Entity\Chapter;
use App\Enum\Games as EnumGameFormula;
use App\Enum\Games as GamesEnum;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

class WhereDoesItFit extends Game
{
    protected EntityManagerInterface $em;
    protected SettingsService $settings;

    public function __construct(EntityManagerInterface $em, SettingsService $settings)
    {
        $this->em = $em;
        $this->settings = $settings;
        parent::__construct($em);
    }

    public function getQuestions($userCourseChapter): array
    {
        $repository = $this->em->getRepository(CategorizeEntity::class);

        $categorize = $repository->findBy([
            'chapter' => $userCourseChapter->getChapter()->getId(),
        ]);

        $questions = $this->getFormattedQuestions($categorize);
        $time = $this->getTotalTime($questions);

        return [
            'questions' => $questions,
            'time' => $time,
        ];
    }

    private function getFormattedQuestions($categorizeQuestions): array
    {
        $pathCategorize = $this->settings->get(GamesEnum::WHERE_DOES_IT_FIT_UPLOADS_PATH_KEY) . '/';

        $questions = [];
        foreach ($categorizeQuestions as $question) {
            $answers = $this->getFormattedAnswers($question->getCategorizeAnswers());

            $questions[] = [
                'id' => $question->getId(),
                'text' => $question->getQuestion(),
                'categorized' => true,
                'imageUrl' => $pathCategorize . $question->getImage(),
                'answers' => $answers,
                'time' => $question->getTime(),
            ];
        }

        return $questions;
    }

    private function getFormattedAnswers($answersData): array
    {
        $pathCategorizeOptions = '/' . $this->settings->get(GamesEnum::WHERE_DOES_IT_FIT_UPLOADS_OPTION_PATH_KEY) . '/';

        $answers = [];
        foreach ($answersData as $answer) {
            $answers[] = [
                'id' => $answer->getId(),
                'answer' => $answer->getOptions()->getName(),
                'image' => $pathCategorizeOptions . $answer->getOptions()->getImage(),
            ];
        }

        return $answers;
    }

    private function getTotalTime($questions)
    {
        $time = 0;
        foreach ($questions as $question) {
            $time += $question['time'];
        }

        return $time;
    }

    public function check($userCourseChapter, $answers): array
    {
        return [
            'correct' => $this->isCorrect($answers),
        ];
    }

    private function isCorrect($answer)
    {
        if (!isset($answer) || !isset($answer->id)) {
            return false;
        }

        $answerId = $answer->id;
        $repository = $this->em->getRepository(CategorizeAnswers::class);
        $categorizedAnswers = $repository->find(['id' => $answerId]);

        return $categorizedAnswers->isCorrect();
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (!isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal'])
            || !isset($data['totalQuestions'])) {
            return 0;
        }

        $chapterType = $args->getType();
        $percentageCompleted = $chapterType->getPercentageCompleted();
        $answers = $data['answers'];
        $nAnswers = \count($answers);
        $nRequiredQuestions = round($nAnswers * $percentageCompleted, 1);
        $nQuestions = $data['totalQuestions'];
        $maxTime = $data['timeTotal'];
        $rightAnswers = 0;
        $time = 0;
        $nAttempts = \array_key_exists('attempts', $data) && \is_array($data['attempts'])
            ? \count($data['attempts']) : 1;

        if($nAnswers > 0){
            foreach ($answers as $answer) {
                if (isset($answer['correct']) && $answer['correct']) {
                    ++$rightAnswers;
                }
                $time += $answer['time'];
            }
        }

        $completionPercentage = $nQuestions > 0 ? ($rightAnswers / $nQuestions) : 0;
        if ($completionPercentage < $percentageCompleted) {
            return 0;
        }
        $remainingTime = ($maxTime - $time) > 0 ? ($maxTime - $time) : 1;
        $denominator = $nQuestions - $nRequiredQuestions;
        $basePercentage = EnumGameFormula::BASE_HALF + (EnumGameFormula::BASE_QUARTER * (($rightAnswers - $nRequiredQuestions) / $denominator)) + (EnumGameFormula::BASE_QUARTER * ($remainingTime / $maxTime));
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

        return $attemptsCorrectionPercentage;
    }
}
