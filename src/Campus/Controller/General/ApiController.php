<?php

declare(strict_types=1);

namespace App\Campus\Controller\General;

use App\Entity\Course;
use App\Entity\User;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\CourseSegmentCategoryRepository;
use App\Repository\HelpCategoryRepository;
use App\Repository\UserRepository;
use App\Service\General\ScoreService;
use App\Service\SettingsService;
use App\Service\User\General\UserLevelService;
use App\Service\User\Token\GenericTokenService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Swagger\Annotations as SWG;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController.
 *
 * @Route("/api")
 */
class ApiController extends ApiBaseController
{
    private $em;
    private $mailer;

    /**
     * ApiController constructor.
     */
    public function __construct(LoggerInterface $logger, UserRepository $userRepository, CourseRepository $courseRepository, AnnouncementRepository $announcementRepository, EntityManagerInterface $em, TranslatorInterface $translator, MailerInterface $mailer, SettingsService $settings)
    {
        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settings);
        $this->em = $em;
        $this->mailer = $mailer;
    }

    /**
     * @Rest\Get("/help", name="api_help")
     *
     * @SWG\Response(
     *     response=200,
     *     description="Text load successfully"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="Text doesn't load"
     * )
     * @SWG\Response(
     *     response=401,
     *     description="The user has no access to this text"
     * )
     * @SWG\Response(
     *     response=500,
     *     description="Error"
     * )
     *
     * @SWG\Tag(name="Help")
     */
    public function loadHelpText(HelpCategoryRepository $helpCategoryRepository)
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;
            $message = 'Help Texts retrieve successfully';

            $user = $this->userRepository->findOneBy(['email' => $this->getUser()->getUsername()]);

            $helpCategories = $helpCategoryRepository->findAll();

            $helpTexts = [];

            foreach ($helpCategories as $category) {
                $categoryToSend = [
                    'id' => $category->getId(),
                    'name' => ($category->getTranslations()->get($user->getLocaleCampus())) ? $category->getTranslations()->get($user->getLocaleCampus())->getName() : $category->getName(),
                    'helpTexts' => [],
                ];

                foreach ($category->getHelpTexts() as $helpText) {
                    if ($helpText->getTranslations()->get($user->getLocaleCampus())) {
                        $textToSend = [
                            'id' => $helpText->getId(),
                            'title' => $helpText->getTranslations()->get($user->getLocaleCampus())->getTitle(),
                            'text' => $helpText->getTranslations()->get($user->getLocaleCampus())->getText(),
                        ];
                    } else {
                        $textToSend = [
                            'id' => $helpText->getId(),
                            'title' => $helpText->getTitle(),
                            'text' => $helpText->getText(),
                        ];
                    }

                    array_push($categoryToSend['helpTexts'], $textToSend);
                }

                array_push($helpTexts, $categoryToSend);
            }
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = "An error has occurred trying to get help text: {$e->getMessage()}";
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $helpTexts : $message,
        ];

        return $this->sendResponse($response, ['groups' => 'help']);
    }

    /**
     * @Rest\Post("/sendusercomment", name="api_help_usercomment")
     *
     * @SWG\Response(
     *     response=200,
     *     description="Text save successfully"
     * )
     * @SWG\Response(
     *     response=404,
     *     description="Text doesn't save"
     * )
     * @SWG\Response(
     *     response=401,
     *     description="The text hasnt been saved"
     * )
     * @SWG\Response(
     *     response=500,
     *     description="Error"
     * )
     *
     *  * @SWG\Parameter(
     *     name="type",
     *     in="query",
     *     type="number",
     *     description="The comment's type",
     *     schema={}
     * )
     *  * @SWG\Parameter(
     *     name="comment",
     *     in="query",
     *     type="string",
     *     description="The comment's text",
     *     schema={}
     * )
     *
     * @SWG\Tag(name="Help")
     */
    public function sendUserComment(Request $request)
    {
        try {
            $subject = $request->get('subjectEmail');
            $body_email = $request->get('bodyEmail');
            $count_files = $request->get('count-files');

            if ($this->isGranted('IS_AUTHENTICATED_FULLY')) {
                $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
            } else {
                $user = null;
            }

            $code = Response::HTTP_OK;
            $error = false;
            $languageDefault = $this->settings->get('app.defaultLanguage');
            $locale = (isset($user) && null != $user->getLocaleCampus()) ? $user->getLocaleCampus() : $languageDefault;

            $message_ok = $this->translator->trans('email.template_email.consultation', [], 'email', $locale);

            foreach ($this->settings->get('app.supportEmail') as $email) {
                $sendEmail = (new TemplatedEmail())
                    ->from($this->settings->get('app.fromEmail'))
                    ->to($email)
                    ->subject($subject)
                    ->htmlTemplate('template_email/email-received.html.twig')
                    ->context([
                        'user' => $user,
                        'bodyEmail' => $body_email,
                    ]);

                for ($i = 0; $i < $count_files; ++$i) {
                    $file = $request->files->get('file'.$i);
                    $sendEmail->embedFromPath($file, $file->getClientOriginalName());
                }

                $this->mailer->send($sendEmail);
            }
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = "An error has occurred trying to get help text: {$e->getMessage()}";
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $message_ok : $message,
        ];

        return $this->sendResponse($response);
    }

    public function getInfoUser()
    {
        if ($this->isGranted('IS_AUTHENTICATED_FULLY')) {
            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
        } else {
            $user = null;
        }

        return $user;
    }

    /**
     * @Rest\Get("/course-filters", name="api_course_filters")
     *
     * @SWG\Response(
     *     response=200,
     *     description="Text save successfully"
     * )
     *
     * @SWG\Tag(name="CourseFilters")
     */
    public function courseFilters(CourseSegmentCategoryRepository $courseSegmentCategoryRepository)
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;
            $languageDefault = $this->settings->get('app.defaultLanguage');
            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
            $locale = null != $user->getLocaleCampus() ? $user->getLocaleCampus() : $languageDefault;
            $courseSegment = [];

            foreach ($courseSegmentCategoryRepository->findAll() as $segment) {
                $courseSegmentCategoryById = $courseSegmentCategoryRepository->getCourseSegmentCategoryTranslation($segment->getId(), $locale);
                $segments = [];

                foreach ($segment->getSegments() as $seg) {
                    $courseSegmentById = $courseSegmentCategoryRepository->getCourseSegmentTranslation($seg->getId(), $locale);
                    array_push($segments, [
                        'id' => $seg->getId(),
                        'name' => $courseSegmentById ? $courseSegmentById['name'] : $seg->getName(),
                    ]);
                }
                array_push($courseSegment, [
                    'id' => $segment->getId(),
                    'name' => $courseSegmentCategoryById ? $courseSegmentCategoryById['name'] : $segment->getName(),
                    'segments' => $segments,
                ]);
            }

            $courseFilters = [
                'segmentsCategory' => $courseSegment,
            ];
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = "An error has occurred trying to get passport: {$e->getMessage()}";
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $courseFilters : $message,
        ];

        return $this->sendResponse($response, ['groups' => 'courseFilters']);
    }

    /**
     * @Rest\Get("/user-level", name="api_user_level")
     *
     * @SWG\Response(
     *     response=200,
     *     description="Success"
     * )
     *
     * @SWG\Tag(name="UserLevel")
     */
    public function getUserLevel(UserLevelService $userLevelService)
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;

            $user = $this->getInfoUser();

            if ($user) {
                $level = $userLevelService->userLevel($user);
            }
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = "An error has occurred trying to get user level: {$e->getMessage()}";
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $level : $message,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Rest\Get("/campus-day-score")
     */
    public function testScore(ScoreService $scoreService, Request $request): Response
    {
        $center = $request->get('filtro_centro_id');
        $startedAt = $request->get('filtro_fecha_ini');
        $finishedAt = $request->get('filtro_fecha_fin');

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $scoreService->generateCampusDays($center, $startedAt, $finishedAt, true),
        ]);
    }

    /**
     * @Rest\Post("/generic-token")
     */
    public function handleGenericToken(Request $request, GenericTokenService $genericTokenService): Response
    {
        try {
            $content = json_decode($request->getContent(), true);
            if (empty($content['token'])) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => '"token" field required',
                ]);
            }

            $result = $genericTokenService->handleGenericToken($content['token']);

            return $this->sendResponse([
                'status' => $result['error'] ? Response::HTTP_ACCEPTED : Response::HTTP_OK,
                'error' => $result['error'],
                'data' => $result['data'],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }
}
