<?php

namespace App\Campus\Controller\General;

use App\Entity\Message;
use App\Entity\MessageAttachment;
use App\Entity\Notification;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\MessageRepository;
use App\Repository\NewsRepository;
use App\Repository\NotificationRepository;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Swagger\Annotations as SWG;
use Vich\UploaderBundle\Handler\DownloadHandler;
use Symfony\Contracts\Translation\TranslatorInterface;


/**
 * Class ApiController
 * @package App\Controller
 *
 * @Route("/api")
 */
class ApiNewsController extends ApiBaseController
{

    private $em;
    private $notificationRepository;


    /**
     * ApiController constructor.
     *
     * @param LoggerInterface $logger
     * @param UserRepository $userRepository
     * @param CourseRepository $courseRepository
     * @param AnnouncementRepository $announcementRepository
     * @param EntityManagerInterface $em
     * @param NotificationRepository $notificationRepository
     * @param TranslatorInterface $translator
     */
    public function __construct(
        LoggerInterface $logger,
        UserRepository $userRepository,
        CourseRepository $courseRepository,
        AnnouncementRepository $announcementRepository,
        EntityManagerInterface $em,
        NotificationRepository $notificationRepository,
        TranslatorInterface $translator,
        SettingsService $settingsService
    ) {

        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settingsService);
        $this->em = $em;
    }


    /**
     * @Rest\Get("/news", name="api_news_get")
     *
     * @return Response
     */
    public function getNews(NewsRepository $newsRepository)
    {
        $user = $this->userRepository->findOneBy(array('email' => $this->getUser()->getUsername()));

        $news = $newsRepository->findBy([
            'locale' => $user->getLocale()
        ]);

        $response = [
            'status' => Response::HTTP_OK,
            'error'  => false,
            'data'   => $news,
        ];

        return $this->sendResponse($response, ['groups' => ['news']]);
    }
}
