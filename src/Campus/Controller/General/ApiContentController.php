<?php

declare(strict_types=1);

namespace App\Campus\Controller\General;

use App\Entity\Chapter;
use App\Enum\ChapterType as EnumChapterType;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController.
 *
 * @Route("/api")
 */
class ApiContentController extends ApiBaseController
{
    private $em;

    /**
     * ApiController constructor.
     */
    public function __construct(LoggerInterface $logger, UserRepository $userRepository, CourseRepository $courseRepository, AnnouncementRepository $announcementRepository, EntityManagerInterface $em, TranslatorInterface $translator, SettingsService $settingsService)
    {
        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settingsService);
        $this->em = $em;
    }

    /**
     * @Rest\Get("/contents/{id}/get", name="api_contents_get")
     *
     * @return Response
     */
    public function getContents(Chapter $chapter)
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;
            $message = $this->translator->trans('message_api.content.scorm_correctly', [], 'message_api', 'en');

            $course = $chapter->getCourse();

            if (!$this->checkCourseAccess($course)) {
                $code = Response::HTTP_UNAUTHORIZED;
                $error = true;
                $message = $this->translator->trans('message_api.content.scorm_user_not_access', [], 'message_api', 'en');
            } else {
                if (EnumChapterType::CONTENT_TYPE === $chapter->getType()->getId()) {
                    $contents = $chapter->getContents();
                    $contentData = [];
                    $size = \count($contents) ?? 0;
                    foreach ($contents as $cont) {
                        $contentData[] = [
                            'id' => $cont->getId(),
                            'title' => $cont->getTitle(),
                            'content' => $cont->getContent(),
                            'position' => $cont->getPosition() < $size ? $cont->getPosition() : $size,
                        ];
                    }

                    $data = [
                        'chapter' => [
                            'id' => $chapter->getId(),
                            'title' => $chapter->getTitle(),

                            'contents' => $contentData,
                        ],
                    ];
                } else {
                    $code = Response::HTTP_UNAUTHORIZED;
                    $error = true;
                    $message = $this->translator->trans('message_api.content.scorm_not_content', [], 'message_api', 'en');
                }
            }
        } catch (\Exception $e) {
            $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            $error = true;
            $message = "An error has occurred trying to start chapter: {$e->getMessage()}";
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK == $code ? $data : $message,
        ];

        return $this->sendResponse($response, ['groups' => ['contents']]);
    }
}
