<?php

declare(strict_types=1);

namespace App\Campus\Controller\General;

use App\Campus\Service\UserCourseChapter\UserCourseService;
use App\Entity\AdivinaImagen;
use App\Entity\Announcement;
use App\Entity\AnnouncementTemporalization;
use App\Entity\AnnouncementUser;
use App\Entity\Categorize;
use App\Entity\Chapter;
use App\Entity\FillGaps;
use App\Entity\HigherLower;
use App\Entity\OrdenarMenormayor;
use App\Entity\Parejas;
use App\Entity\Question;
use App\Entity\Scorm;
use App\Entity\TimeGame;
use App\Entity\TrueOrFalse;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Entity\Videoquiz;
use App\Enum\ChapterContent;
use App\Enum\Games;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\ScormRepository;
use App\Repository\UserCourseChapterRepository;
use App\Repository\UserCourseRepository;
use App\Repository\UserRepository;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementUserService;
use App\Service\General\IpService;
use App\Service\SettingsService;
use App\Service\Traits\Announcement\AnnouncementTemporalizationTrait;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController.
 *
 * @Route("/api")
 */
class ApiChapterController extends ApiBaseController
{
    use AnnouncementTemporalizationTrait;

    private $em;
    private $userCourseChapterRepository;
    private $userCourseRepository;
    private $announcementConfigurationsService;
    private $announcementUserService;
    private $requestStack;
    private $ipService;
    private $userCourseService;

    /**
     * ApiController constructor.
     */
    public function __construct(
        LoggerInterface $logger,
        UserRepository $userRepository,
        CourseRepository $courseRepository,
        AnnouncementRepository $announcementRepository,
        EntityManagerInterface $em,
        TranslatorInterface $translator,
        UserCourseChapterRepository $userCourseChapterRepository,
        UserCourseRepository $userCourseRepository,
        AnnouncementConfigurationsService $announcementConfigurationsService,
        AnnouncementUserService $announcementUserService,
        SettingsService $settings,
        RequestStack $requestStack,
        IpService $ipService,
        UserCourseService $userCourseService
    ) {
        parent::__construct(
            $logger,
            $userRepository,
            $courseRepository,
            $announcementRepository,
            $translator,
            $settings
        );

        $this->em = $em;
        $this->userCourseChapterRepository = $userCourseChapterRepository;
        $this->userCourseRepository = $userCourseRepository;
        $this->announcementConfigurationsService = $announcementConfigurationsService;
        $this->announcementUserService = $announcementUserService;
        $this->requestStack = $requestStack;
        $this->ipService = $ipService;
        $this->userCourseService = $userCourseService;
    }

    /**
     * @Rest\Get("/chapter/{id}/start", name="api_chapter_start")
     */
    public function chapterStart(Chapter $chapter)
    {
        try {
            if (!$this->checkCourseAccess($chapter->getCourse())) {
                throw new \Exception('The user has no access to this course', Response::HTTP_UNAUTHORIZED);
            }

            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $this->initChapter($chapter),
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ];
        }

        return $this->sendResponse($response, ['groups' => 'progress']);
    }

    private function initChapter(Chapter $chapter)
    {
        $user = $this->getUser();
        $userCourse = $this->fetchUserCourse($chapter);
        $userCourseChapter = $this->fetchUserCourseChapter($chapter);
        $seconds = Games::PUZZLE_TYPE == $chapter->getType()->getId() ? array_sum(Games::PUZZLE_TIME_GAPS) : $userCourseChapter->getChapter()->getGameTotalTime();
        $description = ($chapter->getType()->isGame()) ? $this->translator->trans($chapter->getType()->getStartDescriptionLabel(), ['%seconds%' => $seconds], 'chapters', $user->getLocaleCampus()) : null;

        return [
            'course' => $userCourse,
            'chapter' => $userCourseChapter,
            'locale' => $user->getLocaleCampus(),
            'description' => $description,
        ];
    }

    private function fetchUserCourse(Chapter $chapter)
    {
        $announcement = $this->findAnnouncement($chapter);

        return $this->userCourseRepository->findOneBy([
            'user' => $this->getUser(),
            'course' => $chapter->getCourse(),
            'announcement' => ($announcement) ?: null,
        ]) ?? $this->createUserCourse($chapter);
    }

    private function createUserCourse(Chapter $chapter)
    {
        $announcement = $this->findAnnouncement($chapter);

        $userCourse = new UserCourse();
        $userCourse->setUser($this->getUser());
        $userCourse->setCourse($chapter->getCourse());
        if ($announcement) {
            $userCourse->setAnnouncement($announcement);
        }
        $this->em->persist($userCourse);
        $this->em->flush();

        return $userCourse;
    }

    private function findAnnouncement(Chapter $chapter)
    {
        $announcement = $this->em->getRepository(Announcement::class)->findAnnouncementUserNotified($chapter->getCourse(), $this->getUser());

        return $announcement && Announcement::STATUS_ACTIVE === $announcement->getStatus() ? $announcement : null;
    }

    private function fetchUserCourseChapter(Chapter $chapter)
    {
        return $this->userCourseChapterRepository->findOneBy([
            'userCourse' => $this->fetchUserCourse($chapter),
            'chapter' => $chapter,
        ]) ?? $this->createUserCourseChapter($chapter);
    }

    private function createUserCourseChapter(Chapter $chapter)
    {
        $userCourseChapter = new UserCourseChapter();
        $userCourseChapter->setUserCourse($this->fetchUserCourse($chapter));
        $userCourseChapter->setChapter($chapter);
        $userCourseChapter->setTimeSpent(0);

        $this->em->persist($userCourseChapter);
        $this->em->flush();

        return $userCourseChapter;
    }

    /**
     * @Rest\Post("/chapter/{id}/update", name="api_chapter_update")
     *
     * @return Response
     */
    public function chapterUpdate(UserCourseChapter $userCourseChapter, Request $request)
    {
        try {
            $code = Response::HTTP_OK;
            $error = false;
            $message = 'Chapter correctly updated';
            $course = $userCourseChapter->getUserCourse()->getCourse();

            if (!$this->checkCourseAccess($course)) {
                throw new \Exception('The user has no access to this course', Response::HTTP_UNAUTHORIZED);
            }

            $data = json_decode($request->getContent(), true);

            if (!$this->isUserAbleToCompleteChapter($userCourseChapter)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_OK,
                    'error' => false,
                    'data' => 'you do not have time to complete the chapter',
                ]);
            }

            $dataChapter = $this->updateChapter($userCourseChapter, $data);
            $message = $this->endOfChapter($dataChapter, $userCourseChapter);

            $this->em->persist($userCourseChapter);
            $this->em->flush();
        } catch (\Exception $e) {
            $errorMessage = \sprintf('Error on line %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());

            return $this->sendResponse(['error' => true, 'status' => 500, 'message' => $errorMessage]);
        }

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => $message,
        ];

        return $this->sendResponse($response);
    }

    private function updateChapter(UserCourseChapter $userCourseChapter, $data)
    {
        $now = new \DateTime();
        $announcement = $this->findAnnouncement($userCourseChapter->getChapter());

        if (!$announcement) {
            $this->saveDataChapterInBd($userCourseChapter, $data);

            return $data;
        }

        $announcementTemporalization = $this->em->getRepository(AnnouncementTemporalization::class)
            ->findOneBy(['announcement' => $announcement, 'chapter' => $userCourseChapter->getChapter()]);

        $isGame = $userCourseChapter->getChapter()->getType()->isGame();
        if ($isGame) {
            $this->saveDataChapterInBd($userCourseChapter, $data);

            return $data;
        }

        $finishDateTemporalization = $this->getFinishDateTemporalization($announcement, $announcementTemporalization);
        $hasMinimunTimeChapter = $this->hasMinimunTimeChapter($announcementTemporalization, true);

        $timeInRequest = $data['time'] ?? ($data['data']['time'] ?? 0);

        if (!$hasMinimunTimeChapter) {
            $this->saveDataChapterInBd($userCourseChapter, $data);

            return $data;
        }

        if ($data['finished'] ?? ($data['data']['finished'] ?? false)) {
            $this->saveDataChapterInBd($userCourseChapter, $data);
        }

        if ($hasMinimunTimeChapter && $finishDateTemporalization > $now) {
            $minimunTime = $announcementTemporalization->getMinimumTime() > $timeInRequest ? $announcementTemporalization->getMinimumTime() - $timeInRequest : 0;

            $this->updateFinishedStatus($userCourseChapter, $data, $minimunTime);

            $this->saveDataChapterInBd($userCourseChapter, $data);

            return $data;
        }
    }

    private function updateFinishedStatus(UserCourseChapter $userCourseChapter, &$data, $minimumTime)
    {
        $previous = $userCourseChapter->getData();

        if (isset($previous['finished']) && $previous['finished']) {
            $data['finished'] = true;
            $finished = true;
        } else {
            $finished = $data['finished'] ?? ($data['data']['finished'] ?? false);
        }

        $setFinished = $finished && $userCourseChapter->getTimeSpent() >= $minimumTime;

        if (isset($data['finished'])) {
            $data['finished'] = $setFinished;
        } elseif (isset($data['data']['finished'])) {
            $data['data']['finished'] = $setFinished;
        }
    }

    private function saveDataChapterInBd(UserCourseChapter $userCourseChapter, $data)
    {
        $this->updateChapterScorm($data, $userCourseChapter);
        $this->updateTimeChapter($data, $userCourseChapter);
        $this->saveDataOfTheChapter($data, $userCourseChapter);

        $ip = $this->ipService->getClientIp();

        $userCourseChapter->setIp($ip);

        $this->em->persist($userCourseChapter);
        $this->em->flush();
    }

    private function isUserAbleToCompleteChapter(UserCourseChapter $userCourseChapter)
    {
        $now = new \DateTime();
        $announcement = $this->findAnnouncement($userCourseChapter->getChapter());
        if (!$announcement) {
            return true;
        }

        if ($now > $announcement->getFinishAt()) {
            return false;
        }

        $hasTemporalization = $this->announcementConfigurationsService->hasTemporalization($announcement);
        if (!$hasTemporalization) {
            return true;
        }

        $announcementTemporalization = $this->em->getRepository(AnnouncementTemporalization::class)
            ->findOneBy(['announcement' => $announcement, 'chapter' => $userCourseChapter->getChapter()]);

        $finishDateTemporalization = $this->getFinishDateTemporalization($announcement, $announcementTemporalization);
        $hasMinimunTimeChapter = $this->hasMinimunTimeChapter($announcementTemporalization, $hasTemporalization);

        if (!$hasMinimunTimeChapter) {
            return true;
        }

        if ($hasMinimunTimeChapter && $finishDateTemporalization < $now) {
            return false;
        }

        return true;
    }

    private function updateTimeChapter($data, UserCourseChapter $userCourseChapter): void
    {
        if (isset($data['time']) || isset($data['data']['time'])) {
            $time = (isset($data['time'])) ? $data['time'] : $data['data']['time'];
            $userCourseChapter->setTimeSpent($userCourseChapter->getTimeSpent() + $time);
            if (isset($data['data']['time'])) {
                unset($data['data']['time']);
            }
        }
    }

    private function saveDataOfTheChapter($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();

        $games = [
            Games::SECRET_WORD_TYPE => 'updateGamesFirst',
            Games::QUIZ_TYPE => 'updateGamesFirst',
            Games::WHEEL_TYPE => 'updateGamesFirst',
            Games::DOUBLE_OR_NOTHING_TYPE => 'updateGamesFirst',
            Games::LETTERS_WHEEL_TYPE => 'updateGamesLetterWheel',
            Games::PUZZLE_TYPE => 'updateGamesFirst',
            Games::VIDEO_QUIZ_TYPE => 'updateChapterVideoQuiz',
            Games::WORD_SEARCH_TYPE => 'updateLetterSoup',
            Games::FILL_IN_THE_BLANKS_TYPE => 'updateFillGaps',
            Games::HIGHER_OR_LOWER_TYPE => 'updateHighLower',
            Games::WHERE_DOES_IT_FIT_TYPE => 'updateCategorize',
            Games::TRUE_OR_FALSE_TYPE => 'updateTrueOrFalse',
            Games::MATCH_TYPE => 'updatePairs',
            Games::RIDDLE_TYPE => 'updateGuessImagen',
            Games::SORT_LETTERS_TYPE => 'updateOrderLetters',
            Games::ENIGMA_TYPE => 'updateEnigma',
        ];

        if (isset($data['data'])) {
            if (\array_key_exists($userCourseChapter->getChapter()->getType()->getId(), $games)) {
                $this->{$games[$userCourseChapter->getChapter()->getType()->getId()]}($data, $userCourseChapter);
            } else {
                $userCourseChapter->setData(array_merge($previous, $data['data']));
            }
        }
    }

    private function updateGamesFirst($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();
        $questions = $this->em->getRepository(Question::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);

        $totalQuestions = count($questions) ?? 0;
        $timeTotal = 0;

        foreach ($questions as $question) {
            $timeTotal += $question->getTime();
        }

        if (isset($data['data'])) {
            $data['data'] = array_merge($data['data'], ['totalQuestions' => $totalQuestions, 'timeTotal' => $timeTotal]);
            $userCourseChapter->setData(array_merge($previous, $data['data']));
        }
    }

    private function updateGamesLetterWheel($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();
        $timeGameData = $this->em->getRepository(TimeGame::class)->findOneBy(['chapter' => $userCourseChapter->getChapter()->getId()]);

        $timeTotal = isset($timeGameData) ? $timeGameData->getTime() : 0;

        if (isset($data['data'])) {
            $data['data'] = array_merge($data['data'], ['timeTotal' => $timeTotal]);
            $userCourseChapter->setData(array_merge($previous, $data['data']));
        }
    }

    private function updateChapterVideoQuiz($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();

        if (Games::VIDEO_QUIZ_TYPE == $userCourseChapter->getChapter()->getType()->getId()) {
            $videoQuiz = $this->em->getRepository(Videoquiz::class)->findOneBy(['chapter' => $userCourseChapter->getChapter()->getId()]);

            $videoQuestions = count($videoQuiz->getVideopreguntas()) ?? 0;
            if (isset($data['data'])) {
                $data['data'] = array_merge($data['data'], ['totalVideo' => $videoQuiz->getVideoDuration(), 'videoQuestions' => $videoQuestions]);
                $userCourseChapter->setData(array_merge($previous, $data['data']));
            }
        }
    }

    private function updateLetterSoup($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();
        if (Games::WORD_SEARCH_TYPE == $userCourseChapter->getChapter()->getType()->getId()) {
            $letterSoup = $this->em->getRepository(OrdenarMenormayor::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);

            $words = 0;
            $time = 0;
            foreach ($letterSoup as $word) {
                $wordsByQuestion = explode(',', $word->getWordsArray());
                $time += $word->getTime();
                foreach ($wordsByQuestion as $word) {
                    ++$words;
                }
            }

            if (isset($data['data'])) {
                $data['data'] = array_merge($data['data'], ['words' => $words, 'timeTotal' => $time]);
                $userCourseChapter->setData(array_merge($previous, $data['data']));
            }
        }
    }

    private function updateFillGaps($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();
        if (Games::FILL_IN_THE_BLANKS_TYPE == $userCourseChapter->getChapter()->getType()->getId()) {
            $fillGaps = $this->em->getRepository(FillGaps::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);

            $holes = 0;
            $timeTotal = 0;
            foreach ($fillGaps as $fill) {
                $timeTotal += $fill->getTime();
                $fillAnswers = json_decode($fill->getAnswers());
                foreach ($fillAnswers as $fillAnswer) {
                    ++$holes;
                }
            }

            if (isset($data['data'])) {
                $data['data'] = array_merge($data['data'], ['holes' => $holes, 'timeTotal' => $timeTotal]);
                $userCourseChapter->setData(array_merge($previous, $data['data']));
            }
        }
    }

    private function updateHighLower($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();
        if (Games::HIGHER_OR_LOWER_TYPE == $userCourseChapter->getChapter()->getType()->getId()) {
            $highLower = $this->em->getRepository(HigherLower::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);

            $words = 0;
            $timeTotal = 0;
            foreach ($highLower as $high) {
                $timeTotal += $high->getTime();
                foreach ($high->getHigherLowerWords() as $word) {
                    ++$words;
                }
            }

            if (isset($data['data'])) {
                $data['data'] = array_merge($data['data'], ['words' => $words, 'timeTotal' => $timeTotal]);
                $userCourseChapter->setData(array_merge($previous, $data['data']));
            }
        }
    }

    private function updateCategorize($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();
        if (Games::WHERE_DOES_IT_FIT_TYPE == $userCourseChapter->getChapter()->getType()->getId()) {
            $categories = $this->em->getRepository(Categorize::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
            $totalQuestions = count($categories) ?? 0;
            $timeTotal = 0;
            foreach ($categories as $category) {
                $timeTotal += $category->getTime();
            }

            if (isset($data['data'])) {
                $data['data'] = array_merge($data['data'], ['totalQuestions' => $totalQuestions, 'timeTotal' => $timeTotal]);
                $userCourseChapter->setData(array_merge($previous, $data['data']));
            }
        }
    }

    private function updateTrueOrFalse($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();
        if (Games::TRUE_OR_FALSE_TYPE == $userCourseChapter->getChapter()->getType()->getId()) {
            $questions = $this->em->getRepository(TrueOrFalse::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
            $totalQuestions = count($questions) ?? 0;
            $timeTotal = 0;
            foreach ($questions as $question) {
                $timeTotal += $question->getTime();
            }

            if (isset($data['data'])) {
                $data['data'] = array_merge($data['data'], ['totalQuestions' => $totalQuestions, 'timeTotal' => $timeTotal]);
                $userCourseChapter->setData(array_merge($previous, $data['data']));
            }
        }
    }

    private function updatePairs($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();
        if (Games::MATCH_TYPE == $userCourseChapter->getChapter()->getType()->getId()) {
            $questions = $this->em->getRepository(Parejas::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
            $timeGame = $this->em->getRepository(TimeGame::class)->findOneBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
            if (\is_null($timeGame)) {
                $tempTime = 0;
                foreach ($questions as $question) {
                    $tempTime += $question->getTiempo();
                }
                $timeGame = $tempTime / \count($questions);
            } else {
                $timeGame = $timeGame->getTime();
            }
            $totalQuestions = count($questions) ?? 0;

            if (isset($data['data'])) {
                $data['data'] = array_merge($data['data'], ['totalQuestions' => $totalQuestions, 'timeTotal' => $timeGame]);
                $userCourseChapter->setData(array_merge($previous, $data['data']));
            }
        }
    }

    private function updateGuessImagen($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();
        if (Games::RIDDLE_TYPE == $userCourseChapter->getChapter()->getType()->getId()) {
            $questions = $this->em->getRepository(AdivinaImagen::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
            $totalQuestions = count($questions) ?? 0;
            $timeTotal = 0;
            foreach ($questions as $question) {
                $timeTotal += $question->getTime();
            }

            if (isset($data['data'])) {
                $data['data'] = array_merge($data['data'], ['totalQuestions' => $totalQuestions, 'timeTotal' => $timeTotal]);
                $userCourseChapter->setData(array_merge($previous, $data['data']));
            }
        }
    }

    private function updateOrderLetters($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();
        if (Games::SORT_LETTERS_TYPE == $userCourseChapter->getChapter()->getType()->getId()) {
            $questions = $this->em->getRepository(OrdenarMenormayor::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
            $totalQuestions = count($questions) ?? 0;
            $timeTotal = 0;
            foreach ($questions as $question) {
                $timeTotal += $question->getTime();
            }

            if (isset($data['data'])) {
                $data['data'] = array_merge($data['data'], ['totalQuestions' => $totalQuestions, 'timeTotal' => $timeTotal]);
                $userCourseChapter->setData(array_merge($previous, $data['data']));
            }
        }
    }

    private function updateEnigma($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();
        if (Games::ENIGMA_TYPE == $userCourseChapter->getChapter()->getType()->getId()) {
            $questions = $this->em->getRepository(OrdenarMenormayor::class)->findBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
            $totalQuestions = count($questions) ?? 0;
            $timeTotal = 0;
            foreach ($questions as $question) {
                $timeTotal += $question->getTime();
            }

            if (isset($data['data'])) {
                $data['data'] = array_merge($data['data'], ['totalQuestions' => $totalQuestions, 'timeTotal' => $timeTotal]);
                $userCourseChapter->setData(array_merge($previous, $data['data']));
            }
        }
    }

    private function updateChapterScorm($data, UserCourseChapter $userCourseChapter): void
    {
        $previous = $userCourseChapter->getData();
        if (isset($data['param'])) {
            $previous['scorm'][$data['param']] = $data['value'];
        }
        $userCourseChapter->setData($previous);
    }

    private function endOfChapter($data, UserCourseChapter $userCourseChapter)
    {
        $isGame = $userCourseChapter->getChapter()->getType()->isGame();
        $isScorm = ChapterContent::SCORM_TYPE === $userCourseChapter->getChapter()->getType()->getId();
        $finished = $data['finished'] ?? ($data['data']['finished'] ?? false);
        $params = isset($data['param'], $data['value']);

        if ($finished && $isGame) {
            return $this->previosStepsToCompleteTheGame($userCourseChapter);
        }
        if ($finished) {
            $this->setFinishChapter($userCourseChapter);
        }
        if ($isScorm && $params) {
            $this->setFinishChapterTypeScorm($data, $userCourseChapter);
        }

        $this->em->persist($userCourseChapter);

        return 'Chapter correctly updated ';
    }

    private function previosStepsToCompleteTheGame(UserCourseChapter $userCourseChapter)
    {
        $message = $this->getMessageFinishGame($userCourseChapter);
        $this->setFinishChapterTypeGame($userCourseChapter);
        $this->updateAttempsToTryGamesAgain($userCourseChapter);

        return $message;
    }

    private function setFinishChapterTypeGame(UserCourseChapter $userCourseChapter)
    {
        $score = $this->userCourseService->calculatePoints($userCourseChapter);

        if ($score) {
            $this->setPointUserChapter($userCourseChapter);

            return $this->setFinishChapter($userCourseChapter);
        }
    }

    private function setPointUserChapter(UserCourseChapter $userCourseChapter)
    {
        $score = $this->userCourseService->calculatePoints($userCourseChapter);

        $userCourseChapter->setPoints($score);
    }

    private function setFinishChapterTypeScorm($data, UserCourseChapter $userCourseChapter)
    {
        $param = $data['param'] ?? null;
        $value = $data['value'] ?? null;

        $dataScorm= [];
        $resetFlag = false;
        $checkScoreFlag = false;
        $scoreRawAchieved = false;
        $rawScore = Scorm::SCORM_SCORE_RAW_DEFAULT_VALUE;

        if (isset($userCourseChapter)){
            $chapter = $userCourseChapter->getChapter();
            $scormRepository = $this->em->getRepository(Scorm::class);
            $scorm = $scormRepository->findOneBy([
                'chapter' => $chapter,
            ]);

            $dataScore = $scorm->getRawScore();
            if (isset($dataScore) && (\intval($dataScore) >= 0) && (\intval($dataScore) <= 100) ){
                $rawScore = $dataScore;
                $checkScoreFlag = true;
            }
            
            if (!$checkScoreFlag && ($param == Scorm::SCORM_SCORE_RAW) ){
                $rawScore = $value;
            }
    
            $validParams = [
                Scorm::SCORM_STATUS => [Scorm::SCORM_STATUS_COMPLETE, Scorm::SCORM_STATUS_COMPLETED, Scorm::SCORM_STATUS_PASSED],
                Scorm::SCORM_COMPLETION => [Scorm::SCORM_STATUS_COMPLETED],
                Scorm::SCORM_CORE_COMPLETION => [ Scorm::SCORM_CORE_COMPLETION_DEFAULT_VALUE],
                Scorm::SCORM_SCORE_RAW => [ Scorm::SCORM_SCORE_RAW_DEFAULT_VALUE, $rawScore],
            ];
           
            if ($param && $value && isset($validParams[$param]))  {
                if ($checkScoreFlag){
                    $scoreRawAchieved = (($param == Scorm::SCORM_SCORE_RAW) && ($value >= $rawScore)) ? 1 : 0;
                }           
                if (in_array($value, $validParams[$param]) || $scoreRawAchieved){
                    $this->setFinishChapter($userCourseChapter);                
                }    
            }
        }
    }

    private function setFinishChapter(UserCourseChapter $userCourseChapter): void
    {
        $userCourse = $userCourseChapter->getUserCourse();

        if (\is_null($userCourseChapter->getFinishedAt())) {
            //$this->logger->error("setFinishChapter: " . $userCourseChapter->getChapter()->getId() );
            $userCourseChapter->setFinishedAt(new \DateTime());
            $this->userCourseService->setFinishUserCourse($userCourse);
        }
    }

    private function updateAttempsToTryGamesAgain(UserCourseChapter $userCourseChapter): void
    {
        $isVideoQuiz = Games::VIDEO_QUIZ_TYPE == $userCourseChapter->getChapter()->getType()->getId();
        $previous = $userCourseChapter->getData();

        $previous['attempts'] = $previous['attempts'] ?? [];
        $previous['attempts'][] = $previous['answers'];
        $previous['answers'] = [];

        unset($previous['sections']);
        if ($isVideoQuiz) {
            unset($previous['answers']);
        }

        $userCourseChapter->setData($previous);
    }

    private function getMessageFinishGame(UserCourseChapter $userCourseChapter)
    {
        $score = $this->userCourseService->calculatePoints($userCourseChapter);

        return $score > 0 ? $this->getMessageWhenGameHasExceeded($userCourseChapter)
            : $this->getMessageWhenGameHasNoPoints($userCourseChapter);
    }

    private function getMessageWhenGameHasExceeded(UserCourseChapter $userCourseChapter)
    {
        return \is_null($userCourseChapter->getFinishedAt()) ?
            $this->checkChapterGame($userCourseChapter)
            : ['description' => $this->descriptionGameFinish($userCourseChapter)];
    }

    private function getMessageWhenGameHasNoPoints(UserCourseChapter $userCourseChapter)
    {
        return [
            'description' => $this->descriptionGameFinish($userCourseChapter),
            'isCheck' => false,
        ];
    }

    private function descriptionGameFinish(UserCourseChapter $userCourseChapter)
    {
        $score = $this->userCourseService->calculatePoints($userCourseChapter) > 0 ? $this->userCourseService->calculatePoints($userCourseChapter) : null;
        $userCourse = $userCourseChapter->getUserCourse();

        return $this->translator->trans($userCourseChapter->getChapter()->getType()->getFinishDescriptionLabel($score), [], 'chapters', $userCourse->getUser()->getLocaleCampus());
    }

    private function checkChapterGame(UserCourseChapter $userCourseChapter)
    {
        return [
            'description' => $this->descriptionGameFinish($userCourseChapter),
            'isCheck' => true,
        ];
    }

    private function hasAnnouncement(UserCourse $userCourse)
    {
        return null !== $userCourse->getAnnouncement();
    }

    private function finishAnnouncementUser(UserCourse $userCourse)
    {
        $announcement = $userCourse->getAnnouncement();
        $announcementUser = $this->em->getRepository(AnnouncementUser::class)->findOneBy([
            'announcement' => $announcement,
            'user' => $this->getUser(),
        ]);

        $hasSurvey = $this->announcementConfigurationsService->hasSurvey($announcement);
        if (!$announcement->isIsConfirmationRequiredDiploma() && !$hasSurvey) {
            $this->announcementUserService->setAprovedAnnouncementUser($announcementUser);
        }
    }
}
