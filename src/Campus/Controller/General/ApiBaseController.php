<?php

namespace App\Campus\Controller\General;

use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\BrowserAndDeviceLoginTrait;
use App\Entity\ChatChannel;
use App\Entity\ChatServer;
use App\Entity\Course;
use App\Entity\UrlShortener;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserLogin;
use App\Entity\UserToken;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\UserRepository;
use App\Repository\UserTokenRepository;
use App\Saml\Saml2Service;
use App\Security\ApiLoginAuthenticator;
use App\Service\User\Authentication\LdapAuthService;
use App\Service\User\Token\RefreshTokenService;
use App\Service\User\Authentication\SamlService;
use App\Repository\ChallengeRepository;
use App\Service\Api\FormationUserService;
use App\Service\SettingsService;
use App\Service\User\Authentication\StarTeam;
use App\Service\User\Token\TokenLoginService;
use App\Service\User\General\UserLevelService;
use DateTimeZone;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use Gesdinet\JWTRefreshTokenBundle\Model\RefreshTokenManagerInterface;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Swagger\Annotations as SWG;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;
use Doctrine\Common\Annotations\AnnotationReader;
use Doctrine\ORM\EntityManager;
use Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory;
use Symfony\Component\Serializer\Mapping\Loader\AnnotationLoader;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;


/**
 * Class ApiController
 * @package App\Controller
 *
 */
class ApiBaseController extends AbstractFOSRestController
{
	use SerializerTrait;
	use BrowserAndDeviceLoginTrait;

	protected LoggerInterface        $logger;
	protected UserRepository         $userRepository;
	protected CourseRepository       $courseRepository;
	protected AnnouncementRepository $announcementRepository;
	protected TranslatorInterface    $translator;
	protected SettingsService        $settings;

	/**
	 * ApiController constructor.
	 *
	 * @param LoggerInterface $logger
	 * @param UserRepository $userRepository
	 * @param CourseRepository $courseRepository
	 * @param AnnouncementRepository $announcementRepository
	 * @param TranslatorInterface $translator
	 */
	public function __construct(
		LoggerInterface        $logger,
		UserRepository         $userRepository,
		CourseRepository       $courseRepository,
		AnnouncementRepository $announcementRepository,
		TranslatorInterface    $translator,
		SettingsService $settingsService
	) {
		$this->logger = $logger;
		$this->userRepository = $userRepository;
		$this->courseRepository = $courseRepository;
		$this->announcementRepository = $announcementRepository;
		$this->translator = $translator;
		$this->settings = $settingsService;
	}


	/**
	 * @Rest\Post("/login-deprecated", name="api_login_double")
	 *
	 * @SWG\Response(
	 *     response=200,
	 *     description="User was logged in successfully"
	 * )
	 *
	 * @SWG\Response(
	 *     response=401,
	 *     description="User was not alloweb to access"
	 * )
	 *
	 * @SWG\Response(
	 *     response=500,
	 *     description="User was not logged in successfully"
	 * )
	 *
	 * @SWG\Parameter(
	 *     name="token",
	 *     in="body",
	 *     @SWG\Schema(
	 *             type="object",
	 *     ),
	 *     description="The Field is used to SSO"
	 * )
	 *
	 * @SWG\Tag(name="User")
     * @deprecated Authentication implemented in ApiLoginAuthenticator
     * @see ApiLoginAuthenticator
	 *
	 */
	public function login(
		Request                     $request,
		TokenLoginService           $tokenLoginService,
		JWTManager                  $JWT,
		UserPasswordHasherInterface $passwordEncoder,
		EntityManagerInterface      $em,
		TokenStorageInterface       $tokenStorage,
		RequestStack                $requestStack,
		LdapAuthService             $ldapAuthService,
		RefreshTokenService $refreshTokenService
	): Response {
		/** @var User|null $user */
		$user = null;
		$data = json_decode($request->getContent(), true);
		try {
			if (!empty($data['token'])) {
				if (filter_var($this->settings->get('saml.enabled'), FILTER_VALIDATE_BOOLEAN)) {
					// Login is coming from saml authentication and considered as successful
					$user = $this->handleSaml2LoginProcedure($em, $data['token']);
				} elseif ($this->settings->get('sso.saml')) {
					// Maintain support for previous implementation sso.saml
					$user = $this->handleSsoSamlLoginProcedure($data['token']);
				} else {
					$user = $this->handleTokenLoginService($tokenLoginService, $data['token']);
				}
				if ($user instanceof Response) return $user;
			} elseif (!empty($data['email'])) {
				// Check if ldap is enabled
				if ($this->settings->get('ldap.enabled')) {
					$user = $this->handleLdapLogin($ldapAuthService, $data['email'], $data['password']);
				} else {
					// Normal login procedure
					$user = $this->handleNormalLoginProcedure($passwordEncoder, $data['email'], $data['password']);
				}
				if ($user instanceof Response) return $user;
			} else {
				return $this->sendResponse(
					[
						'status' => 200,
						'error'  => true,
						'data'   => 'No email or token has been provided'
					]
				);
			}

			if ($user) {
				$payload = []; // Used as payload for the JWT
				if (is_array($user)) {
					$payload = $user['payload'];
					$user = $user['user'];
				}

				$token = new UsernamePasswordToken($user, null, 'main', $user->getRoles());
				$tokenStorage->setToken($token);
				$requestStack->getSession()->set('_security_main', serialize($token));

				if (!empty($payload)) {
					// Add additional payload to the token
					$jwtToken = $JWT->createFromPayload($user, $payload);
				} else {
					$jwtToken = $JWT->create($user);
				}

				$refreshToken = $refreshTokenService->createForUserWithTtl($user, 7200, $payload);
				$em->persist($refreshToken);
				$em->flush();

				$this->saveLogin($user, $em, $data);
				if ($this->settings->get('app.use_itinerary')) {
					//Something here... I don't know
				}
				return $this->sendResponse(
					[
						'status' => 200,
						'error'  => false,
						'data'   => [
							'user'  => $user,
							'token' => $jwtToken,
							'level' => $this->settings->get('ranking.useLevel'),
							'refreshToken' => $refreshToken->getRefreshToken()
						]
					],
					[
						'groups' => array('user_area')
					]
				);
			} else {
				return $this->sendResponse(
					[
						'status' => 200,
						'error'  => true,
						'data'   => 'Unauthorized'
					]
				);
			}
		} catch (\Exception $e) {
			return $this->sendResponse([
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => true,
				'data'   => $this->translator->trans(
					'message_api.base.error_login',
					[],
					'message_api',
					$user ? $user->getLocale() : $this->settings->get('app.defaultLanguage')
				) . "{" . $e->getMessage() . "}"
			]);
		}
	}

	/**
	 * @param TokenLoginService $tokenLoginService
	 * @param string $token
	 * @return User|object|Response
	 */
	private function handleTokenLoginService(TokenLoginService $tokenLoginService, string $token)
	{
		$user = $tokenLoginService->checkToken($token);
		if (!$user) {
			return $this->sendResponse(
				[
					'status' => 200,
					'error'  => true,
					'data'   => $this->translator->trans('message_api.base.authorized_token_fail', [], 'message_api', $this->settings->get('app.defaultLanguage'))
				]
			);
		}
		return $user;
	}

	/**
	 * Backward compatibility with previous implementation of Saml Login
	 * @return Response|User
	 * @deprecated  Migrate to Saml2Service
	 * @see Saml2Service
	 */
	private function handleSsoSamlLoginProcedure(string $token)
	{
		$token_data = explode('_', $token, 2);

		$user = $this->userRepository->find($token_data[0]);
		if (!$user || $user->getAutologinHash() != $token_data[1]) {
			// Failed to Log In
			return $this->sendResponse(
				[
					'status' => 200,
					'error'  => true,
					'data'   => $this->translator->trans(
						'message_api.base.authorized_access',
						[],
						'message_api',
						$user ? $user->getLocale() : $this->settings->get('app.defaultLanguage')
					)
				]
			);
		}
		return $user;
	}

	/**
	 * Authentication using saml
	 * @param EntityManagerInterface $em
	 * @param string $token
	 * @return Response|User
	 */
	private function handleSaml2LoginProcedure(EntityManagerInterface $em, string $token)
	{
		$userToken = $em->getRepository(UserToken::class)->findOneBy([
			'token' => $token,
			'type'  => UserToken::TYPE_SAML2_AUTH
		]);
		if (!$userToken || !$userToken->getIsValid()) {
			return $this->sendResponse(
				[
					'status' => 401,
					'error'  => true,
					'data'   => 'User token is invalid or does not exists',
				]
			);
		}

		$user = $userToken->getUser();

		$userToken->setUsed(true)
			->setUsedAt(new \DateTimeImmutable());
		$em->flush($userToken);
		return $user;
	}

	/**
	 * Handle authentication related to ldap|ldaps
	 * @param LdapAuthService $ldapAuthService
	 * @param $uid
	 * @param $password
	 * @return User|Response
	 */
	private function handleLdapLogin(LdapAuthService $ldapAuthService, $uid, $password)
	{
		$user = $ldapAuthService->login($uid, $password);
		if (!$user) {
			return $this->sendResponse([
				'status' => 200,
				'error'  => true,
				'data'   => $this->translator->trans(
					'message_api.base.user_not_exists_in_ad',
					[],
					'message_api',
					$this->settings->get('app.defaultLanguage')
				)
			]);
		}
		return $user;
	}

	/**
	 * Authentication against user stored in database
	 * @param UserPasswordHasherInterface $userPasswordHasher
	 * @param string $email
	 * @param string $password
	 * @return User|Response
	 */
	private function handleNormalLoginProcedure(UserPasswordHasherInterface $userPasswordHasher, string $email, string $password)
	{
		$user = $this->userRepository->findOneBy(['email' => $email]);
		if (!$user || !$user->getIsActive() || !$user->getValidated() || !$userPasswordHasher->isPasswordValid($user, $password)) {
			// If one of the conditions fail, not authorized
			return $this->sendResponse(
				[
					'status' => 200,
					'error'  => true,
					'data'   => $this->translator->trans(
						'message_api.base.credentials_no_correct',
						[],
						'message_api',
						$user ? $user->getLocale() : $this->settings->get('app.defaultLanguage')
					)
				]
			);
		}

        /**
         * Check if API Login validations are enabled
         */
        if ($this->settings->get('app.api_login.enabled') === true)
        {
            // Call extra steps after login to validate if user is still active
        }

		return $user;
	}


	/**
	 * @Rest\Get("/logout", name="api_logout")
	 *
	 * @SWG\Response(
	 *     response=200,
	 *     description="User successfully logout"
	 * )
	 *
	 * @SWG\Response(
	 *     response=401,
	 *     description="User was not alloweb to access"
	 * )
	 *
	 * @SWG\Response(
	 *     response=500,
	 *     description="User was not logged in successfully"
	 * )
	 *
	 *
	 * @SWG\Tag(name="User")
	 *
	 */
	public function logout(
		Request               $request,
		TokenStorageInterface $tokenStorage,
		UserTokenRepository   $userTokenRepository
	): Response {
		try {

			/** @var UserToken|null $userToken */
			$userToken = null;
			if ($this->settings->get('saml.enabled')) {
				// Do Database query only if saml is enabled
				$user = $this->getUser();

				/** @var UserToken|null $userToken */
				$userToken = $userTokenRepository->createQueryBuilder('ut')
					->select('ut')
					->andWhere('ut.user = :user and ut.used = 1')
					->setParameter('user', $user)
					->orderBy('ut.usedAt', 'DESC')
					->setMaxResults(1)
					->getQuery()
					->getResult();
				if (is_array($userToken) && count($userToken) > 0)
					$userToken = $userToken[0];
			}

			$tokenStorage->setToken();
			$request->getSession()->invalidate();
		} catch (\Exception $e) {
			return $this->sendResponse([
				'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
				'error'  => true,
				'data'   => "An error has occurred trying to login the user - Error: {" . $e->getMessage() . "}"
			]);
		}

		return $this->sendResponse([
			'status' => Response::HTTP_OK,
			'error'  => false,
			'data'   => "User successfully logout"
		]);
	}


	/**
	 * @param $course Course
	 * @return bool
	 */
	protected function checkCourseAccess(Course $course)
	{
		if (!$course->getActive() && !$this->isGranted('ROLE_ADMIN') && !$this->isGranted('ROLE_MANAGER')) {
			//If course is a translation, check if the main course is active
			if (!($course->getTranslation() && $course->getTranslation()->getActive())) {
				$this->logger->error('Without access');
				return false;
			}
		}

		$user = $this->userRepository->findOneBy(array('email' => $this->getUser()->getUsername()));
		if (
			!$this->checkAccessByParansOpenAndCourseOpenAndUserOpen($course, $user)
			&& !$this->isGranted('ROLE_ADMIN')
			&& !$this->isGranted('ROLE_MANAGER')
			&& (is_null($course->getTranslation())
				|| (!is_null($course->getTranslation())
					&& !is_null($user->getExtra())
//					&& is_null($this->courseRepository->checkAccessByProfesssionalCategory($course->getTranslation(), $user->getExtra()->getCategory()))
				)
			)
			&& !$course->checkUserFilterAccess($user)
		) {
			$this->logger->error($this->checkAccessByParansOpenAndCourseOpenAndUserOpen($course, $user));
			$this->logger->error($course->getTranslation());
			return false;
		} else {
			return true;
		}
	}

	private function checkAccessByParansOpenAndCourseOpenAndUserOpen(Course $course, $user)
	{

		if ($this->settings->get('app.openCourse') && $course->getOpen() && $course->getOpenVisible() && $user->getOpen()) {
			return true;
		}



		if (!is_null($this->announcementRepository->findAnnouncementUserNotified($course, $user))) {
			return true;

		}

		//Check itinerary
		if ($this->settings->get('app.use_itinerary') && $this->courseRepository->checkItineraryAccess($course, $user)) {
			return true;
		}

		return false;
	}


	private function saveLogin(User $user, EntityManagerInterface $em, $data = null)
	{
		$userLoginRepository = $em->getRepository(UserLogin::class);
		$count = $userLoginRepository->countByUserFromDate($user, new \DateTime('-2 hour'));
		if (!$count) {
			$timezone = isset($data['timezone']) ? $data['timezone'] : $this->settings->get('app.default_timezone');
			$now = new \DateTime('now', new \DateTimeZone($timezone));

			$dateTimeZone = new DateTimeZone($timezone);
			$country = $dateTimeZone->getLocation()['country_code'];

			$userLogin = new UserLogin();
			$userLogin->setUser($user)
				->setCreatedAt(new \DateTime());
			$userLogin->setDevice($this->detectDevice());
			$userLogin->setBrowser($this->detectBrowser());
			$userLogin->setPlatform($this->detectPlatform());
			$userLogin->setTimezone($timezone);
			$userLogin->setCountry($country);
			$userLogin->setTimezoneCreatedAt($now);
			$userLogin->setTimezoneUpdatedAt($now);

			$em->persist($userLogin);
			$em->flush();

			return $userLogin;
		}

		return false;
	}

	/**
	 * @Route("/profile", name="profile-user",methods={"GET"})
	 * @return Response
	 */
	public function profileUser(EntityManagerInterface $em, FormationUserService $formationUserService)
	{
		try {

			$userToken = $em->getRepository(UserToken::class)->findOneBy([
				'user' => $this->getUser(),
				'type' => UserToken::TYPE_SAML2_AUTH
			]);

			$generalServer = $em->getRepository(ChatServer::class)->getServer(ChatServer::TYPE_GENERAL, 0, true);
			$forumChannel = $em->getRepository(ChatChannel::class)->getChannelByType($generalServer, ChatChannel::TYPE_FORUM, true);

            $samlLogoutUrl = $this->settings->get('saml.logout_url');
            $samlEnabled = $this->settings->get('saml.enabled');


			$user = [
				'id'         => $this->getUser()->getId(),
				'email'      => $this->getUser()->getEmail(),
				'firstName'   => $this->getUser()->getFirstName(),
				'lastName'   => $this->getUser()->getLastName(),
				'extra'      => [],
				'points'     => $this->getUser()->getPoints(),
				'avatar'     => $this->getUser()->getAvatar(),
				'locale'     => $this->getUser()->getLocaleCampus(),
				'dataAvatar' => $this->getUser()->getDataAvatar(),
				'fullName'   => $this->getUser()->getFullName(),
				'policies'   =>  $this->settings->get('app.userPolicies') ? $this->getUser()->getPolicies() : false,
				"finishedCourses" => (int) $em->getRepository(UserCourse::class)->finishedCourseByUser($this->getUser()),
				"timeSpent" => $this->getUser()->getTimeSpent(),
				"trainingPage" => false,
				'sso'        => (bool)$userToken,
				'isAdmin' => $this->getUser()->isAdmin(),
				'isManager' => $this->getUser()->isManager(),
				'isManagerEditor' => $this->getUser()->isManagerEditor(),
				'forumChannel' => $forumChannel ? $forumChannel->getId() : null,
                'ssoLogout' => $samlEnabled && !empty($samlLogoutUrl)
			];


			$response = [
				'status' => 200,
				'error'  => false,
				'data'   => $user
			];
		} catch (\Exception $e) {
			$response = [
				'status' => 500,
				'error'  => true,
				'data'   => $e->getMessage()
			];
		}

		return $this->sendResponse($response, array('groups' => array('user_area')));
	}



	/**
	 * @Rest\Get("/api/url-shortener/long-url")
	 * @param EntityManagerInterface $em
	 * @param Request $request
	 * @return Response
	 */
	public function readSharedUrl(EntityManagerInterface $em, Request $request)
	{
		$shortUrl = $request->get('share');
		if (empty($shortUrl)) return $this->sendResponse([
			'status' => Response::HTTP_ACCEPTED,
			'error' => true,
			'data' => 'URL not found'
		]);
		$urlShortenerRepository = $em->getRepository(UrlShortener::class);
		$longUrl = $urlShortenerRepository->getLongUrl($shortUrl);
		return $this->sendResponse([
			'status' => empty($longUrl) ? Response::HTTP_ACCEPTED : Response::HTTP_OK,
			'error' => empty($longUrl),
			'data' => $longUrl ?: 'URL not found'
		]);
	}
}
