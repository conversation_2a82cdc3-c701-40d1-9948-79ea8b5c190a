<?php

namespace App\Resources\Traits\Catalog;

use App\Entity\TypeMoney;
use App\Entity\TypeMoneyTranslation;
use App\Resources\DataFixtureBase\Announcement\TypeMoneyData;

trait TypeMoneyTrait
{
    protected function getDefaultData(): array
    {
        $parameters = self::PARAMETERS_BASE;
        $parameters['fieldsToTranslate'] = ['name', 'country'];
        $parameters['setFields'] = [
            'name' => 'setName', 'country' => 'setCountry', 'symbol' => 'setSymbol',
            'fractional_unit' => 'setFractionalUnit',
            'code_iso' => 'setCodeIso'
        ];
        $parameters['setFieldsToTranslations'] = ['name' => 'setName', 'country' => 'setCountry'];
        $parameters['fieldState'] = ['state' => 'setState'];

        return [
            'data' => TypeMoneyData::DEFAULT_DATA,
            'parameters' => $parameters,
            'baseEntity' => TypeMoney::class,
            'translationEntity' => TypeMoneyTranslation::class,
        ];
    }
}