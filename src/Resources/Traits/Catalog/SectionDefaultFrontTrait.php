<?php

namespace App\Resources\Traits\Catalog;

use App\Entity\SectionDefaultFront;
use App\Entity\SectionDefaultFrontTranslation;
use App\Resources\DataFixtureBase\General\SectionDefaultFrontData;

trait SectionDefaultFrontTrait
{
    protected function getDefaultData(): array
    {
        $parameters = self::PARAMETERS_BASE;
        $parameters['fieldState'] = null;
        $parameters['fieldExtras'] = [
            'isActive' => 'setIsActive',
            'idSection' => 'setIdSection',
            'isOpenCourse' => 'setIsOpenCourse',
        ];

        return [
            'data' => SectionDefaultFrontData::DEFAULT_DATA,
            'parameters' => $parameters,
            'baseEntity' => SectionDefaultFront::class,
            'translationEntity' => SectionDefaultFrontTranslation::class,
        ];
    }
}