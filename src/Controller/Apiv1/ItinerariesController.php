<?php

declare(strict_types=1);

namespace App\Controller\Apiv1;

use App\Entity\Itinerary;
use App\Entity\ItineraryCourse;
use App\Exception\ExcededApiRequestsException;
use App\Exception\InvalidApiKeyException;
use App\Service\General\FilterService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/api/v1")
 */
class ItinerariesController extends ApiBaseController
{
    /**
     * @Route("/itineraries", methods={"GET"})
     *
     * @throws \Exception
     */
    public function __invoke(FilterService $filterService): Response
    {
        $apiKey = $this->requestStack->getCurrentRequest()->headers->get(self::API_KEY_FIELD);

        try {
            $this->checkAccess($apiKey);
        } catch (InvalidApiKeyException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'data' => ['error' => 'X-API-KEY header is missing or invalid.'],
            ]);
        } catch (ExcededApiRequestsException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_TOO_MANY_REQUESTS,
                'data' => ['error' => 'Too many requests.'],
            ]);
        }

        $this->saveRequest($apiKey, 'itineraries');

        $itinerariesRepository = $this->entityManager->getRepository(Itinerary::class);
        $itineraries = $itinerariesRepository->findBy([], ['name' => 'ASC']);

        $data = [];
        foreach ($itineraries as $itinerary) {
            /* @var Itinerary $itinerary */
            $data[] = [
                'id' => $itinerary->getId(),
                'name' => $itinerary->getName(),
                'description' => $itinerary->getDescription(),
                'createdAt' => $itinerary->getCreatedAt() ? $itinerary->getCreatedAt()->format('Y-m-d H:i:s') : null,
                'updatedAt' => $itinerary->getUpdatedAt() ? $itinerary->getUpdatedAt()->format('Y-m-d H:i:s') : null,
                'active' => $itinerary->isActive(),
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $data,
        ]);
    }

    private function getCourses(Itinerary $itinerary): array
    {
        $courses = [];
        foreach ($itinerary->getItineraryCourses() as $itineraryCourse) {
            /** @var ItineraryCourse $itineraryCourse */
            $course = $itineraryCourse->getCourse();
            $courses[] = [
                'id' => $course->getId(),
                'name' => $course->getName(),
                'type' => $course->getTypeCourse()->getName(),
                'categoryId' => $course->getCategory()->getId(),
                'categoryName' => $course->getCategory()->getName(),
            ];
        }

        return $courses;
    }
}
