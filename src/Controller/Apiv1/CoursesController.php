<?php


namespace App\Controller\Apiv1;

use App\Entity\Course;
use App\Entity\TypeCourse;
use App\Entity\UserCourseChapter;
use App\Exception\ExcededApiRequestsException;
use App\Exception\InvalidApiKeyException;
use App\Exception\InvalidDateFormatException;
use App\Exception\InvalidDateRangeException;
use Exception;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;


/**
 * @Route("/api/v1")
 */
class CoursesController extends ApiBaseController
{

    /**
     * @Route("/courses", methods={"POST"})
     * @return Response
     * @throws Exception
     */
    public function __invoke (): Response
    {
        $apiKey = $this->requestStack->getCurrentRequest()->headers->get(self::API_KEY_FIELD);

        try
        {
            $this->checkAccess($apiKey);
        }
        catch (InvalidApiKeyException $e)
        {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'data'   => ['error' => 'X-API-KEY header is missing or invalid.'],
            ]);
        }
        catch (ExcededApiRequestsException $e)
        {
            return $this->sendResponse([
                'status' => Response::HTTP_TOO_MANY_REQUESTS,
                'data'   => ['error' => 'Too many requests.'],
            ]);
        }

        $requestContent = json_decode($this->requestStack->getCurrentRequest()->getContent(), true);

        $from = $requestContent['date_from'] ?? '';
        $to   = $requestContent['date_to'] ?? '';

        try
        {
            $this->checkDates($from, $to);
        }
        catch (InvalidDateFormatException $e)
        {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'data'   => ['error' => 'The date format is not valid.'],
            ]);
        }
        catch (InvalidDateRangeException $e)
        {
            return $this->sendResponse([
                'status' => Response::HTTP_REQUESTED_RANGE_NOT_SATISFIABLE,
                'data'   => ['error' => 'The date range is not valid.'],
            ]);
        }


        $this->saveRequest($apiKey, 'courses');

        $courseRepository = $this->entityManager->getRepository(Course::class);
        $courses          = $courseRepository->createQueryBuilder('course')
            ->addOrderBy('course.name', 'ASC')
            ->leftJoin('course.chapters', 'chapter')
            ->andWhere('course.updatedAt BETWEEN :from AND :to')
            ->orWhere('chapter.updatedAt BETWEEN :from AND :to')
            ->setParameter('from', $from)
            ->setParameter('to', $to)
            ->getQuery()
            ->getResult();

        $data = [];
        foreach ($courses as $course)
        {
            /** @var Course $course */

            $data[] = [
                'id'                 => $course->getId(),
                'code'               => $course->getCode(),
                'name'               => $course->getName(),
                'description'        => $course->getDescription(),
                'generalInformation' => $course->getGeneralInformation(),
                'open'               => $course->getOpen(),
                'openCampus'         => $course->getOpenVisible(),
                'active'             => $course->getActive(),
                'isNew'              => $course->getIsNew(),
                'language'           => $course->getLocale(),
                'translationId'      => $course->getTranslation() ? $course->getTranslation()->getId() : null,
                'categoryId'         => $course->getCategory() ? $course->getCategory()->getId() : null,
                'categoryName'       => $course->getCategory() ? $course->getCategory()->getName() : null,
                'courseType'         => $course->getTypeCourse() ? TypeCourse::TYPES[$course->getTypeCourse()->getId()] : null,
                'chapters'           => $this->getChapters($course),
                'announcements'      => $this->getAnnouncements($course),
                'itineraries'        => $this->getItineraries($course),
            ];
        }


        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data'   => $data,
        ]);
    }


    private function getChapters (Course $course): array
    {
        $chapters = [];

        foreach ($course->getChapters() as $chapter)
        {
            $chapters[] = [
                'id'          => $chapter->getId(),
                'name'        => $chapter->getTitle(),
                'description' => $chapter->getDescription(),
                'typeId'      => $chapter->getType()->getId(),
                'typeName'    => $chapter->getType()->getName(),
                'order'       => $chapter->getPosition(),
                'seasonId'    => $chapter->getSeason()->getId(),
                'seasonName'  => $chapter->getSeason()->getName(),
                'seasonOrder' => $chapter->getSeason()->getSort(),
            ];
        }

        return $chapters;
    }


    private function getAnnouncements (Course $course): array
    {
        $announcements = [];

        foreach ($course->getAnnouncements() as $announcement)
        {
            $announcements[] = [
                'id'                   => $announcement->getId(),
                'code'                 => $announcement->getCode(),
                'startAt'              => $announcement->getStartAt() ? $announcement->getStartAt()->format('Y-m-d H:i:s') : null,
                'finishAt'             => $announcement->getFinishAt() ? $announcement->getFinishAt()->format('Y-m-d H:i:s') : null,
                'notifiedAt'           => $announcement->getNotifiedAt() ? $announcement->getNotifiedAt()->format('Y-m-d H:i:s') : null,
                'status'               => $announcement->getStatus(),
                'maxUsers'             => $announcement->getMaxUsers(), // 'max_users' => $announcement->getMaxUsers() ?? '
                'usersPerGroup'        => $announcement->getUsersPerGroup(),
                'objectiveAndContents' => $announcement->getObjectiveAndContents(),
                'timezone'             => $announcement->getTimezone(),
            ];
        }

        return $announcements;
    }


    private function getItineraries (Course $course): array
    {
        $itineraries = [];

        foreach ($course->getItineraryCourses() as $itineraryCourse)
        {
            $itineraries[] = [
                'id'          => $itineraryCourse->getItinerary()->getId(),
                'name'        => $itineraryCourse->getItinerary()->getName(),
                'description' => $itineraryCourse->getItinerary()->getDescription(),
                'active'      => $itineraryCourse->getItinerary()->isActive(),
                'order'       => $itineraryCourse->getPosition(),
            ];
        }
        return $itineraries;
    }
}
