<?php


namespace App\Controller\Apiv1;

use App\Entity\Filter;
use App\Exception\ExcededApiRequestsException;
use App\Exception\InvalidApiKeyException;
use App\Service\General\FilterService;
use Exception;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;


/**
 * @Route("/api/v1")
 */
class FiltersController extends ApiBaseController
{

    /**
     * @Route("/filters", methods={"GET"})
     * @return Response
     * @throws Exception
     */
    public function __invoke (FilterService $filterService): Response
    {
        $apiKey = $this->requestStack->getCurrentRequest()->headers->get(self::API_KEY_FIELD);

        try
        {
            $this->checkAccess($apiKey);
        }
        catch (InvalidApiKeyException $e)
        {
            return $this->sendResponse([
                'status' => Response::HTTP_UNAUTHORIZED,
                'data'   => ['error' => 'X-API-KEY header is missing or invalid.'],
            ]);
        }
        catch (ExcededApiRequestsException $e)
        {
            return $this->sendResponse([
                'status' => Response::HTTP_TOO_MANY_REQUESTS,
                'data'   => ['error' => 'Too many requests.'],
            ]);
        }


        $this->saveRequest($apiKey, 'filters');

        $filterRepository = $this->entityManager->getRepository(Filter::class);
        $filters           = $filterRepository->findBy([], ['id' => 'ASC']);

        $data = [];
        foreach ($filters as $filter)
        {
            /** @var Filter $filter */
            $data[] = [
                'id'          => $filter->getId(),
                'name'        => $filter->getName(),
                'code'        => $filter->getCode(),
                'categoryId'  => $filter->getFilterCategory()->getId(),
                'parentId'    => $filter->getParent() ? $filter->getParent()->getId() : null,
            ];
        }


        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data'   => $data,
        ]);
    }
}
