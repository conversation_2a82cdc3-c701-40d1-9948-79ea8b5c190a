<?php

declare(strict_types=1);

namespace App\Controller;

use App\Admin\Traits\SerializerTrait;
use App\Entity\User;
use App\Entity\UserToken;
use App\Saml\Saml2Service;
use App\Saml\UserContainer;
use App\Security\Integrations\IntegrationLdapService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;
use Symfony\Component\Intl\Locales;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;
use Symfony\Component\Security\Http\Event\LogoutEvent;

class SecurityController extends AbstractController
{
    use SerializerTrait;
    private Saml2Service $saml2Service;
    private LoggerInterface $logger;
    private EntityManagerInterface $em;
    private SettingsService $settings;
    private IntegrationLdapService $integrationLdapService;

    public function __construct(
        Saml2Service $saml2Service,
        LoggerInterface $logger,
        EntityManagerInterface $em,
        SettingsService $settings,
        IntegrationLdapService $integrationLdapService
    ) {
        $this->saml2Service = $saml2Service;
        $this->logger = $logger;
        $this->em = $em;
        $this->settings = $settings;
        $this->integrationLdapService = $integrationLdapService;
    }

    /**
     * @Route("/login", name="app_login")
     */
    public function login(AuthenticationUtils $authenticationUtils, Request $request): Response
    {
        if ($this->getUser()) {
            if (!$this->isGranted('ROLE_TUTOR') || !$this->isGranted('ROLE_MANAGER')) {
                return $this->redirectToRoute('user-redirect-to-front');
            }
            if ($this->isGranted('ROLE_MANAGER') || $this->isGranted('ROLE_ADMIN')) {
                return $this->redirectToRoute('admin', [], 301);
            }
            //             elseif ($this->isGranted('IS_AUTHENTICATED_FULLY')) return $this->redirect($request->getBaseUrl() . '/campus/', 301);
        }

        // get the login error if there is one
        $error = $authenticationUtils->getLastAuthenticationError();
        // last username entered by the user
        $lastUsername = $authenticationUtils->getLastUsername();

        try {
            $ldapEnabled = $this->integrationLdapService->isEnabled();
        } catch (\Exception $e) {
            $ldapEnabled = false;
        }

        return $this->render(
            'security/login.html.twig',
            [
                'last_username' => $lastUsername,
                'error' => $error,
                'saml' => $this->settings->get('sso.saml'),
                'idp' => [
                    'ldap' => $ldapEnabled,
                    'saml2' => filter_var($this->settings->get('saml.enabled'), FILTER_VALIDATE_BOOLEAN),
                    'oauth2' => filter_var($this->settings->get('oauth2.enabled'), FILTER_VALIDATE_BOOLEAN),
                ],
            ]
        );
    }

    /**
     * @Route("/login-ldap", name="app_login_ldap")
     */
    public function ldapLogin(AuthenticationUtils $authenticationUtils): Response
    {
        try {
            $ldapEnabled = $this->integrationLdapService->isEnabled();
        } catch (\Exception $e) {
            $ldapEnabled = false;
        }
        if (!$ldapEnabled) {
            return $this->redirectToRoute('app_login');
        }

        // get the login error if there is one
        $error = $authenticationUtils->getLastAuthenticationError();
        // last username entered by the user
        $lastUsername = $authenticationUtils->getLastUsername();

        return $this->render(
            'security/ldap_login.html.twig',
            [
                'last_username' => $lastUsername,
                'error' => $error,
            ]
        );
    }

    /**
     * @Route("/logout", name="app_logout")
     */
    public function logout()
    {
        throw new \LogicException('This method can be blank - it will be intercepted by the logout key on your firewall.');
    }

    /**
     * SAML2 Authentication, working with Azure AD.
     */

    /**
     * @Route("/saml2/sso", name="saml2_sso")
     */
    public function samlLogin(Request $request): Response
    {
        return $this->makeSamlLoginRequest($request, '/saml2/consume');
    }

    /**
     * @Route("/saml2/campus/sso", name="saml2_campus_sso")
     */
    public function samlCampusLogin(Request $request): Response
    {
        return $this->makeSamlLoginRequest($request, '/saml2/consume/campus');
    }

    private function makeSamlLoginRequest(Request $request, string $endpoint): Response
    {
        try {
            $content = $this->saml2Service->makeLoginRequest($request, $endpoint);

            return $this->samlRenderFile($content);
        } catch (\Exception $e) {
            return $this->render('saml_error.html.twig', [
                'code' => SymfonyResponse::HTTP_BAD_REQUEST,
                'locale' => $this->settings->get('app.defaultLanguage'),
                'failedFields' => null,
                'message' => $e->getMessage(),
            ]);
        }
    }

    private function samlRenderFile($content): Response
    {
        return $this->render(
            'security/saml.html.twig',
            [
                'data' => $content,
            ]
        );
    }

    /**
     * Used when login from admin.
     *
     * @Route("/saml2/consume", name="saml_sso_consume")
     *
     * @throws \Exception
     */
    public function samlConsume(Request $request): Response
    {
        $result = $this->saml2Service->processResponse($request);
        if ($result instanceof UserContainer) {
            // Login is successfully completed
            // Generate local token
            $this->saml2Service->generateLocalSessionToken($result);

            return $this->redirectToRoute('admin', [], 301);
        }

        return $result;
    }

    /**
     * @Route("/saml2/consume/campus", name="samlConsumeCampus")
     *
     * @return UserContainer|RedirectResponse|Response
     *
     * @throws \Exception
     */
    public function samlConsumeCampus(Request $request)
    {
        $result = $this->saml2Service->processResponse($request);
        if ($result instanceof UserContainer) {
            // Is success, generate token for api usage
            $userToken = new UserToken();
            $userToken->setUser($result->getUser())
                ->setType(UserToken::TYPE_SAML2_AUTH)
            ;
            $this->saml2Service->generateApiSessionToken($result, $userToken);
            $this->em->persist($userToken);
            $this->em->flush();

            return $this->redirect($request->getBaseUrl() . '/campus/?token=' . $userToken->getToken());
        }

        return $result;
    }

    /**
     * @Route("/saml2/sso/logout", name="saml2_logout")
     */
    public function samlLogout(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user) {
            return new RedirectResponse('/');
        }

        $userToken = $this->em->getRepository(UserToken::class)->findOneBy([
            'user' => $user,
            'type' => UserToken::TYPE_SAML2_AUTH,
        ], ['usedAt' => 'DESC']);
        if (!$userToken) {
            return new RedirectResponse('/');
        }
        $params = $userToken->getExtra();
        if (empty($params)) {
            return new RedirectResponse('/');
        }

        $content = $this->saml2Service->logout($params);
        if (!$content) {
            return new RedirectResponse('/');
        }

        return $this->samlRenderFile($content);
    }

    /**
     * @Route("/saml2/logout", name="consume_saml2_logout")
     */
    public function samlProcessLogout(Request $request, Saml2Service $saml2Service): Response
    {
        try {
            $saml2Service->processLogoutResponse($request);
        } catch (\Exception $e) {
        }

        return $this->redirect('/campus/?logout=success');
    }

    /**
     * @Route("redirect-to-campus", name="user-redirect-to-front")
     */
    public function userGoToFront(
        EntityManagerInterface $em,
        Request $request,
        TokenStorageInterface $tokenStorage,
        EventDispatcherInterface $eventDispatcher
    ): Response {
        $user = $this->getUser();
        if (!$user) {
            return new RedirectResponse('/');
        }

        $userToken = new UserToken();
        $token = (new \DateTime())->getTimestamp() . $user->getId() . $user->getEmail();
        $userToken->setUser($user)
            ->setToken(hash('sha256', $token))
            ->setValidUntil((new \DateTimeImmutable())->modify('60 seconds'))
            ->setType(UserToken::TYPE_OTP);

        $em->persist($userToken);
        $em->flush();

        // Logout from admin session handler
        $logoutEvent = new LogoutEvent($request, $tokenStorage->getToken());
        $eventDispatcher->dispatch($logoutEvent);

        $response = $logoutEvent->getResponse();

        $tokenStorage->setToken();

        $response = $this->redirect('/campus/?token=' . $userToken->getToken());
        $response->headers->clearCookie('REMEMBERME');

        return $response;
    }

    /**
     * @Route("/admin/go-to-front", name="goToFront")
     */
    public function goToFront(EntityManagerInterface $em): Response
    {
        $user = $this->getUser();

        $userToken = new UserToken();
        $token = (new \DateTime())->getTimestamp() . $user->getId() . $user->getEmail();
        $userToken->setUser($user)
            ->setToken(hash('sha256', $token))
            ->setValidUntil((new \DateTimeImmutable())->modify('60 seconds'))
            ->setType(UserToken::TYPE_OTP);

        $em->persist($userToken);
        $em->flush();

        return $this->redirect('/campus/?token=' . $userToken->getToken());
    }

    /**
     * @Rest\Get("/admin/languages")
     *
     * @return Response
     */
    public function getAppLocaleInfo()
    {
        $locales = [];
        foreach ($this->settings->get('app.languages') as $locale) {
            $locales[$locale] = ucfirst(Locales::getName($locale, $locale));
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'locales' => $locales,
                'defaultLanguage' => $this->settings->get('app.defaultLanguage'),
                'locale' => $this->getUser()->getLocale(),
            ],
        ]);
    }

    /**
     * @Route("/sso-authentication", name="sso-authentication-route")
     */
    public function globalSso(): Response
    {
        if (true === filter_var($this->settings->get('saml.enabled'), FILTER_VALIDATE_BOOLEAN)) {
            return $this->redirectToRoute('saml2_campus_sso');
        } elseif (true === filter_var($this->settings->get('oauth2.enabled'), FILTER_VALIDATE_BOOLEAN)) {
            return $this->redirectToRoute('oauth2-connect');
        }

        return new Response('Provider not defined. Contact administrator', Response::HTTP_NOT_FOUND);
    }

    /**
     * @Route("/admin-sso", name="sso-admin-auth-route")
     *
     * @return void
     */
    public function adminSSSO()
    {
    }
}
