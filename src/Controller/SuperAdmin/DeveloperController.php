<?php

declare(strict_types=1);

namespace App\Controller\SuperAdmin;

use App\Entity\Catalog;
use App\Service\Catalog\ExecuteCatalogService;
use App\Service\SettingsService;
use App\Service\UserCourse\UserCourseService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use FOS\RestBundle\Controller\Annotations as Rest;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Psr\Log\LoggerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Route("/admin/")
 */
class DeveloperController extends BaseController
{
    private AdminContextProvider $context;
    private JWTManager $JWTManager;
    private ExecuteCatalogService $catalogService;
    private UserCourseService $courseService;

    public function __construct(
        AdminContextProvider $context,
        JWTManager $JWTManager,
        SettingsService $settings,
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        TranslatorInterface $translator,
        ExecuteCatalogService $catalogService,
        UserCourseService $courseService
    ) {
        parent::__construct($settings, $em, $logger, $requestStack, $translator);

        $this->context = $context;
        $this->JWTManager = $JWTManager;
        $this->catalogService = $catalogService;
        $this->courseService = $courseService;
    }

    public static function getEntityFqcn(): string
    {
        return Catalog::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud->overrideTemplate('crud/index', 'super_admin/developer/app.html.twig');
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_INDEX === $responseParameters->get('pageName')) {
            $this->configureAppResponseParameters(
                $responseParameters,
                $this->settings,
                $this->context,
                $this->JWTManager,
                [],
            );
        }

        return $responseParameters;
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Execute fixtures.
     *
     * @Rest\Get("developer/catalogs/all")
     *
     * @return Response
     */
    public function getCatalogs()
    {
        return $this->executeSafe(function () {
            return $this->catalogService->getCatalogs();
        });
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Execute fixtures.
     *
     * @Rest\Get("developer/services/all")
     *
     * @return Response
     */
    public function executeServicesAll()
    {
        return $this->executeSafe(function () {
            return $this->catalogService->executeAllCatalog();
        });
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Post("developer/services")
     *
     * @return Response
     */
    public function executeServices()
    {
        return $this->executeSafe(function () {
            $request = $this->requestStack->getCurrentRequest();
            $servicios = json_decode($request->getContent(), true)['services'];
            $this->catalogService->executeSeviceCatalog($servicios);
            $paramters['message'] = "El servicio '$servicios' se executado correctament";

            return $paramters;
        });
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Get("developer/usercourse/finished")
     */
    public function updateUserCourseFinished(): Response
    {
        return $this->executeSafe(function () {
            $UserCourses = $this->courseService->updateUserCouserFinished();

            return [
                'datos' => $UserCourses
            ];
        });
    }
}
