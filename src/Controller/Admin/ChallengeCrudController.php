<?php

namespace App\Controller\Admin;

use App\Admin\Field\FosCkeditorField;
use App\Admin\Field\VichImageField;
use App\Admin\Traits\LanguagesTrait;
use App\Entity\Challenge;
use App\Entity\ChallengeDuel;
use App\Entity\ChallengeDuelQuestions;
use App\Entity\ChallengeQuestions;
use App\Entity\ChallengeUser;
use App\Entity\ChallengeUserPoints;
use App\Entity\User;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\Field;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ImageField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\LocaleField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Router\CrudUrlGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class ChallengeCrudController extends AbstractCrudController
{
    use LanguagesTrait;

    private $settings;
    private $em;
    private $context;

    public function __construct(SettingsService $settings, EntityManagerInterface $em, AdminContextProvider $context, LoggerInterface $logger)
    {
        $this->settings       = $settings;
        $this->em           = $em;
        $this->context      = $context;
    }

    public static function getEntityFqcn(): string
    {
        return Challenge::class;
    }

    public function configureCrud (Crud $crud): Crud
    {
        return $crud
            ->overrideTemplate('crud/detail', 'admin/challenge/detail.html.twig');
    }


    public function configureFields(string $pageName): iterable
    {
        $panel1 = FormField::addPanel('Basic information');
        $title = TextField::new('title');
        $start_date = DateTimeField::new('start_date');
        $end_date = DateTimeField::new('end_date');
        $imageFile = VichImageField::new('imageFile');
        $announcements  = AssociationField::new('announcement');
        $image = ImageField::new('image')->setBasePath($this->settings->get('app.challenge_uploads_path'));

        $id = IntegerField::new('id', 'ID');

        if($this->settings->get('app.multilingual') == true){
            $locale = LocaleField::new('locale')->setFormTypeOption('choice_loader', null)
                ->setFormTypeOption('choices', $this->getLanguages());
        }

        if($this->settings->get('app.setCoursePoints') == true){
            $points = IntegerField::new('points');
        }

        if (Crud::PAGE_INDEX === $pageName) {
            $fields = [$title, $start_date, $end_date, $image];

            if(isset($locale)){
                $translation = TextField::new('translation');
                array_splice($fields, 4, 0, [$locale]);
                array_splice($fields, 9, 0, [$translation]);
            }

            return $fields;
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            return  [$title, $start_date, $end_date, $image, $announcements];
        } elseif (Crud::PAGE_NEW === $pageName || Crud::PAGE_EDIT) {

            $pointsPosition = 4;

            $fields = [$panel1, $title, $start_date, $end_date, $imageFile, $announcements];

            if(isset($locale)) {
                if(Crud::PAGE_NEW === $pageName) $locale->setFormTypeOptionIfNotSet('data', $this->settings->get('app.defaultLanguage'));
                array_splice($fields, 2, 0, [$locale]);
                $pointsPosition++;
            }

            if(isset($points)) array_splice($fields, $pointsPosition, 0, [$points]);

            return $fields;
        }
    }

    public function configureActions(Actions $actions): Actions
    {
        $actions
            ->add(Crud::PAGE_INDEX, Action::DETAIL);
        if($this->settings->get('app.multilingual') == true){
            $translateAction = Action::new('translate', 'Translate')
                ->linkToCrudAction('translateAction')
                ->displayIf(static function ($entity) {
                    return is_null($entity->getTranslation());
                });

            $actions
                ->add(Crud::PAGE_INDEX, $translateAction);
        }

        return $actions;
    }

    public function configureResponseParameters (KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName'))
        {
            $challengeRepository            = $this->em->getRepository(Challenge::class);
            $pointsRepository = $this->em->getRepository(ChallengeUserPoints::class);
            $challengeDuelRepository = $this->em->getRepository(ChallengeDuel::class);
            $challengeDuelQuestionRepository = $this->em->getRepository(ChallengeDuelQuestions::class);

            $entity                         = $this->context->getContext()->getEntity();
            $challenge                      = $challengeRepository->find($entity->getPrimaryKeyValue());
            $announcements                  = $challenge->getAnnouncement();
            $challengeQuestionRepository    = $this->em->getRepository(ChallengeQuestions::class);
            $questions                      = $challengeQuestionRepository->findBy(['desafio' => $challenge]);
            $userRepository                 = $this->em->getRepository(User::class);
            $user                           = $userRepository->find($challenge->getCreatedBy());
            $ranking                         = $pointsRepository->findBy(["challenge" => $challenge],['points'=> 'DESC']);

            foreach ( $ranking as $item){

                $duelWinAsUser1 = $challengeDuelRepository->getUserDuelWinByChallengeIdUser1($item->getUser(), $challenge);

                $duelWinAsUser2 = $challengeDuelRepository->getUserDuelWinByChallengeIdUser2($item->getUser(), $challenge);

                $duelLoseAsUser1 = $challengeDuelRepository->getUserDuelLoseByChallengeIdUser1($item->getUser(), $challenge);
                $duelLoseAsUser2 = $challengeDuelRepository->getUserDuelLoseByChallengeIdUser2($item->getUser(), $challenge);

                $duelTie = $challengeDuelRepository->getUserDuelTieByChallengeId($item->getUser(), $challenge);

                $item->wins = count($duelWinAsUser1) + count($duelWinAsUser2);
                $item->lose = count($duelLoseAsUser1) + count($duelLoseAsUser2);
                $item->tie  = count($duelTie);
            }

            $allDuels = $challengeDuelRepository->getCountDuelsByIdChallenge($challenge);
            $percentCorrectAnswers1 = $challengeDuelQuestionRepository->getAllCorrectAnswers1ByIdChallenge($challenge);
            $percentCorrectAnswers2 = $challengeDuelQuestionRepository->getAllCorrectAnswers2ByIdChallenge($challenge);

            $users = $challenge->getChallengeUsers();

            $crudUrlGenerator = $this->get(CrudUrlGenerator::class);
            $referrer         = $crudUrlGenerator->build()
                ->unsetAll()
                ->setController(ChallengeCrudController::class)
                ->setAction('detail')
                ->setEntityId($challenge->getId())
                ->generateUrl();

            $responseParameters->set('challenge', $challenge);
            $responseParameters->set('user', $user);
            $responseParameters->set('users', $users);
            $responseParameters->set('questions', $questions);
            $responseParameters->set('categories', $challengeRepository->findAll());
            $responseParameters->set('referrer', $referrer);
            $responseParameters->set('announcements', $announcements);
            $responseParameters->set('ranking', $ranking);
        }
        return $responseParameters;
    }

}
