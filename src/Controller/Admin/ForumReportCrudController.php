<?php

namespace App\Controller\Admin;

use App\Admin\Traits\SerializerTrait;
use App\Entity\ForumReport;
use App\Repository\ForumReportRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextareaField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\RequestStack;

class ForumReportCrudController extends AbstractCrudController
{
    use SerializerTrait;

    private $settings;
    private $logger;
    private $requestStack;
    private $em;
    private $context;


    public function __construct (SettingsService $settings, LoggerInterface $logger, RequestStack $requestStack, EntityManagerInterface $em, AdminContextProvider $context)
    {
        $this->settings       = $settings;
        $this->logger       = $logger;
        $this->requestStack = $requestStack;
        $this->em           = $em;
        $this->context      = $context;
    }
    public static function getEntityFqcn(): string
    {
        return ForumReport::class;
    }

//    public function configureCrud (Crud $crud): Crud
//    {
//        return $crud
//            ->overrideTemplate('crud/index', 'admin/forum/reportindex.html.twig');
//
//    }

    public function configureFields(string $pageName): iterable
    {
        $forumPost  = AssociationField::new('forumPost');


        $thread = TextField::new('thread')->setLabel('Hilo');

        if (Crud::PAGE_INDEX === $pageName)
        {
            return [$forumPost];
        }
        else if (Crud::PAGE_DETAIL === $pageName)
        {
            return [$forumPost];
        }
        else if (Crud::PAGE_NEW === $pageName)
        {
            return [$forumPost];
        }
        else if (Crud::PAGE_EDIT === $pageName)
        {
            return [$forumPost];
        }
    }

//    public function configureResponseParameters (KeyValueStore $responseParameters): KeyValueStore
//    {
//        if (Crud::PAGE_INDEX === $responseParameters->get('pageName'))
//        {
//
//            $experimentRepository = $this->em->getRepository(ForumReport::class);
//            $entity               = $this->context->getContext()->getEntity();
//
//            $experiment = $experimentRepository->findAll();
//
//            $responseParameters->set('experiment', $experiment);
//        }
//        return $responseParameters;
//    }
}
