<?php

namespace App\Controller\Admin;


use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\DeleteFilePathTrait;
use App\Entity\Chapter;
use App\Entity\Puzzle;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use FOS\RestBundle\Controller\Annotations as Rest;


class PuzzleCrudController extends AbstractCrudController
{
    use DeleteFilePathTrait, SerializerTrait;

    private $em;
    private $logger;
    private $requestStack;

    public function __construct(EntityManagerInterface $em, LoggerInterface $logger, RequestStack $requestStack)
    {
        $this->em = $em;
        $this->logger = $logger;
        $this->requestStack = $requestStack;
    }

    public static function getEntityFqcn(): string
    {
        return Puzzle::class;
    }

    /**
     * @Route("/upload-image-puzzle", name="upload-imagen-puzzle",methods={"POST"})
     * @return Response
     */

    public function uploadFilePuzzle(Request $request)
    {

        $response = [];

        try {
            $chapterId = $request->get('chapter');
            $chapter = $this->em->getRepository(Chapter::class)->find($chapterId);
            $file = $request->files->get('file');

            $puzzle = new Puzzle();
            $puzzle->setImage('');
            $puzzle->setImageFile($file);
            $puzzle->setChapter($chapter);
            $this->em->persist($puzzle);
            $this->em->flush();


            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'La imagen se guardó exitosamente'
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Ha ocurrido un error al guardar la imagen: {' . $e->getMessage() . '}'
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Rest\Get("/admin/detail-puzzle/{id}", name="details-puzzle")
     *
     * @return Response
     */
    public function detailPuzzle(Chapter $chapter)
    {
        $puzzle = $this->em->getRepository(Puzzle::class)->findOneBy([
            'Chapter' => $chapter
        ]);
        try {
            $code = Response::HTTP_OK;
            $error = false;

            if ($puzzle) {
                $response = [
                    'status' => $code,
                    'error' => $error,
                    'data' => [
                        'id' =>  $puzzle->getId(),
                        'chapter' => $puzzle->getChapter()->getId(),
                        'image' => $puzzle->getImage()
                    ]
                ];
            } else {
                $response = [
                    'status' => $code,
                    'error' => $error,
                    'data' => "No results"
                ];
            }
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Ha ocurrido un error al mostra el detalle del puzzle - Error: {' . $e->getMessage() . '}'
            ];
        }

        return $this->sendResponse($response, array('groups' => array('details')));
    }

    /**
     * @Route("/admin/update-image-puzzle", name="update-image-puzzle",methods={"POST"})   
     * @return Response
     */
    public function updateImagepuzzle(Request $request)
    {
        try {
            $puzzleId =  $request->get('id');;
            $chapterId =  $request->get('chapter');;
            $file = $request->files->get('file');
            $filePrevious = $request->get('nameImage');

            $chapter = $this->em->getRepository(Chapter::class)->find($chapterId);
            $puzzle = $this->em->getRepository(Puzzle::class)->find($puzzleId);

            if (!$puzzle) {
                throw $this->createNotFoundException("No se encontro puzzle con ese Id " . $puzzleId);
            }

            $puzzle->setImage('');
            $puzzle->setImageFile($file);
            $puzzle->setChapter($chapter);
            $this->em->persist($puzzle);
            $this->em->flush();

            //Eliminar Imagen Anterior
            $this->deleteFile($this->getParameter('app.puzzle_uploads_path'), $filePrevious);

            $response = [
                'status' => 200,
                'error' => false,
                'data' => 'La imagen se actualizó exitosamente'
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Ha ocurrido un error al actualizar el puzzle - Error: {' . $e->getMessage() . '}'
            ];
        }

        return $this->sendResponse($response);
    }
}
