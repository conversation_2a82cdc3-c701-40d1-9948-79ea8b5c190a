<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Admin\Field\FosCkeditorField;
use App\Admin\Field\VichImageField;
use App\Admin\Filter\CourseCreatorFilter;
use App\Admin\Filter\CourseLocaleFilter;
use App\Admin\Filter\CourseSegmentFilter;
use App\Admin\Traits\HelpEntityTrait;
use App\Admin\Traits\LanguagesTrait;
use App\Admin\Traits\ResponseTrait;
use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\VueAppDefaultConfiguration;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\CourseCategoryTranslation;
use App\Entity\CourseLevel;
use App\Entity\CourseSegment;
use App\Entity\Export;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\Nps;
use App\Entity\ProfessionalCategory;
use App\Entity\Season;
use App\Entity\Survey;
use App\Entity\SurveyCourse;
use App\Entity\Tag;
use App\Entity\TaskCourse;
use App\Entity\TranslationsAdmin;
use App\Entity\TranslationsAdminTranslation;
use App\Entity\TypeCourse;
use App\Entity\TypeCourseTranslation;
use App\Entity\TypeDiploma;
use App\Entity\UrlShortener;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Enum\DiplomaType;
use App\Enum\TypeCourse as EnumTypeCourse;
use App\Repository\CourseRepository;
use App\Repository\FilterRepository;
use App\Repository\ProfessionalCategoryRepository;
use App\Repository\TypeCourseRepository;
use App\Repository\UrlShortenerRepository;
use App\Repository\UserCourseRepository;
use App\Repository\UserRepository;
use App\Security\Voter\CourseVoter;
use App\Service\Course\CloneRolePlayService;
use App\Service\Course\Common\UserCourseService;
use App\Service\General\ImageUploaderHandler;
use App\Service\SettingsService;
use App\Service\Task\TaskService;
use App\Utils\CharactersCurationUtils;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FieldCollection;
use EasyCorp\Bundle\EasyAdminBundle\Collection\FilterCollection;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Assets;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Filters;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\SearchDto;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\CollectionField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\Field;
use EasyCorp\Bundle\EasyAdminBundle\Field\FormField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ImageField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use EasyCorp\Bundle\EasyAdminBundle\Field\LocaleField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Filter\BooleanFilter;
use EasyCorp\Bundle\EasyAdminBundle\Filter\ChoiceFilter;
use EasyCorp\Bundle\EasyAdminBundle\Filter\DateTimeFilter;
use EasyCorp\Bundle\EasyAdminBundle\Orm\EntityRepository;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use FOS\RestBundle\Controller\Annotations as Rest;
use KMS\FroalaEditorBundle\Form\Type\FroalaEditorType;
use Knp\Component\Pager\PaginatorInterface;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Psr\Log\LoggerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Component\Form\Extension\Core\Type\LocaleType;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Intl\Locales;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Route("/admin")
 */
class CourseCrudController extends AbstractCrudController
{
    use SerializerTrait;
    use LanguagesTrait;
    use ResponseTrait;
    use HelpEntityTrait;
    use VueAppDefaultConfiguration;

    private EntityManagerInterface $em;
    private AdminContextProvider $context;
    private LoggerInterface $logger;
    private JWTManager $jwt;
    protected TranslatorInterface $translator;
    private RouterInterface $router;
    private RequestStack $requestStack;
    private PaginatorInterface $paginator;
    private ImageUploaderHandler $imageUploaderHandler;
    private AdminUrlGenerator $adminUrlGenerator;
    private SettingsService $settings;
    private CloneRolePlayService $cloneRolePlayService;

    private UserCourseService $userCourseService;
    private TaskService $taskService;

    public function __construct(
        EntityManagerInterface $em,
        RouterInterface $router,
        ImageUploaderHandler $imageUploaderHandler,
        AdminUrlGenerator $adminUrlGenerator,
        RequestStack $requestStack,
        AdminContextProvider $context,
        LoggerInterface $logger,
        JWTManager $jwt,
        TranslatorInterface $translator,
        PaginatorInterface $paginator,
        SettingsService $settingsService,
        CloneRolePlayService $cloneRolePlayService,
        UserCourseService $userCourseService,
        TaskService $taskService
    ) {
        $this->em = $em;
        $this->context = $context;
        $this->logger = $logger;
        $this->jwt = $jwt;
        $this->translator = $translator;
        $this->router = $router;
        $this->requestStack = $requestStack;
        $this->paginator = $paginator;

        $this->imageUploaderHandler = $imageUploaderHandler;
        $this->adminUrlGenerator = $adminUrlGenerator;
        $this->settings = $settingsService;
        $this->cloneRolePlayService = $cloneRolePlayService;

        $this->userCourseService = $userCourseService;
        $this->taskService = $taskService;
    }

    public static function getEntityFqcn(): string
    {
        return Course::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular($this->translator->trans('course.label_in_singular', [], 'messages', $this->getUser()->getLocale()))
            ->setEntityLabelInPlural($this->translator->trans('course.label_in_plural', [], 'messages', $this->getUser()->getLocale()))
            ->addFormTheme('@FOSCKEditor/Form/ckeditor_widget.html.twig')
            ->addFormTheme('@KMSFroalaEditor/Form/froala_widget.html.twig')
            ->setSearchFields(['id', 'code', 'name', 'description', 'image', 'translation.id', 'tags.name'])
            ->overrideTemplate('crud/detail', 'admin/course/detail.html.twig')
            ->overrideTemplate('crud/index', 'admin/course/index.html.twig')
            ->setDefaultSort(['createdAt' => 'DESC']);
    }

    public function configureAssets(Assets $assets): Assets
    {
        return $assets
            // adds the CSS and JS assets associated to the given Webpack Encore entry
            // it's equivalent to adding these inside the <head> element:
            // {{ encore_entry_link_tags('...') }} and {{ encore_entry_script_tags('...') }}
            ->addWebpackEncoreEntry('courseClone')
            ->addWebpackEncoreEntry('open_visible');
    }

    public function configureFields(string $pageName): iterable
    {
        $panel1 = FormField::addPanel($this->translator->trans('course.configureFields.basic_information', [], 'messages', $this->getUser()->getLocale()));
        $code = TextField::new('code', $this->translator->trans('course.configureFields.code', [], 'messages', $this->getUser()->getLocale()))
            ->setTemplatePath('bundles/EasyAdminBundle/field-linked.html.twig');
        $name = TextField::new('name', $this->translator->trans('course.configureFields.name', [], 'messages', $this->getUser()->getLocale()))
            ->setTemplatePath('bundles/EasyAdminBundle/field-code-name-course.html.twig');
        $description = FosCkeditorField::new('description', $this->translator->trans('course.configureFields.description', [], 'messages', $this->getUser()->getLocale()))->setFormTypeOptions([
            'config_name' => 'basic',
        ]);

        $open = BooleanField::new('open', $this->translator->trans('course.configureFields.open', [], 'messages', $this->getUser()->getLocale()));
        $open_new = BooleanField::new('isNew', $this->translator->trans('course.configureFields.new', [], 'messages', $this->getUser()->getLocale()));
        $open_visible = BooleanField::new('open_visible', $this->translator->trans('course.configureFields.open_visible', [], 'messages', $this->getUser()->getLocale()));
        $active = BooleanField::new('active', $this->translator->trans('course.configureFields.active', [], 'messages', $this->getUser()->getLocale()));
        $categories = AssociationField::new('categories', $this->translator->trans('course.configureFields.profesional_categories', [], 'messages', $this->getUser()->getLocale()))->setFormTypeOptions(['attr' => ['name' => 'categories']]);
        $panel3 = FormField::addPanel($this->translator->trans('course.configureFields.image', [], 'messages', $this->getUser()->getLocale()));
        $imageFile = VichImageField::new('imageFile')->setFormTypeOptions(['attr' => ['accept' => 'image/*']]);
        $id = IntegerField::new('id', 'ID');
        $image = ImageField::new('image')->setBasePath($this->settings->get('app.course_uploads_path'));
        $thumbnail = ImageField::new('thumbnailUrl', $this->translator->trans('course.configureFields.thumbnail_url', [], 'messages', $this->getUser()->getLocale()));
        $createdAt = DateTimeField::new('createdAt', $this->translator->trans('common_areas.created_at', [], 'messages', $this->getUser()->getLocale()));
        $updatedAt = DateTimeField::new('updatedAt', $this->translator->trans('common_areas.updated_at', [], 'messages', $this->getUser()->getLocale()));
        $deletedAt = DateTimeField::new('deletedAt', $this->translator->trans('common_areas.deleted_at', [], 'messages', $this->getUser()->getLocale()));
        $announcements = AssociationField::new('announcements');
        $chapters = AssociationField::new('chapters', $this->translator->trans('course.configureFields.chapter', [], 'messages', $this->getUser()->getLocale()));

        $category = AssociationField::new('category', $this->translator->trans('course.configureFields.category', [], 'messages', $this->getUser()->getLocale()))
            ->setTemplatePath('bundles/EasyAdminBundle/field-course-category-translation.html.twig');

        $information = Field::new('generalInformation', $this->translator->trans('course.configureFields.general_information', [], 'messages', $this->getUser()->getLocale()))->setFormType(FroalaEditorType::class);
        $panelInformation = FormField::addPanel($this->translator->trans('chapter.configureFields.general_information', [], 'messages', $this->getUser()->getLocale()));
        $tagsAss = AssociationField::new('tags');

        $totalChapter = IntegerField::new('totalChapter', $this->translator->trans('course.configureFields.chapter', [], 'messages', $this->getUser()->getLocale()))
            ->addCssClass('badge badge-secondary badge-pill m-4');
        $isCompleted = BooleanField::new('completed', $this->translator->trans('complete', [], 'messages', $this->getUser()->getLocale()))->renderAsSwitch(false);
        $typeCourse = AssociationField::new('typeCourse', $this->translator->trans('chapter.configureFields.type', [], 'chapters', $this->getUser()->getLocale()))
            ->setTemplatePath('bundles/EasyAdminBundle/field-custom-linked.html.twig');

        $isMainCourseEvaluation = CollectionField::new('mainCourseEvaluation')
            ->setFieldFqcn(Tag::class)
            ->setFormType('App\Form\Type\Admin\MainNpsCourseType')
            ->setEntryIsComplex(true)
            ->showEntryLabel(false)
            ->setFormTypeOptions([
                'label' => false,
            ]);

        $tags = CollectionField::new('tags')
            ->setFieldFqcn(Tag::class)
            ->setFormType('App\Form\TagFormType')
            ->setEntryIsComplex(true)
            ->showEntryLabel(false)
            ->setFormTypeOptions([
                'label' => 'Tags',
                'attr' => [
                    'placeholder' => $this->translator->trans('course.configureFields.tag_description', [], 'messages', $this->getUser()->getLocale()),
                ],
            ])
            ->addJsFiles('https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js')
            ->addJsFiles('https://twitter.github.io/typeahead.js/releases/latest/typeahead.bundle.js')
            ->addJsFiles('tag/tagsinput.js')
            ->addCssFiles('tag/tagsinput.css');

        if (true == $this->settings->get('app.multilingual')) {
            $locale = LocaleField::new('locale')
                ->setLabel($this->translator->trans('course.configureFields.language', [], 'messages', $this->getUser()->getLocale()))
                ->setFormTypeOption('choice_loader', null)
                ->setFormTypeOption('choices', $this->getLanguages());
        }

        if (true == $this->settings->get('app.setCoursePoints')) {
            $points = IntegerField::new('points');
        }

        if (true == $this->settings->get('app.useSegment')) {
            // $segment = CollectionField::new('courseSegments', $this->translator->trans('course.configureFields.segment', [], 'messages', $this->getUser()->getLocale()));
            $segmentRepository = $this->em->getRepository(CourseSegment::class);
            $query = $segmentRepository->findAll();
            $filter = AssociationField::new('courseSegments', $this->translator->trans('course.configureFields.segment', [], 'messages', $this->getUser()->getLocale()))
                ->setFormTypeOptions([
                    'choices' => $query,
                    'by_reference' => false,
                ])->addJsFiles('build/selectDependent.js');
        }

        if (true == $this->settings->get('app.setCourseLevel')) {
            $level = AssociationField::new('level');
        }

        if (true == $this->settings->get('app.courseInfoGeneral')) {
            $documentation = Field::new('documentation')->setFormType(FroalaEditorType::class);
            $panelDocumentation = FormField::addPanel('Documentation');
        }

        if (Crud::PAGE_INDEX === $pageName) {
            $fields = [$id, $typeCourse, $code, $name, $category, $tagsAss, $active, $open, $open_new, $thumbnail, $totalChapter, $isCompleted];

            if (isset($locale)) {
                $translation = AssociationField::new('translations', $this->translator->trans('course.configureFields.translation', [], 'messages', $this->getUser()->getLocale()));
                array_splice($fields, 4, 0, [$locale]);
                array_splice($fields, 10, 0, [$translation]);
            }
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            $fields = [$id, $code, $name, $description, $image, $open, $open_visible, $createdAt, $updatedAt, $deletedAt, $announcements, $chapters, $categories, $typeCourse];
            if (isset($locale)) {
                $translation = TextField::new('translation');
                array_splice($fields, 4, 0, [$locale]);
                array_splice($fields, 9, 0, [$translation]);
            }

            return $fields;
        } elseif (Crud::PAGE_NEW === $pageName || Crud::PAGE_EDIT) {
            $levelPosition = 4;
            $pointsPosition = 4;

            $code->setColumns(6);
            $name->setColumns(6);
            $category->setColumns(6);
            $description->setColumns(12);
            $information->setColumns(12);
            $fields = [$panel1, $code, $name, $category, $description, $tags, $panelInformation, $information, $active, $open, $open_new, $open_visible, $panel3, $imageFile, $isMainCourseEvaluation];

            if (isset($locale)) {
                $locale->setColumns(6);
                if (Crud::PAGE_NEW === $pageName) {
                    $locale->setFormTypeOptionIfNotSet('data', $this->settings->get('app.defaultLanguage'));
                }
                array_splice($fields, 2, 0, [$locale]);
                ++$levelPosition;
                ++$pointsPosition;
            }

            if (isset($level)) {
                array_splice($fields, $levelPosition, 0, [$level]);
                ++$levelPosition;
                ++$pointsPosition;
            }

            if (isset($filter)) {
                $filter->setColumns(6);
                array_splice($fields, $levelPosition, 0, [$filter]);
                ++$pointsPosition;
            }

            if (isset($points)) {
                array_splice($fields, $pointsPosition, 0, [$points]);
            }

            if (isset($documentation)) {
                array_splice($fields, $pointsPosition + 3, 0, [$panelDocumentation, $documentation]);
            }
        }

        if (!$this->isGranted('COURSE_PUBLISH')) {
            array_splice($fields, array_search($active, $fields), 1);
        }

        return $fields;
    }

    public function configureActions(Actions $actions): Actions
    {
        /**
         * ************************
         * ***  Index page    ***.
         * ************************
         */
        $cloneCourse = Action::new('cloneCourse', $this->translator->trans('course.configureFields.clone', [], 'messages', $this->getUser()->getLocale()))
            ->linkToCrudAction('cloneCourse');

        $editCourseAction = Action::new('editCourse', $this->translator->trans('Edit', [], 'messages', $this->getUser()->getLocale()))
            ->linkToCrudAction('editCourseAction');

        $actions->add(Crud::PAGE_INDEX, Action::DETAIL);
        $actions->add(Crud::PAGE_INDEX, $cloneCourse);
        $actions->add(Crud::PAGE_INDEX, $editCourseAction);
        $actions->remove(Crud::PAGE_INDEX, Action::NEW);
        $actions->remove(Crud::PAGE_INDEX, Action::EDIT);
        $actions->reorder(Crud::PAGE_INDEX, [Action::DETAIL, 'editCourse', 'cloneCourse', Action::DELETE]);
        // $actions->remove(Crud::PAGE_INDEX, Action::DELETE);

        /**
         * ************************
         * ***  Details page    ***.
         * ************************
         */
        $editCourseDetailAction = Action::new('editCourse', $this->translator->trans('Edit', [], 'messages', $this->getUser()->getLocale()))
            ->linkToCrudAction('editCourseAction')
            ->setIcon('fas fa-pencil')->addCssClass('btn btn-primary');

        $accessLevelAction = Action::new('accessLevel', $this->translator->trans('course.configureFields.access_level', [], 'messages', $this->getUser()->getLocale()))
            ->linkToCrudAction('accessLevelAction')
            ->addCssClass('btn btn-primary')
            ->setIcon('fas fa-layer-group');

        $detailsOrder = [Action::DELETE, 'editCourse', 'accessLevel'];

        if ($this->settings->get('app.multilingual')) {
            $translateAction = Action::new('translate', $this->translator->trans('course.configureFields.translate', [], 'messages', $this->getUser()->getLocale()))
                ->linkToCrudAction('translateAction')
                ->displayIf(static function ($entity) {
                    return \is_null($entity->getTranslation());
                })
                ->addCssClass('btn btn-primary')
                ->setIcon(' fa fa-globe');

            $actions
                ->add(Crud::PAGE_DETAIL, $translateAction);
            $detailsOrder[] = 'translate';
        }

        $accessLevelAction = Action::new('accessLevel', $this->translator->trans('course.configureFields.access_level', [], 'messages', $this->getUser()->getLocale()))
            ->linkToCrudAction('accessLevelAction')
            ->addCssClass('btn btn-primary')
            ->setIcon('fas fa-layer-group');

        $shareCourseAction = Action::new('shareCourse', $this->translator->trans('share', [], 'messages', $this->getUser()->getLocale()))
            ->linkToCrudAction('actionGetSharedCourseUrl');

        //    $actions->add(Crud::PAGE_DETAIL, $accessLevelAction);
        $actions->add(Crud::PAGE_DETAIL, $editCourseDetailAction);

        $cloneCourse = Action::new('cloneCourse', $this->translator->trans('course.configureFields.clone', [], 'messages', $this->getUser()->getLocale()))
            ->linkToCrudAction('cloneCourse');

        // $actions->add(Crud::PAGE_INDEX, $cloneCourse);
        $actions->add(Crud::PAGE_INDEX, $shareCourseAction);

        $actions->reorder(Crud::PAGE_DETAIL, []);
        $actions->remove(Crud::PAGE_DETAIL, Action::DELETE);
        $actions->remove(Crud::PAGE_DETAIL, Action::EDIT);
        $actions->remove(Crud::PAGE_INDEX, Action::DELETE);

        return $actions;
    }

    // editCourseAction

    public function cloneCourse(AdminContext $context)
    {
        $id = $context->getRequest()->query->get('entityId');

        $entity = $this->em->getRepository(Course::class)->find($id);
        $clonedCourse = clone $entity;
        $clonedCourse->setName($clonedCourse->getName() . '[Clon]');
        $clonedCourse->setCreatedAt(new \DateTime());
        $clonedCourse->setUpdatedAt(new \DateTime());
        $clonedCourse->setActive(false);
        $this->cloneRolePlayService->cloneRolePlay($clonedCourse);
        $this->em->persist($clonedCourse);

        $translateCourses = $this->em->getRepository(Course::class)->findBy(['translation' => $entity]);
        foreach ($translateCourses as $course) {
            $translateCloneCourse = clone $course;
            $translateCloneCourse->setTranslation($clonedCourse);
            $translateCloneCourse->setName($course->getName() . '[Clon]');
            $translateCloneCourse->setCreatedAt(new \DateTime());
            $translateCloneCourse->setUpdatedAt(new \DateTime());
            $translateCloneCourse->setActive(false);
            $this->em->persist($translateCloneCourse);
        }

        $this->em->flush();

        $url = $this->get(AdminUrlGenerator::class)
            ->setAction(Action::DETAIL)
            ->setEntityId($clonedCourse->getId())
            ->generateUrl();

        return $this->redirect($url);
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName')) {
            $courseRepository = $this->em->getRepository(Course::class);
            $entity = $this->context->getContext()->getEntity();
            $course = $courseRepository->find($entity->getPrimaryKeyValue());

            $seasonTypes = [
                Season::TYPE_SEQUENTIAL => $this->translator->trans('course.season.type.sequential', [], 'messages', $this->getUser()->getLocale()),
                Season::TYPE_FREE => $this->translator->trans('course.season.type.free', [], 'messages', $this->getUser()->getLocale()),
                Season::TYPE_EXAM => $this->translator->trans('course.season.type.exam', [], 'messages', $this->getUser()->getLocale()),
            ];

            $adminUrlGenerator = $this->get(AdminUrlGenerator::class);
            $referrer = $adminUrlGenerator
                ->unsetAll()
                ->setController(CourseCrudController::class)
                ->setAction('detail')
                ->setEntityId($course->getId())
                ->generateUrl();

            $referrerSeasons = $adminUrlGenerator
                ->unsetAll()
                ->setController(CourseCrudController::class)
                ->setAction('detail')
                ->set('tab', 'seasons')
                ->setEntityId($course->getId())
                ->generateUrl();

            $referrerAnnouncement = $adminUrlGenerator
                ->unsetAll()
                ->setController(CourseCrudController::class)
                ->setAction('detail')
                ->set('tab', 'announcement')
                ->setEntityId($course->getId())
                ->generateUrl();

            $referrerAnnouncementDelete = $adminUrlGenerator
                ->unsetAll()
                ->setController(CourseCrudController::class)
                ->setAction('detail')
                ->set('tab', 'announcement')
                ->setEntityId($course->getId())
                ->generateUrl();

            $referrerTranslateCourse = $adminUrlGenerator
                ->unsetAll()
                ->setController(CourseCrudController::class)
                ->setAction('detail')
                ->set('tab', 'translate-course')
                ->setEntityId($course->getId())
                ->generateUrl();

            $referrerTask = $adminUrlGenerator
                ->unsetAll()
                ->setController(CourseCrudController::class)
                ->setAction('detail')
                ->set('tab', 'task')
                ->setEntityId($course->getId())
                ->generateUrl();

            $tab = $this->context->getContext()->getRequest()->get('tab');

            if (!$tab) {
                if (null == $course->getTypeCourse() || (1 == $course->getTypeCourse()->getId() || 3 == $course->getTypeCourse()->getId())) {
                    $tab = 'chapters';
                } else {
                    $tab = 'announcement';
                }
            }

            $userCourseRepository = $this->em->getRepository(UserCourse::class);
            $userCourseChapterRepository = $this->em->getRepository(UserCourseChapter::class);
            $npsRepository = $this->em->getRepository(Nps::class);

            $usersIds = $this->userCourseService->getAllUsersIds($course);
            $findUsers = false;

            $averageTime = $userCourseChapterRepository->getAverageTimeSpentByCourse($course);
            $stats = [
                'startedCount' => $this->userCourseService->courseGetTotalUsersStarted($course, $findUsers, $usersIds), // $userCourseRepository->countByCourse($course, false),
                'finishedCount' => $this->userCourseService->courseGetTotalUsersFinished($course, $findUsers, $usersIds), // $userCourseRepository->countByCourse($course, true),
                'totalTime' => $userCourseChapterRepository->getTimeSpentByCourse($course),
                'averageTime' => $averageTime['averageTime'],
                'averageStars' => $npsRepository->getStarsAVG(['courseID' => $course->getId()]),
            ];
            $tabs = [
                'tabPerson' => $this->settings->get('app.course.tab.person'),
                'tabStats' => $this->settings->get('app.course.tab.stats'),
                'tabOpinions' => $this->settings->get('app.course.tab.opinions'),
                'announcement_enabled' => $this->settings->get('app.announcement.enabled'),
            ];

            // Category translation.
            /** @var CourseCategory $courseCategory */
            $courseCategory = $course->getCategory();
            /** @var CourseCategoryTranslation $categoryTranslation */
            $categoryTranslation = $courseCategory->translate($course->getLocale());

            $responseParameters->set('course', $course);
            $responseParameters->set('courseCategory', $categoryTranslation->getName());
            $responseParameters->set('chapters', $course->getChapters());
            $responseParameters->set('referrer', $referrer);
            $responseParameters->set('referrerSeasons', $referrerSeasons);
            $responseParameters->set('referrerAnnouncement', $referrerAnnouncement);
            $responseParameters->set('referrerAnnouncementDelete', $referrerAnnouncementDelete);
            $responseParameters->set('referrerTranslateCourse', $referrerTranslateCourse);
            $responseParameters->set('referrerTask', $referrerTask);

            $responseParameters->set('token', $this->jwt->create($this->getUser()));
            $responseParameters->set('locale', $this->getUser()->getLocale());

            $responseParameters->set('tab', $tab);
            $responseParameters->set('stats', $stats);
            $responseParameters->set('isExportRedirect', empty($_GET['referrer']) && isset($_GET['exportStatus']));
            $responseParameters->set('exportStatus', empty($_GET['exportStatus']) ? null : $_GET['exportStatus']);

            $responseParameters->set('seasonTypes', $seasonTypes);
            $responseParameters->set('tabs', $tabs);

            // Configure app response parameters for course detail view
            $this->configureAppResponseParameters(
                responseParameters: $responseParameters,
                settings: $this->settings,
                context: $this->context,
                JWTManager: $this->jwt,
                config: [
                    'isAnnouncementEnabled' => $this->settings->get('app.announcement.enabled'),
                ]
            );
        }

        if (Crud::PAGE_INDEX === $responseParameters->get('pageName')) {
            $share = $this->context->getContext()->getRequest()->get('share');
            $responseParameters->set('share', $share);
            $this->getHelp($responseParameters, 'Course');
        }

        return $responseParameters;
    }

    public function configureFilters(Filters $filters): Filters
    {
        $filters
            ->add(CourseCreatorFilter::new('created_by', $this->translator->trans('common_areas.created_by', [], 'messages', $this->getUser()->getLocale())))
            // ->add(CourseCreationDateFilter::new('created_at', $this->translator->trans('common_areas.created_at', [], 'messages', $this->getUser()->getLocale())))
            ->add(DateTimeFilter::new('createdAt')->setLabel($this->translator->trans('course.configureFields.created_at', [], 'messages', $this->getUser()->getLocale())))
            ->add(BooleanFilter::new('active')->setLabel($this->translator->trans('course.configureFields.active', [], 'messages', $this->getUser()->getLocale())))
            ->add(BooleanFilter::new('open')->setLabel($this->translator->trans('course.configureFields.open', [], 'messages', $this->getUser()->getLocale())))
            ->add(BooleanFilter::new('isNew')->setLabel($this->translator->trans('course.configureFields.new', [], 'messages', $this->getUser()->getLocale())))
            ->add(BooleanFilter::new('open_visible')->setLabel($this->translator->trans('course.configureFields.open_visible', [], 'messages', $this->getUser()->getLocale())));

        $categories = array_flip($this->em->getRepository(CourseCategory::class)->getList());
        $typeCourse = array_flip($this->em->getRepository(TypeCourse::class)->getList());
        if (\count($categories)) {
            $filters->add(
                ChoiceFilter::new('category')
                    ->setChoices($categories)
                    ->setLabel($this->translator->trans('course.configureFields.category', [], 'messages', $this->getUser()->getLocale()))
            );
        }
        if (\count($typeCourse)) {
            $filters->add(
                ChoiceFilter::new('typeCourse')
                    ->setChoices($typeCourse)
                    ->setLabel($this->translator->trans('chapter.configureFields.type', [], 'chapters', $this->getUser()->getLocale()))
            );
        }
        //            ->add(CourseProfessionalCategoryFilter::new('categories', $this->translator->trans('course.configureFields.profesional_categories', [], 'messages', $this->getUser()->getLocale())));

        if (true == $this->settings->get('app.multilingual')) {
            $filters
                ->add(CourseLocaleFilter::new('locale', $this->translator->trans('course.configureFields.locale', [], 'messages', $this->getUser()->getLocale())));
        }

        if (true == $this->settings->get('app.setCourseLevel')) {
            $filters
                ->add(
                    ChoiceFilter::new('level')
                        ->setChoices(array_flip($this->em->getRepository(CourseLevel::class)->getList()))
                        ->setLabel('Nivel')
                );
        }

        if (true == $this->settings->get('app.useSegment')) {
            $filters
                ->add(CourseSegmentFilter::new('courseSegments', 'Segmentos'));
        }

        return $filters;
    }

    public function detail(AdminContext $context)
    {
        $responseParameters = parent::detail($context); // TODO: Change the autogenerated stub

        /*
         * NPS Calc
         */
        $entity = $this->context->getContext()->getEntity();
        /**
         * @var $course Course
         */
        $course = $this->em->getRepository(Course::class)->find($entity->getPrimaryKeyValue());
        $nps = $this->em->getRepository(Nps::class)->getNpsForCourse($course);

        $query = $this->em->getRepository(Nps::class)->findOpinionCourse($course, 'text');
        $page = $context->getRequest()->query->getInt('page', 1);

        // send opinions to view paginate

        $taskCourse = $this->em->getRepository(TaskCourse::class)->findBy(['course' => $entity->getPrimaryKeyValue(), 'announcement' => null]);
        $responseParameters->set('nps', (\is_null($nps)) ? '-' : (int) ($nps['nps'] * 100));

        $this->logger->error(serialize($nps));

        $form = $this->createFormBuilder()
            ->add('language', LocaleType::class, [
                'attr' => [
                    'data-widget' => 'select2',
                    'class' => 'form-control',
                ],
                'choice_loader' => null,
                'choices' => $this->getLanguages(),
            ])
            ->getForm()
            ->createView();

        $responseParameters->set('translateForm', $form);
        // $responseParameters->set('opinions', $opinions);
        $responseParameters->set('urlCourseOpinion', $this->generateUrlCourseOpinion($course));
        $courseStatus = [UserCourse::STATUS_NO_STARTED, UserCourse::STATUS_STARTED, UserCourse::STATUS_FINISHED];
        $responseParameters->set('courseStatus', $courseStatus);

        /**
         * @var $creator User
         */
        $creator = $this->em->getRepository(User::class)->findWithDeleted($course->getCreatedBy()->getId());
        $updater = $this->em->getRepository(User::class)->findWithDeleted($course->getUpdatedBy()->getId());

        $responseParameters->set('creator', $creator);
        $responseParameters->set('updater', $updater);
        $responseParameters->set('taskCourse', $taskCourse);

        return $responseParameters;
    }

    public function generateUrlCourseOpinion($course)
    {
        $adminUrlGenerator = $this->get(AdminUrlGenerator::class);

        return $adminUrlGenerator
            ->unsetAll()
            ->setController(CourseCrudController::class)
            ->setAction('detail')
            ->set('tab', 'opinion')
            ->setEntityId($course->getId())
            ->generateUrl();
    }

    public function createIndexQueryBuilder(SearchDto $searchDto, EntityDto $entityDto, FieldCollection $fields, FilterCollection $filters): QueryBuilder
    {
        $qb = $this->get(EntityRepository::class)->createQueryBuilder($searchDto, $entityDto, $fields, $filters);
        $qb->andWhere('entity.translation is null');

        $query = $searchDto->getQuery();
        if (!empty($query)) {
            // Not working if I try to filter using the main query (no idea why).
            // Workaround: find all translations that includes the requested query
            // and use the response id's as parameter for IN condition

            $translations = $this->em->getRepository(Course::class)->createQueryBuilder('ct')
                ->select('distinct(t1.id) as id')
                ->join('ct.translation', 't1')
                ->andWhere('ct.translation IS NOT NULL')
                ->where('LOWER(ct.code) LIKE :query OR LOWER(ct.name) LIKE :query OR LOWER(ct.description) LIKE :query')
                ->setParameter('query', "%$query%")
                ->getQuery()
                ->getResult();

            $ids = [];
            foreach ($translations as $t) {
                $ids[] = $t['id'];
            }
            if (\count($ids) > 0) {
                $qb->orWhere($qb->expr()->in('entity.id', $ids));
            }
        }

        $qb->leftJoin('entity.typeCourse', 'tp')
            ->andWhere('tp.active = 1')
            ->andWhere('tp.denomination = :denomination')
            ->setParameter('denomination', EnumTypeCourse::INTERN);

        if (!\in_array('ROLE_ADMIN', $this->getUser()->getRoles())) {
            $qb->andWhere('entity.createdBy = :user or :user MEMBER OF entity.managers');

            $qb->setParameter('user', $this->getUser());

            return $qb;
        }

        return $qb;
    }

    public function translateAction(AdminContext $context)
    {
        $id = $context->getRequest()->query->get('entityId');
        $locale = ($context->getRequest()->query->get('language')) ? $context->getRequest()->query->get('language') : 'pt';

        $entity = $this->em->getRepository(Course::class)->find($id);
        $translate = clone $entity;
        $translate->setLocale($locale);
        $translate->setTranslation($entity);
        $this->em->persist($translate);
        $this->em->flush();

        return $this->redirect($this->get(AdminUrlGenerator::class)->setAction(Action::DETAIL)->generateUrl());
    }

    public function createEntity(string $entityFqcn)
    {
        $course = new Course();

        if (!$this->settings->get('app.multilingual')) {
            $course->setLocale($this->settings->get('app.defaultLanguage'));
        }
        if (!$this->settings->get('app.setCoursePoints')) {
            $course->setPoints($this->settings->get('app.courseDefaultPoints'));
        }

        /**
         * Adding default season in Course.
         */
        $season = new Season();
        $season->setName('Init');
        $season->setSort(1);

        $course->addSeason($season);
        $course->setIsNew(false);

        return $course;
    }

    /**
     * @Route("/course/check-locale/{course}", name="course-check-locale")
     */
    public function checkLocale(Request $request, Course $course)
    {
        $this->logger->error('curso: ' . $course->getId());

        $locale = $request->get('locale');

        $this->logger->error('locale: ' . $locale);

        $translation = $entity = $this->em
            ->getRepository(Course::class)
            ->findOneBy(
                [
                    'translation' => $course,
                    'locale' => $locale,
                ]
            );

        if ($course->getLocale() == $locale || !\is_null($translation)) {
            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'message' => $this->translator->trans('course.configureFields.translate_already', [], 'messages', $this->getUser()->getLocale()),
            ];
        } else {
            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
            ];
        }

        return $this->sendResponse($response);
    }

    protected function checkEntityAccess()
    {
        if (!\in_array('ROLE_ADMIN', $this->getUser()->getRoles())) {
            /**
             * @var $course Course
             */
            $course = $this->context->getContext()->getEntity()->getInstance();
            if ($course->getCreatedBy() != $this->getUser()) {
                $this->addFlash('danger', 'You have not access to this course');

                return false;
            }
        }

        return true;
    }

    /**
     * @Route("/course/clone-categories/{course}", name="course-clone-categories")
     */
    public function cloneCategories(Request $request, Course $course)
    {
        $categories = $course->getCategories();

        $courses_to = [771, 772, 776, 846, 778, 779, 780, 781, 773, 774, 775, 805, 806, 807];

        foreach ($courses_to as $courseToId) {
            $courseTo = $this->em->getRepository(Course::class)->find($courseToId);
            foreach ($categories as $category) {
                $courseTo->addCategory($category);
            }
            $this->em->persist($courseTo);
            $this->em->flush();
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/course/all-categories/{course}", name="course-all-categories")
     */
    public function assignAllCategories(Request $request, Course $course)
    {
        $categories = $this->em->getRepository(ProfessionalCategory::class)->findAll();

        foreach ($categories as $category) {
            $course->addCategory($category);
        }
        $this->em->persist($course);
        $this->em->flush();

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Route("/course/all-to-category/{category}", name="category-all-courses")
     */
    public function assignCategorieToAllCourses(Request $request, ProfessionalCategory $category)
    {
        $courses = $this->em->getRepository(Course::class)->findBy(['active' => 1]);

        foreach ($courses as $course) {
            $course->addCategory($category);
            $this->em->persist($course);
            $this->em->flush();
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Rest\Post("/course/save-filters")
     */
    public function saveCourseFilters(Request $request, CourseRepository $courseRepository): Response
    {
        try {
            $content = json_decode($request->getContent(), true);
            $id = $content['id'] ?? null;
            $filters = $content['filters'] ?? [];
            if (empty($id)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => 'id is required (Course)',
                ]);
            }
            $course = $courseRepository->find($id);
            if (!$course) {
                return $this->sendResponse([
                    'status' => Response::HTTP_NOT_FOUND,
                    'error' => true,
                    'data' => 'Course not found',
                ]);
            }
            $courseRepository->setCourseFiltersFromIdsArray($course, $filters);
            $this->em->persist($course);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'SAVED',
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Post("/course/save-course-managers")
     */
    public function saveCourseManagers(Request $request, CourseRepository $courseRepository): Response
    {
        try {
            $content = json_decode($request->getContent(), true);
            $id = $content['id'] ?? null;
            $managers = $content['managers'] ?? [];
            if (empty($id)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => 'id is required (Course)',
                ]);
            }
            $course = $courseRepository->find($id);
            if (!$course) {
                return $this->sendResponse([
                    'status' => Response::HTTP_NOT_FOUND,
                    'error' => true,
                    'data' => 'Course not found',
                ]);
            }
            $courseRepository->setCourseManagersFromArray($course, $managers);
            $this->em->persist($course);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'SAVED',
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Get("/course/{id}/professional-categories")
     */
    public function getCourseProfessionalCategories(Course $course): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->em->getRepository(ProfessionalCategory::class)->createQueryBuilder('pc')
                ->select('pc.id', 'pc.name', 'pc.code')
                ->join('pc.courses', 'c')
                ->where('c =:course')
                ->setParameter('course', $course)
                ->getQuery()
                ->getResult(),
        ]);
    }

    /**
     * @Rest\Post("/course/save-professional-categories")
     */
    public function saveCourseProfessionalCategories(Request $request, CourseRepository $courseRepository): Response
    {
        try {
            $content = json_decode($request->getContent(), true);
            $id = $content['id'] ?? null;
            $categories = $content['categories'] ?? [];
            if (empty($id)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => 'id is required (Course)',
                ]);
            }
            $course = $courseRepository->find($id);
            if (!$course) {
                return $this->sendResponse([
                    'status' => Response::HTTP_NOT_FOUND,
                    'error' => true,
                    'data' => 'Course not found',
                ]);
            }
            $courseRepository->setCourseProfessionalCategoriesFromArray($course, $categories);
            $this->em->persist($course);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'SAVED',
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    public function accessLevelAction(AdminContext $context): Response
    {
        $id = $context->getEntity()->getInstance()->getId();
        $keyValueStore = KeyValueStore::new([]);
        $keyValueStore = $this->configureAppResponseParameters(
            $keyValueStore,
            $this->settings,
            $this->context,
            $this->jwt,
            [
                'id' => $id,
            ],
            [
                'courseUrl' => $this->adminUrlGenerator->setEntityId($id)->setController(CourseCrudController::class)->setAction(Crud::PAGE_DETAIL)->generateUrl(),
                'useFilters' => $this->settings->get('app.user.useFilters'),
            ],
            'CourseAccessLevel'
        );

        return $this->render('admin/course/app.html.twig', $keyValueStore->all());
    }

    /**
     * @Route ("/courses/save-access-level", name="admin_courses_save_access_level")
     *
     * @return Response
     */
    public function saveAccessLevel(Request $request, CourseRepository $courseRepository, UserRepository $userRepository, ProfessionalCategoryRepository $professionalCategoryRepository, FilterRepository $filterRepository)
    {
        $content = json_decode($request->getContent(), true);
        $course = $courseRepository->find($content['course']);

        foreach ($course->getManagers() as $manager) {
            $course->removeManager($manager);
        }

        foreach ($content['managers'] as $manager) {
            $course->addManager($userRepository->find($manager['id']));
        }

        foreach ($course->getCategories() as $professionalCategory) {
            $course->removeCategory($professionalCategory);
        }

        foreach ($content['professionalCategories'] as $professionalCategory) {
            $course->addCategory($professionalCategoryRepository->find($professionalCategory['id']));
        }

        $this->em->flush();

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [],
        ];

        return $this->sendResponse($response);
    }

    /**
     * Access level.
     */

    /**
     * @Rest\Get("/courses/{course}/load-filters/{category}", name="get_course_filters_per_category")
     */
    public function courseFilters(Course $course, FilterCategory $category): Response
    {
        $filters = [];

        foreach ($course->getFilters() as $filter) {
            if ($filter->getFilterCategory()->getId() == $category->getId()) {
                $filters[] = $filter;
            }
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $filters,
        ], ['groups' => ['only_filter']]);
    }

    /**
     * @Rest\Post("/courses/{course}/filter/{filter}")
     */
    public function addFilterToCourse(Course $course, Filter $filter): Response
    {
        $course->addFilter($filter);
        $this->em->persist($course);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
            'message' => \sprintf($this->translator->trans('filter.added_filter', [], 'messages', $this->getUser()->getLocale()), $filter->getName()),
        ]);
    }

    /**
     * @Rest\Delete("/courses/{course}/filter/{filter}")
     */
    public function removeFilterFromCourse(Course $course, Filter $filter): Response
    {
        $course->removeFilter($filter);
        $this->em->persist($course);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'message' => \sprintf($this->translator->trans('filter.removed_filter', [], 'messages', $this->getUser()->getLocale()), $filter->getName()),
        ]);
    }

    /**
     * @Route("/tags.json", name="tags", defaults={"_format": "json"})
     */
    public function tagsAction(Request $request)
    {
        // $search = $request->get('search');
        $tags = $this->em->getRepository(Tag::class)->findBy([], ['name' => 'ASC']);

        return $this->render('admin/course/tags.json.twig', ['tags' => $tags]);
    }

    /**
     * @Route("/tags", name="tags",methods={"GET"})
     *
     * @return Response
     */
    public function getTag()
    {
        $tag = $this->em->getRepository(Tag::class)->findAll();

        foreach ($tag as $t) {
            $tags[] =
                $t->getName();
        }

        $response = [
            'status' => 200,
            'error' => false,
            'data' => $tags,
        ];

        return $this->sendResponse($response, ['groups' => ['detail']]);
    }

    // DERELICT CODE - TODO
    /**
     * @Route("/course_data_export", name="download_course_stats", methods={"POST"})
     */
    public function downloadCourseStats(Request $request, UserCourseRepository $userCourseRepository): Response
    {
        $courseName = $this->em->getRepository(Course::class)->find($request->get('courseID'))->getName();
        $userCourseChapterRepository = $this->em->getRepository(UserCourseChapter::class);

        $conditions = [
            'courseID' => $request->get('courseID'),
            'dateFrom' => $request->get('dateFrom') ?? '',
            'dateTo' => $request->get('dateTo') ?? '',
            'courseStartedIntime' => $request->get('courseStartedIntime') ?? '',
            'courseFinishedIntime' => $request->get('courseFinishedIntime') ?? '',
            'customFilters' => $request->get('customFilters') ?? '',
        ];

        if ($this->settings->get('app.export.active_cron_exports')) {
            $meta['startDate'] = $request->get('dateFrom');
            $meta['endDate'] = $request->get('dateTo');
            $meta['courseID'] = $request->get('courseID');
            $meta['courseStartedIntime'] = $request->get('courseStartedIntime');
            $meta['courseFinishedIntime'] = $request->get('courseFinishedIntime');
            $meta['customFilters'] = $request->get('customFilters');

            $status = $this->taskService->enqueueTask(
                $this->getUser(),
                'export-file',
                $meta,
                'course-stats-export',
                $courseName
            );

            return $this->redirect($this->get(AdminUrlGenerator::class)->setAction(Action::DETAIL)->setEntityId($request->get('courseID'))->set('exportStatus', $status['status'])->generateUrl());
        }

        $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
        $userCourseData = $userCourseRepository->exportCourseInfo($conditions, $user);
        $course = $this->em->find(Course::class, $request->get('courseID'));

        $filterCategory = $this->em->getRepository(FilterCategory::class)->findAll();
        $categories = [];

        foreach ($filterCategory as $filCat) {
            array_push(
                $categories,
                $filCat->getName(),
            );
        }

        $headerExcel1 = ['fistName', 'lastName', 'email', 'active', 'code', 'CourseLocale'];
        $headerExcel1_gender = ['fistName', 'lastName', 'email', 'active', 'code', 'CourseLocale', 'gender'];
        $headerExcel2 = ['points', 'startedAt', 'finishedAt', 'timeSpent', 'announcementCreator'];

        if (!$this->settings->get('app.export.code')) {
            $headerExcel1 = array_diff($headerExcel1, ['code']);
            $headerExcel1_gender = array_diff($headerExcel1_gender, ['code']);
        }

        if ($this->settings->get('app.export.finishedChapters')) {
            $headerExcel2[] = 'finishedChapters';
        }

        $headExcel = $this->settings->get('app.export.gender_excel')
            ? array_merge($headerExcel1_gender, $categories, $headerExcel2) :
            array_merge($headerExcel1, $categories, $headerExcel2);

        list($spreadsheet, $sheet) = $this->newSpreadsheet('Course Data - Export', $headExcel);

        $row = 1;
        foreach ($userCourseData as $data) {
            $userId = $data['id'];
            if (\is_null($userId)) {
                continue;
            }
            $userFind = $this->em->getRepository(User::class)->find($userId);

            foreach ($filterCategory as $filCat) {
                $filtersUser = $this->em->getRepository(Filter::class)->fetchFiltersUsers($userFind->getId(), $filCat->getId());
                $data['filtro_' . $filCat->getId()] = '' != $filtersUser ? $filtersUser['name'] : '';
            }

            $data['points_'] = $data['points'];
            $data['startedAt_'] = $data['startedAt'];
            $data['finishedAt_'] = $data['finishedAt'];
            $data['timeSpent_'] = gmdate('H:i:s', $data['timeSpent']);
            $data['announcementCreator_'] = $data['announcementCreator'];
            $data['active'] = $data['active'] ? 'Yes' : 'No';

            if (!$this->settings->get('app.export.gender_excel')) {
                unset($data['gender']);
            }
            if (!$this->settings->get('app.export.code')) {
                unset($data['code']);
            }

            if ($this->settings->get('app.export.finishedChapters')) {
                $courseToFinish = $this->em->find(Course::class, $data['courseId']);
                if (!\is_null($courseToFinish)) {
                    $data['finishedChapters'] = $userCourseChapterRepository->countByUserAndCourse($userFind, $courseToFinish, true);
                } else {
                    $data['finishedChapters'] = '0';
                }
            }

            unset($data['courseId']);
            unset($data['id']);
            unset($data['points']);
            unset($data['startedAt']);
            unset($data['finishedAt']);
            unset($data['timeSpent']);
            unset($data['announcementCreator']);

            $sheet->fromArray([$data], null, 'A' . ++$row);
        }

        foreach (range('A', 'T') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        // config sheet
        $spreadsheet->createSheet();
        $spreadsheet->setActiveSheetIndex(1);
        $this->statsService->generateExcelConfigSheet($spreadsheet, 'Stats Config', $conditions);

        // $course = substr($this->removeSpecialChar($courseName), 0, 20);
        $course = substr(CharactersCurationUtils::removeSpecialChar($courseName), 0, 20);

        $fileName = "stats_{$course}_" . (new \DateTime())->format('YmdHis') . '.xlsx';
        $tempFile = tempnam(sys_get_temp_dir(), $fileName);
        $writer = new Xlsx($spreadsheet);
        $writer->save($tempFile);

        return $this->file($tempFile, $fileName, ResponseHeaderBag::DISPOSITION_INLINE);
    }

    private function newSpreadsheet($title, $headers): array
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle($title);
        $sheet->fromArray($headers);

        return [$spreadsheet, $sheet];
    }

    /**
     * @Route("/course/export-catalog", name="export_course_catalog")
     */
    public function exportCourseCatalog(Request $request): Response
    {
        $filename = 'Course catalog';

        return $this->sendResponse(
            $this->taskService->enqueueTask(
                $this->getUser(),
                'export-file',
                [],
                'course-catalog-export',
                $filename
            )
        );
    }

    private function removeSpecialChar($text): string
    {
        return preg_replace("/[^\w.-]/", '', strtr($text, [
            'Š' => 'S',
            'š' => 's',
            'Ž' => 'Z',
            'ž' => 'z',
            'À' => 'A',
            'Á' => 'A',
            'Â' => 'A',
            'Ã' => 'A',
            'Ä' => 'A',
            'Å' => 'A',
            'Æ' => 'A',
            'Ç' => 'C',
            'È' => 'E',
            'É' => 'E',
            'Ê' => 'E',
            'Ë' => 'E',
            'Ì' => 'I',
            'Í' => 'I',
            'Î' => 'I',
            'Ï' => 'I',
            'Ñ' => 'N',
            'Ò' => 'O',
            'Ó' => 'O',
            'Ô' => 'O',
            'Õ' => 'O',
            'Ö' => 'O',
            'Ø' => 'O',
            'Ù' => 'U',
            'Ú' => 'U',
            'Û' => 'U',
            'Ü' => 'U',
            'Ý' => 'Y',
            'Þ' => 'B',
            'ß' => 'Ss',
            'à' => 'a',
            'á' => 'a',
            'â' => 'a',
            'ã' => 'a',
            'ä' => 'a',
            'å' => 'a',
            'æ' => 'a',
            'ç' => 'c',
            'è' => 'e',
            'é' => 'e',
            'ê' => 'e',
            'ë' => 'e',
            'ì' => 'i',
            'í' => 'i',
            'î' => 'i',
            'ï' => 'i',
            'ð' => 'o',
            'ñ' => 'n',
            'ò' => 'o',
            'ó' => 'o',
            'ô' => 'o',
            'õ' => 'o',
            'ö' => 'o',
            'ø' => 'o',
            'ù' => 'u',
            'ú' => 'u',
            'û' => 'u',
            'ý' => 'y',
            'þ' => 'b',
            'ÿ' => 'y',
        ]));
    }

    /**
     * New methods for course mini app creation.
     */
    public function createCourseView(): Response
    {
        return $this->renderCourseApp();
    }

    public function editCourseAction(AdminContext $context): Response
    {
        $entityId = $context->getEntity()->getPrimaryKeyValue();

        return $this->renderCourseApp('UpdateCourse', [
            'id' => $entityId,
        ]);
    }

    /**
     * @param array  $routeParams Only if the $starterRoute route require specific params
     * @param string $origin      Based on app logic, origin can be used to decide actions before loading the route
     */
    private function renderCourseApp(string $activeRoute = 'CreateCourse', array $routeParams = [], string $origin = 'default'): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $localeNames = Locales::getNames();
        $locales = [];
        foreach ($this->settings->get('app.languages') as $locale) {
            $locales[$locale] = $localeNames[$locale];
        }

        $categoryFilters = $this->em->getRepository(FilterCategory::class)->findAll();

        $defaultFilterStatus = [];
        foreach ($categoryFilters as $category) {
            $defaultFilterStatus[] = [
                'id' => $category->getId(),
                'selected' => [],
            ];
        }

        $routeStatus = [];

        return $this->render('admin/course/course_app.html.twig', [
            'user' => $this->getUser(),
            'locales' => $locales,
            'defaultLocale' => $this->settings->get('app.defaultLanguage'),
            'userLocale' => $user->getLocale(),
            'froalaKey' => $this->settings->get('app.froalaKey'),

            'activeRoute' => $activeRoute,
            'activeParams' => base64_encode(json_encode(array_merge($routeParams, ['origin' => $origin]))),
            'routeStatus' => base64_encode(json_encode($routeStatus)),
            'config' => base64_encode(json_encode([
                'defaultFilterStatus' => $defaultFilterStatus,
                'filtersEnabled' => $this->settings->get('app.user.useFilters'),
            ])),
            'jwt' => $this->getJwt($this->jwt, $this->settings, $user),
            'app_permissions_manager_canPublish' => $this->settings->get('app.permissions.manager.canPublish'),
            'diploma_index_allow' => $this->settings->get('app.content.diploma'),
        ]);
    }

    /**
     * @Rest\Get("/course/pre-data")
     */
    public function newCoursePreData(): Response
    {
        // 'categories' => $this->em->getRepository(CourseCategory::class)->getCourseCategoriesTypeCourse(),
        // $this->em->getRepository(TypeDiploma::class)->findBy(['active' => true])
        $data = [
            'categories' => $this->em->getRepository(CourseCategory::class)->getListCourseCategoryTranslate($this->getUser()->getLocale()),
            'multilingual' => $this->settings->get('app.multilingual'),
            'setCoursePoints' => $this->settings->get('app.setCoursePoints'),
            'points' => $this->settings->get('app.courseDefaultPoints'),
            'useSegment' => $this->settings->get('app.useSegment'),
            'setCourseLevel' => $this->settings->get('app.setCourseLevel'),
            'courseDocumentation' => $this->settings->get('app.courseInfoGeneral'),
            'typesCourse' => $this->em->getRepository(TypeCourse::class)->getActiveTranslatedList($this->getUser()->getLocale()),
            'surveysCourse' => $this->em->getRepository(Survey::class)->getActiveTranslatedList($this->getUser()->getLocale()),
            'typeDiplomas' => $this->em->getRepository(TypeDiploma::class)->getActiveAndApplyToCurseAndAll(),
            'typeIndexDiploma' => [DiplomaType::DEFAULT, DiplomaType::MANUAL],
            'isCustomizedDiploma' => $this->settings->get('app.content.diploma'),
        ];
        if ($this->settings->get('app.useSegment')) {
            $data['segments'] = $this->em->getRepository(CourseSegment::class)->findAll();
        }
        if ($this->settings->get('app.setCourseLevel')) {
            $data['levels'] = $this->em->getRepository(CourseLevel::class)->findAll();
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ], ['groups' => ['list']]);
    }

    /**
     * @Rest\Get("/course/{id}/filters", name="admin_get_course_filters")
     */
    public function getCourseFilters(Course $course): Response
    {
        $locale = $this->getUser()->getLocale();

        $filters = $this->em->getRepository(Course::class)
            ->createQueryBuilder('c')
            ->select('f.id', 'COALESCE(t.name, f.name) AS name', 'cat.id AS categoryId')
            ->join('c.filters', 'f')
            ->join('f.filterCategory', 'cat')
            ->leftJoin('f.translations', 't', 'WITH', 't.locale = :locale')
            ->where('c = :course')
            ->setParameter('course', $course)
            ->setParameter('locale', $locale)
            ->getQuery()
            ->getResult();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $filters,
        ]);
    }

    /**
     * @Rest\Get("/course/{id}", name="admin_get_course")
     */
    public function getCourseData(Course $course): Response
    {
        $surveyCourse = $this->em->getRepository(SurveyCourse::class)->findOneBy(['course' => $course]);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $course,
            'survey' => \is_null($surveyCourse) ? null : $surveyCourse->getSurvey(),
            'isOriginalCourse' => null === $course->getTranslation(),
            'isCustomizedDiploma' => $this->settings->get('app.content.diploma') && null === $course->getTranslation(),
        ], ['groups' => ['update-course']]);
    }

    /**
     * @Rest\Get("/course/{id}/managers")
     */
    public function getCourseManagers(Course $course): Response
    {
        $managers = $course->getManagers();
        $data = [];
        foreach ($managers as $manager) {
            $data[] = [
                'id' => $manager->getId(),
                'fullName' => $manager->getFullName(),
                'email' => $manager->getEmail(),
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Rest\Post("/course/update/{id}")
     */
    public function updateCourse(Request $request, Course $course, AuthorizationCheckerInterface $authorizationChecker): Response
    {
        if (!$authorizationChecker->isGranted(CourseVoter::UPDATE, $course)) {
            return $this->sendResponse([
                'status' => Response::HTTP_FORBIDDEN,
                'error' => true,
                'message' => 'You do not have permission to update courses.',
            ]);
        }
        try {
            $msg = $this->translator->trans('course.message_saved', [], 'messages', $this->getUser()->getLocale());

            return $this->setCourseData($request, $course, $msg);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => false,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Post("/course", name="admin_create_course")
     */
    public function createCourse(Request $request, AuthorizationCheckerInterface $authorizationChecker): Response
    {
        try {
            $course = new Course();
            if (!$authorizationChecker->isGranted(CourseVoter::CREATE, $course)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_FORBIDDEN,
                    'error' => true,
                    'message' => 'You do not have permission to create courses.',
                ]);
            }
            $course->setIsNew(false);
            $this->em->persist($course);
            $this->newSeasonCourse($course);
            $msg = $this->translator->trans('course.message_saved', [], 'messages', $this->getUser()->getLocale());

            return $this->setCourseData($request, $course, $msg);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => false,
                'data' => $e->getMessage(),
            ]);
        }
    }

    private function newSeasonCourse(Course $course)
    {
        $season = new Season();
        $season->setName('Init');
        $season->setSort(1);

        $course->addSeason($season);
        $course->setIsNew(false);

        $this->em->persist($season);
    }

    /**
     * @throws \Exception
     */
    private function setCourseData(Request $request, Course $course, string $msg = ''): Response
    {
        if (!($name = $request->get('name'))) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => 'Name field is required',
            ]);
        }
        if (!($code = $request->get('code'))) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => 'Code field is required',
            ]);
        }
        if (!($locale = $request->get('locale'))) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => 'Locale field is required',
            ]);
        }

        if (!is_null($request->get('duration')) && !is_int((int)$request->get('duration'))) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'message' => 'Duration must be an integer or null.',
            ]);
        }

        $course->setCode($code)
            ->setName($name)
            ->setLocale($locale)
            ->setDescription($request->get('description'))
            ->setActive('true' === $request->get('active', false))
            ->setOpen('true' === $request->get('open', false))
            ->setIsNew('true' === $request->get('isNew', false))
            ->setOpenVisible('true' === $request->get('open-visible', false))
            ->setGeneralInformation($request->get('general-information'))
            ->setIsContentDiploma('true' == $request->get('is-content-diploma'), false)
            ->setDuration($request->get('duration'));

        if ('true' == $request->get('is-content-diploma') && $this->settings->get('app.content.diploma')) {
            $course->setTypeIndexDiploma($request->get('diploma-index'));
            if (DiplomaType::MANUAL == $request->get('diploma-index')) {
                $course->setDescriptionContentDiploma($request->get('description-content-diploma'));
            }
        } else {
            $course->setTypeIndexDiploma(null);
            $course->setDescriptionContentDiploma(null);
        }

        $categoryId = $request->get('category');
        if (!empty($categoryId)) {
            $course->setCategory($this->em->getRepository(CourseCategory::class)->find($categoryId));
        }

        $survey_id = $request->get('survey');
        $typeDiploma = $request->get('typeDiploma');
        // dd($survey_id, $typeDiploma);
        if (!empty($typeDiploma)) {
            $course->setTypeDiploma($this->em->getRepository(TypeDiploma::class)->find($typeDiploma));
        }

        if (!\is_null($survey_id)) {
            $survey = $this->em->getRepository(Survey::class)->find($survey_id);
            if ($survey) {
                $surveyCourses = $this->em->getRepository(SurveyCourse::class)->findBy(['course' => $course]);
                foreach ($surveyCourses as $surveyCourse) {
                    $this->em->remove($surveyCourse);
                }

                $surveyCourse = new SurveyCourse();
                $surveyCourse->setSurvey($survey);
                $surveyCourse->setCourse($course);
                $this->em->persist($surveyCourse);
            }
        }

        $typeCourseId = $request->get('typeCourse') ? $request->get('typeCourse') : 1;
        $course->setTypeCourse($this->em->getRepository(TypeCourse::class)->find($typeCourseId));

        if (!$this->settings->get('app.multilingual')) {
            $course->setLocale($this->settings->get('app.defaultLanguage'));
        }
        if (!$this->settings->get('app.setCoursePoints')) {
            $course->setPoints($this->settings->get('app.courseDefaultPoints'));
        }
        if ($this->settings->get('app.setCoursePoints') && ($points = $request->get('points', null)) && $points > 0) {
            $course->setPoints(\intval($points) > 0 ? \intval($points) : $this->settings->get('app.courseDefaultPoints'));
        }
        if ($this->settings->get('app.useSegment') && ($segmentsJson = $request->get('segments'))) {
            $segments = json_decode($segmentsJson, true);
            foreach ($segments as $segment) {
                $course->addCourseSegment($this->em->getRepository(CourseSegment::class)->find($segment['id']));
            }
        }

        if ($this->settings->get('app.setCourseLevel') && ($courseLevel = $request->get('level'))) {
            $course->setLevel($this->em->getRepository(CourseLevel::class)->find($courseLevel));
        }

        if ($this->settings->get('app.courseInfoGeneral') && ($documentation = $request->get('documentation'))) {
            $course->setDocumentation($documentation);
        }

        /** @var UploadedFile|null $thumbnailFile */
        $thumbnailFile = $request->files->get('thumbnail');
        if ($thumbnailFile) {
            $filename = $this->imageUploaderHandler->handleImage($thumbnailFile, '/uploads/images/course');
            $course->setImage($filename);
        }

        $sFilters = $request->get('filters');
        if (!empty($sFilters)) {
            if (\is_array($sFilters)) {
                $decoded = $sFilters;
            } else {
                $decoded = json_decode($sFilters, true);
            }
            $ids = $decoded;
            $filters = $this->em->getRepository(Filter::class)
                ->createQueryBuilder('f')
                ->where('f.id IN (:ids)')
                ->setParameter('ids', $ids)
                ->getQuery()
                ->execute();
            $course->setFilters($filters);
        }

        // Handle tags
        $jsonTags = $request->get('tags');
        $tags = [];

        if (!empty($jsonTags)) {
            $sTags = json_decode($jsonTags, true);
            foreach ($sTags as $sTag) {
                $tag = new Tag();
                $tag->setName($sTag)->setSlug($sTag);
                $tags[] = $tag;
            }
        }

        $course->setTags($tags);

        $jsonManager = $request->get('managers');
        if (!empty($jsonManager)) {
            if (\is_array($jsonManager)) {
                $managers = $jsonManager;
            } else {
                $managers = json_decode($jsonManager, true);
            }
            $ids = [];
            foreach ($managers as $m) {
                $ids[] = $m['id'];
            }
            $managers = $this->em->getRepository(User::class)->createQueryBuilder('u')
                ->where('u.id IN (:ids)')
                ->setParameter('ids', $ids)
                ->getQuery()
                ->getResult();

            $course->setManagers($managers);
        }
        $this->em->persist($course);
        $this->em->flush();

        $url = $this->adminUrlGenerator->setController(self::class)
            ->setAction(Crud::PAGE_DETAIL)
            ->setEntityId($course->getId())
            ->generateUrl();

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
            'data' => [
                'message' => $msg,
                'redirect' => $url,
                'courseData' => ['id' => $course->getId(), 'name' => $course->getName()],
            ],
        ]);
    }

    /**
     * @Route("/course/filters/imq", name="course-filters-imq")
     */
    public function assignCoursesToFilters(Request $request)
    {
        // IGURCO
        $courses_id = [50, 71, 119, 163, 183, 260, 336, 341, 348, 349, 420, 456, 436, 430];
        $filters_id = [1, 21, 24, 26, 34];

        // PREVENCION
        $courses_id = [188, 171, 71, 24, 146, 82];
        $filters_id = [250];

        // AMSA
        $courses_id = [24, 71, 84, 403, 409, 416, 422];
        $filters_id = [229];

        // CLINICA
        //        $courses_id = [24, 71, 84, 105, 106, 111, 121, 133, 135, 138, 142, 184, 193, 203, 204, 205, 206, 207, 209, 210, 211, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 230, 233, 250, 263, 264, 266, 364];
        //        $filters_id = [309, 311, 313, 315, 316, 317, 318, 319, 323, 324, 325, 329, 330, 331, 332];
        //
        // SEGUROS
        //        $courses_id = [1, 5, 18, 24, 29, 31, 45, 51, 71, 73, 81, 143, 191, 271, 306, 308, 309, 310, 311, 312, 313, 315, 320, 321, 323, 325, 326, 327, 328, 329, 330, 333, 338, 340, 367, 425, 448, 449, 450, 453, 454, 455, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 488, 489];
        //        $filters_id = [38, 43, 177, 182, 189, 195, 198, 199, 204, 212];

        // CATXXI
        $courses_id = [396, 393, 391, 386, 382, 71];
        $filters_id = [300];

        $filters = [];
        foreach ($filters_id as $id) {
            $filter = $this->em->getRepository(Filter::class)->find($id);

            $filters[] = $filter;
        }

        foreach ($courses_id as $id) {
            /**
             * @var $course Course
             */
            $course = $this->em->getRepository(Course::class)->findOneBy(['code' => 'IMPORT-' . $id]);

            $this->logger->error($id);
            $this->logger->error($course->getId());

            foreach ($filters as $filter) {
                $course->addFilter($filter);
            }

            $this->em->flush();
        }

        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
        ];

        return $this->sendResponse($response);
    }

    /**
     * @Rest\Get("/courses/available")
     */
    public function getAvailableCourses(): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->em->getRepository(Course::class)->createQueryBuilder('c')
                ->select('c.id', 'c.name', 'c.code', 'c.image')
                ->andWhere('c.translation is null')
                ->getQuery()
                ->getResult(),
        ]);
    }

    /**
     * @Rest\Get("/courses/survey-and-filter-available")
     */
    public function getSurveyAndFiltersAvailable(): Response
    {
        $surveyAvailable = $this->settings->get('app.course.survey');
        $filtersCompletedCourseAndSurvey = $this->settings->get('app.course.filters.diploma');

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'surveyAvailable' => $surveyAvailable,
            'filtersCompletedCourseAndSurvey' => $filtersCompletedCourseAndSurvey,
        ]);
    }

    /**
     * @Rest\Get("/course/{id}/filters-categories", name="admin_get_course_filters-categories")
     */
    public function getCourseFiltersCategories(Course $course): Response
    {
        $filtersCourse = [];
        $filters = $this->em->getRepository(FilterCategory::class)->findAll();
        if ($course->getFilters()) {
            foreach ($course->getFilters() as $filterCourse) {
                foreach ($filters as $filter) {
                    if ($filter->getId() == $filterCourse->getFilterCategory()->getId()) {
                        $categoryIndex = null;
                        foreach ($filtersCourse as $index => $category) {
                            if ($category['name'] === $filter->getName()) {
                                $categoryIndex = $index;
                                break;
                            }
                        }

                        if (null === $categoryIndex) {
                            $filtersCourse[] = [
                                'id' => $filter->getId(),
                                'name' => $filter->getName(),
                                'data' => [],
                            ];
                            $categoryIndex = array_key_last($filtersCourse);
                        }

                        $filtersCourse[$categoryIndex]['data'][] = [
                            'id' => $filterCourse->getId(),
                            'filter' => $filterCourse->getName(),
                        ];
                    }
                }
            }
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $filtersCourse,
        ]);
    }

    /**
     * @Rest\Get("/courses/available-tags")
     */
    public function getAvailableTags(Request $request): Response
    {
        $query = $request->get('query', '');
        $result = $this->em->getRepository(Tag::class)->createQueryBuilder('t')
            ->select('t.name')
            ->where('t.name LIKE :query')
            ->setParameter('query', "%$query%")
            ->getQuery()
            ->getResult();
        $data = [];
        foreach ($result as $r) {
            $data[] = $r['name'];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    public function actionGetSharedCourseUrl(AdminContext $adminContext, AdminUrlGenerator $adminUrlGenerator)
    {
        $courseId = $adminContext->getEntity()->getPrimaryKeyValue();
        $request = $adminContext->getRequest();
        $shortUrl = $this->getFrontCourseShortUrl($request, $courseId);

        return $this->redirect($adminUrlGenerator->setAction(Action::INDEX)->set('share', $shortUrl)->generateUrl());
    }

    /**
     * @Rest\Get("/course/{id}/share", name="api_admin_share_course")
     */
    public function shareCourseUrl(Request $request, Course $course): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->getFrontCourseShortUrl($request, $course->getId()),
        ]);
    }

    private function getFrontCourseShortUrl(Request $request, int $courseId): string
    {
        /** @var UrlShortenerRepository $urlShortenerRepository */
        $urlShortenerRepository = $this->em->getRepository(UrlShortener::class);

        return $urlShortenerRepository->getShortUrl($request, '/campus/course/' . $courseId);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     * Handle type course information
     *
     * @Rest\Post("/type-course/{id}/active")
     */
    public function changeTypeCourseActiveStatus(Request $request, TypeCourse $typeCourse): Response
    {
        $content = json_decode($request->getContent(), true);
        $active = $content['active'] ?? false;
        $typeCourse->setActive($active);
        $this->em->persist($typeCourse);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Get("/type-course/all")
     */
    public function getAllTypeCourses(): Response
    {
        $data = [];
        $typeCourses = $this->em->getRepository(TypeCourse::class)->findAll();
        foreach ($typeCourses as $type) {
            $translations = [];
            /** @var TypeCourseTranslation $translation */
            foreach ($type->getTranslations() as $translation) {
                $name = $translation->getName();
                $description = $translation->getDescription();
                $translations[] = [
                    'locale' => $translation->getLocale(),
                    'name' => $name,
                    'description' => $description,
                ];
            }
            $data[] = [
                'id' => $type->getId(),
                'name' => $type->getName(),
                'description' => $type->getDescription(),
                'active' => $type->isActive(),
                'denomitation' => $type->getDenomination(),
                'translations' => $translations,
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Post("/type-course/create")
     */
    public function createTypeCourse(Request $request): Response
    {
        $typeCourse = new TypeCourse();
        if (($result = $this->saveTypeCourse($request, $typeCourse)) instanceof Response) {
            return $result;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Post("/type-course/update")
     */
    public function updateTypeCourse(Request $request, TypeCourseRepository $typeCourseRepository): Response
    {
        $content = json_decode($request->getContent(), true);
        $id = $content['id'] ?? -1;
        $typeCourse = $typeCourseRepository->find($id);
        if (!$typeCourse) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND',
            ]);
        }

        if (($result = $this->saveTypeCourse($request, $typeCourse)) instanceof Response) {
            return $result;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    public function saveTypeCourse(Request $request, TypeCourse $typeCourse)
    {
        $content = json_decode($request->getContent(), true);
        $name = $content['name'] ?? null;
        $description = $content['description'] ?? null;
        $active = $content['active'] ?? false;

        $errors = [];
        if (empty($name)) {
            $errors[] = 'Name required';
        }
        if (\count($errors)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => $errors,
            ]);
        }
        $typeCourse->setName($name)
            ->setDescription($description)
            ->setActive($active);

        $translations = $content['translations'];
        foreach ($translations as $data) {
            $translation = $this->em->getRepository(TypeCourseTranslation::class)->findOneBy([
                'translatable' => $typeCourse,
                'locale' => $data['locale'],
            ]);
            $name = $data['name'] ?? null;
            $description = $data['description'] ?? null;
            if (empty($name) && empty($description)) {
                if ($translation) {
                    $this->em->remove($translation);
                }
                continue;
            }
            if (!$translation) {
                $translation = new TypeCourseTranslation();
                $translation->setTranslatable($typeCourse);
                $translation->setLocale($data['locale']);
            }
            $translation->setName($name)
                ->setDescription($description);
            $this->em->persist($translation);
        }

        $this->em->persist($typeCourse);
        $this->em->flush();

        return true;
    }

    public function getAllTypeCourse(): array
    {
        $typeCourseSelect = [];
        $typeCourses = $this->em->getRepository(TypeCourse::class)->findBy(['active' => true]);

        foreach ($typeCourses as $typeCourse) {
            $elemento = new \stdClass();
            $elemento->id = $typeCourse->getId();
            $elemento->name = $typeCourse->getName();

            array_push($typeCourseSelect, $elemento);
        }

        return $typeCourseSelect;
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Get("/translations-admin/all")
     */
    public function getAllTranslationsAdmin(): Response
    {
        $data = [];
        $translationsAdmins = $this->em->getRepository(TranslationsAdmin::class)->findAll();
        foreach ($translationsAdmins as $translationsAdmin) {
            $translations = [];
            /** @var TranslationsAdminTranslation $translation */
            foreach ($translationsAdmin->getTranslations() as $translation) {
                $name = $translation->getName();
                $description = $translation->getDescription();
                $translations[] = [
                    'locale' => $translation->getLocale(),
                    'name' => $name,
                    'description' => $description,
                ];
            }
            $data[] = [
                'id' => $translationsAdmin->getId(),
                'name' => $translationsAdmin->getName(),
                'description' => $translationsAdmin->getDescription(),
                'active' => $translationsAdmin->isActive(),
                'translations' => $translations,
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Post("/translations-admin/create")
     */
    public function createTranslationsAdmin(Request $request): Response
    {
        $translationsAdmin = new TranslationsAdmin();
        if (($result = $this->saveTranslationsAdmin($request, $translationsAdmin)) instanceof Response) {
            return $result;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
        ]);
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Post("/translations-admin/update")
     */
    public function updateTranslationsAdmin(Request $request): Response
    {
        $content = json_decode($request->getContent(), true);
        $id = $content['id'] ?? -1;
        $translationsAdmin = $this->em->getRepository(TranslationsAdmin::class)->find($id);
        if (!$translationsAdmin) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND',
            ]);
        }

        if (($result = $this->saveTranslationsAdmin($request, $translationsAdmin)) instanceof Response) {
            return $result;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
        ]);
    }

    public function saveTranslationsAdmin(Request $request, TranslationsAdmin $translationsAdmin)
    {
        $content = json_decode($request->getContent(), true);
        $name = $content['name'] ?? null;
        $description = $content['description'] ?? null;
        $active = $content['active'] ?? false;

        $errors = [];
        if (empty($name)) {
            $errors[] = 'Name required';
        }
        if (\count($errors)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => $errors,
            ]);
        }
        $translationsAdmin->setName($name)
            ->setDescription($description)
            ->setActive($active);

        $this->saveTranslationsAdminTranslation($content['translations'], $translationsAdmin);

        $this->em->persist($translationsAdmin);
        $this->em->flush();

        return true;
    }

    public function saveTranslationsAdminTranslation(array $translations, TranslationsAdmin $translationsAdmin)
    {
        foreach ($translations as $data) {
            $translation = $this->em->getRepository(TranslationsAdminTranslation::class)->findOneBy([
                'translatable' => $translationsAdmin,
                'locale' => $data['locale'],
            ]);
            $name = $data['name'] ?? null;
            $description = $data['description'] ?? null;

            if (empty($name) && empty($description)) {
                if ($translation) {
                    $this->em->remove($translation);
                }
                continue;
            }

            if (!$translation) {
                $translation = new TranslationsAdminTranslation();
                $translation->setTranslatable($translationsAdmin);
                $translation->setLocale($data['locale']);
            }
            $translation->setName($name)
                ->setDescription($description);
            $this->em->persist($translation);
        }
    }
}
