<?php

namespace App\Controller\Admin\Api;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\CourseCategoryTranslation;
use App\Entity\TypeCourse;
use App\Repository\CourseCategoryRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/admin/api/v1/course-categories")
 */
class CourseCategoryController extends AbstractController
{
    use SerializerTrait;

    private EntityManagerInterface $em;
    private SettingsService $settings;
    public function __construct(EntityManagerInterface $em, SettingsService $settings)
    {
        $this->em = $em;
        $this->settings = $settings;
    }

//    public function index(): Response
//    {
//        return $this->render('admin/course_category/index.html.twig', []);
//    }



    /**
     * @Rest\Get
     * @param Request $request
     * @param CourseCategoryRepository $courseCategoryRepository
     * @return Response
     */
    public function getCategories(Request $request, CourseCategoryRepository $courseCategoryRepository): Response
    {
        $query = $request->get('query', null);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $courseCategoryRepository->getCourseCategoryByQuery($query)
        ]);
    }

    /**
     * @Rest\Get("/{id}")
     * @param int $id
     * @param CourseCategoryRepository $courseCategoryRepository
     * @return Response
     */
    public function getCategory(int $id, CourseCategoryRepository $courseCategoryRepository): Response
    {
//        return $this->sendResponse(['status' => Response::HTTP_OK, 'data' => $id]);
        $courseCategory = $courseCategoryRepository->find($id);
        if (!$courseCategory) return $this->sendResponse([
            'status' => Response::HTTP_NOT_FOUND,
            'error' => true,
            'data' => 'Not found'
        ]);

        $translations = [];
        /** @var CourseCategoryTranslation $translation */
        foreach ($courseCategory->getTranslations() as $translation) {
            $translations[] = [
                'locale' => $translation->getLocale(),
                'name' => $translation->getName() ?? '',
                'description' => $translation->getDescription() ?? ''
            ];
        }

        $typeCourses = [];
        foreach ($this->em->getRepository(TypeCourse::class)->findBy(['active' => true]) as $typeCourse) {
            $typeCourses[$typeCourse->getId()] = false;
        }
        foreach ($courseCategory->getTypeCourse() as $tc) {
            $typeCourses[$tc->getId()] = true;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'category' => [
                    'id' => $courseCategory->getId(),
                    'name' => $courseCategory->getName(),
                    'description' => $courseCategory->getDescription(),
                ],
                'orderType' => $courseCategory->getOrderType(),
                'orderProperties' => $courseCategory->getOrderProperties(),
                'translations' => $translations,
                'typeCourses' => $typeCourses
            ]
        ]);
    }

    /**
     * @Rest\Put
     * @param Request $request
     * @return Response
     */
    public function updateCourseCategory(Request $request): Response
    {
        $content = \json_decode($request->getContent(), true);
        $id = $content['id'] ?? -1;
        $courseCategory = $this->em->getRepository(CourseCategory::class)->find($id);
        if (!$courseCategory) return $this->sendResponse([
            'status' => Response::HTTP_NOT_FOUND,
            'error' => true,
            'data' => 'Not found'
        ]);

        $result = $this->setCourseCategoryData($courseCategory, $content);

        return $this->sendResponse([
            'status' => $result['error'] ? Response::HTTP_ACCEPTED : Response::HTTP_OK,
            'error' => $result['error'],
            'data' => $result['data']
        ]);
    }

    /**
     * @Rest\Post
     * @param Request $request
     * @return Response
     */
    public function createCourseCategory(Request $request): Response 
    {
        $courseCategory = new CourseCategory();
        $content = \json_decode($request->getContent(), true);
        $result = $this->setCourseCategoryData($courseCategory, $content);
        return $this->sendResponse([
            'status' => $result['error'] ? Response::HTTP_ACCEPTED : Response::HTTP_CREATED,
            'error' => $result['error'],
            'data' => $result['data']
        ]);
    }

    private function setCourseCategoryData(CourseCategory $category, array $data): array
    {
        $name = $data['name'] ?? null;
        $description = $data['description'] ?? null;
        $orderType = $data['orderType'] ?? CourseCategory::ORDER_TYPE_AUTO;

        $translations = $data['translations'] ?? [];
        $orderProperties = $data['orderProperties'] ?? [];
        $typeCourses = $data['typeCourses'] ?? [];

        foreach ($translations as $t) {
            $tLocale = $t['locale'] ?? null;
            $tName = $t['name'] ?? null;
            $tDescription = $t['description'] ?? null;
            $translation = $this->em->getRepository(CourseCategoryTranslation::class)->findOneBy([
                'translatable' => $category,
                'locale' => $tLocale
            ]);

            if (empty($tName) && empty($tDescription)) {
                if ($translation) $this->em->remove($translation);
                continue;
            }

            if (!$translation) {
                $translation = new CourseCategoryTranslation();
                $translation->setTranslatable($category);
                $translation->setLocale($tLocale);
            }

            $translation->setName($tName)
                ->setDescription($tDescription)
            ;

            $this->em->persist($translation);
        }

        $category->setName($name)
            ->setDescription($description)
            ->setOrderProperties($orderProperties)
            ->setOrderType($orderType)
        ;

        $selectedTypeCourses = [];
        /**
         * @var int $typeCourseId
         * @var bool $enabled
         */
        foreach ($typeCourses as $typeCourseId => $enabled) {
            if (!$enabled) continue;
            $selectedTypeCourse = $this->em->getRepository(TypeCourse::class)->find($typeCourseId);
            if ($selectedTypeCourse) $selectedTypeCourses[] = $selectedTypeCourse;
        }

        $category->setTypeCourse($selectedTypeCourses);

        $this->em->persist($category);

        if ($orderType === CourseCategory::ORDER_TYPE_MANUAL) {
            $coursesOrder = $data['coursesOrder'] ?? [];
            foreach ($coursesOrder as $courseId => $sort) {
                $course = $this->em->getRepository(Course::class)->find($courseId);
                if (!$course) continue;
                $course->setSort($sort);
                $this->em->persist($course);
            }
        }

        $this->em->flush();
        return ['error' => false, 'data' => $category->getId()];
    }

    /**
     * @Rest\Get("/{id}/courses-to-order")
     * @param CourseCategory $category
     * @return Response
     */
    public function getAllCourses(CourseCategory $category): Response
    {
        $data = $this->em->getRepository(Course::class)->getCoursesToOrder($category) ;

        foreach ($data as &$d) {
            $d['typeCourseIcon'] = TypeCourse::ICONS[$d['typeCourseId']];
            $d['typeCourseType'] = TypeCourse::TYPES[$d['typeCourseId']];
            $d['image'] = empty($d['image']) ? '/assets/common/add_image.svg' : '/' . $this->settings->get('app.course_uploads_path') . '/' . $d['image'];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data
        ]);
    }


    /**
     * @Rest\Delete("/{id}")
     * @IsGranted("ROLE_ADMIN")
     * @param CourseCategory $courseCategory
     * @return Response
     */
    public function deleteCourseCategory(CourseCategory $courseCategory): Response
    {
        $courses = $this->em->getRepository(Course::class)->findOneBy(['category' => $courseCategory]);
        if ($courses) return $this->sendResponse([
            'status' => Response::HTTP_ACCEPTED,
            'error' => true,
            'data' => "No se puede eliminar la categoría {$courseCategory->getName()}, tiene cursos vinculados"
        ]);

        $this->em->remove($courseCategory);
        $this->em->flush();
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false
        ]);
    }

    /**
     * @Rest\Post("/update-order")
     * @param Request $request
     * @return Response
     */
    public function updateCourseCategoryOrder(Request $request): Response
    {
        $content = \json_decode($request->getContent(), true);

        /**
         * @var int $categoryId
         * @var int $sort
         */
        foreach ($content as $categoryId => $sort) {
            $category = $this->em->getRepository(CourseCategory::class)->find($categoryId);
            if ($category) {
                $category->setSort($sort);
                $this->em->persist($category);
            }
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
        ]);
    }
    
    /**
     * @Rest\Post("/update-active/{id}")
     * @param CourseCategory $courseCategory
     * @return Response
     */
    public function updateCourseCategoryActive(CourseCategory $courseCategory): Response
    {
        $courseCategory->setActive(!$courseCategory->isActive());
        $this->em->persist($courseCategory);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
        ]);
    }
}
