<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Admin\Traits\SerializerTrait;
use App\Entity\Chapter;
use App\Entity\Scorm;
use App\Event\ChapterContentChangedEvent;
use App\Service\Scorm\DownloadScormService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\Option\EA;
use EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\Field;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints\File;
use Vich\UploaderBundle\Form\Type\VichFileType;

class ScormCrudController extends AbstractCrudController
{
    use SerializerTrait;
    private $em;
    private $logger;
    private $requestStack;
    private $eventDispatcher;

    public function __construct(
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        EventDispatcherInterface $eventDispatcher
    ) {
        $this->em = $em;
        $this->logger = $logger;
        $this->requestStack = $requestStack;
        $this->eventDispatcher = $eventDispatcher;
    }

    public static function getEntityFqcn(): string
    {
        return Scorm::class;
    }

    public function configureFields(string $pageName): iterable
    {
        $scorm_package = Field::new('scormPackage')->setFormType(VichFileType::class)->setCustomOptions([
            'constraints' => [
                new File([
                    'maxSize' => '5M',
                    'mimeTypes' => [
                        'application/PNG',
                    ]
                ])
            ]
        ]);

        return [
            $scorm_package
        ];
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            ->remove(Crud::PAGE_NEW, Action::SAVE_AND_ADD_ANOTHER);
    }

    public function createEntity(string $entityFqc)
    {
        $scorm = new Scorm();

        $chapterRepository = $this->em->getRepository(Chapter::class);
        $chapter = $chapterRepository->find($this->requestStack->getCurrentRequest()->get('chapterId'));

        $scorm->setChapter($chapter);

        return $scorm;
    }

    public function delete(AdminContext $context)
    {
        $entity = $context->getEntity();

        $scormRepository = $this->em->getRepository(Scorm::class);
        $entity = $context->getEntity();
        $scorm = $scormRepository->find($entity->getPrimaryKeyValue());

        $folder = $this->getParameter('app.scorm_folders_path') . '/' . $scorm->getFolder();
        $this->deleteFolderScorm($folder);

        $responseParameters = parent::delete($context);

        return $responseParameters;
    }

    public function deleteFolderScorm($dir)
    {
        if (!file_exists($dir) || !is_dir($dir)) {
            return false;
        }

        $result = false;
        if ($handle = opendir("$dir")) {
            $result = true;
            while ((($file = readdir($handle)) !== false) && $result) {
                if ('.' != $file && '..' != $file) {
                    if (is_dir("$dir/$file")) {
                        $result = $this->deleteFolderScorm("$dir/$file");
                    } else {
                        $result = unlink("$dir/$file");
                    }
                }
            }
            closedir($handle);
            if ($result) {
                $result = rmdir($dir);
            }
        }

        return $result;
    }

    /**
     * @Route("/create-scorm", name="createScorm",methods={"POST"})
     *
     * @return Response
     */
    public function saveScormPackage(Request $request)
    {
        try {
            $idChapter = $request->get('chapter');
            $file = $request->files->get('file');

            if (!$file) {
                return $this->sendResponse([
                    'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                    'error' => true,
                    'data' => 'An error has occurred trying to register the scorms - Error: {No file provided}'
                ]);
            }

            $chapter = $this->em->getRepository(Chapter::class)->find($idChapter);

            if (!$chapter) {
                throw new \TypeError('Argument must be an instance of App\Entity\Chapter');
            }

            $scorm = new Scorm();
            $scorm->setChapter($chapter);
            $scorm->setFolder('');
            $scorm->setScormPackage($file);
            $scorm->setShowMenu(false);

            $this->em->persist($scorm);
            $this->em->flush();

            // Dispatch the event after the SCORM package is saved
            $this->eventDispatcher->dispatch(new ChapterContentChangedEvent($chapter), 'chapter.content_changed');

            $referrer = $this->get(AdminUrlGenerator::class)
                ->setController(CourseCrudController::class)
                ->setAction('detail')
                ->setEntityId($chapter->getCourse()->getId())
                ->generateUrl();

            $route = $this->get(AdminUrlGenerator::class)
                ->set('referrer', $referrer)
                ->setController(ChapterCrudController::class)
                ->setEntityId($chapter->getId())
                ->setAction(Action::EDIT)->generateUrl();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'route' => $route
            ]);
        } catch (\TypeError $e) {
            throw $e;
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the scorms - Error: {' . $e->getMessage() . '}'
            ]);
        }
    }

    public function downloadScorm(AdminContext $context, AdminUrlGenerator $adminUrlGenerator, DownloadScormService $downloadScormService)
    {
        $scormRepository = $this->em->getRepository(Scorm::class);
        $scorm = $scormRepository->find($context->getEntity()->getPrimaryKeyValue());
        $referrer = $context->getReferrer();
        if (!$scorm) {
            if ($referrer) {
                return $this->redirect($referrer);
            }

            return $this->redirect($adminUrlGenerator->setAction(Action::INDEX)->unset(EA::ENTITY_ID)->generateUrl());
        }
        $result = $downloadScormService->downloadFromChapter($scorm);
        if (!$result) {
            return new Response('Failed to download scorm zip file', Response::HTTP_BAD_REQUEST);
        }

        return $result;
    }

    /**
     * @Route("/admin/scorm/menu-visualization/{id}", name="scormMenuVisualization",methods={"POST"})
     *
     * @return Response
     */
    public function changeMenuVisualization(Scorm $scorm, Request $request)
    {
        try {
            $visible = $request->get('visible');
            $scorm->setShowMenu($visible);
            $this->em->flush();

            $response = [
                'status' => 200,
                'error' => false
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to change the scorm menu visualization - Error: {' . $e->getMessage() . '}'
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/scorm/activate-raw-score/{id}", name="scormActivateRawScore",methods={"POST"})
     *
     * @return Response
     */
    public function enableRawScore(Scorm $scorm, Request $request)
    {
        try {
            $active = $request->get('active');
            $rawScore = $request->get('rawScore');
            if ($active && '1' == $active) {
                $scorm->setRawScore($rawScore);
            } else {
                $scorm->resetRawScore();
            }
            $this->em->flush();

            $response = [
                'status' => 200,
                'error' => false
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to change the scorm menu visualization - Error: {' . $e->getMessage() . '}'
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/scorm/set-raw-score/{id}", name="scormSetRawScore",methods={"POST"})
     *
     * @return Response
     */
    public function setRawScore(Scorm $scorm, Request $request)
    {
        try {
            $rawScore = $request->get('rawScore');
            $scorm->setRawScore($rawScore);
            $this->em->flush();

            $response = [
                'status' => 200,
                'error' => false
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to change the scorm menu visualization - Error: {' . $e->getMessage() . '}'
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/scorm/allow-reset/{id}", name="scormAllowReset",methods={"POST"})
     *
     * @return Response
     */
    public function changeAllowReset(Scorm $scorm, Request $request)
    {
        try {
            $allowReset = $request->get('allowReset');
            $scorm->setAllowReset($allowReset);
            $this->em->flush();

            $response = [
                'status' => 200,
                'error' => false
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to change the scorm menu visualization - Error: {' . $e->getMessage() . '}'
            ];
        }

        return $this->sendResponse($response);
    }
}
