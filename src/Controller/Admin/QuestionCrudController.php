<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Admin\Field\VichImageField;
use App\Admin\Traits\DeleteFilePathTrait;
use App\Admin\Traits\SerializerTrait;
use App\Entity\Answer;
use App\Entity\Chapter;
use App\Entity\Question;
use App\Enum\Games;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\CollectionField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

class QuestionCrudController extends AbstractCrudController
{
    use DeleteFilePathTrait;
    use SerializerTrait;
    private $em;
    private $requestStack;
    private $logger;
    private $context;
    protected $translator;

    public function __construct(EntityManagerInterface $em, RequestStack $requestStack, LoggerInterface $logger, AdminContextProvider $context, TranslatorInterface $translator)
    {
        $this->em = $em;
        $this->requestStack = $requestStack;
        $this->logger = $logger;
        $this->context = $context;
        $this->translator = $translator;
    }

    public static function getEntityFqcn(): string
    {
        return Question::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            // ->overrideTemplate('crud/detail', 'admin/question/detail.html.twig')
            ->overrideTemplate('crud/detail', 'admin/question/app.html.twig')
            ->overrideTemplate('crud/new', 'admin/question/new.html.twig')
            ->overrideTemplate('crud/edit', 'admin/question/edit.html.twig');
    }

    public function configureFields(string $pageName): iterable
    {
        $chapterRepository = $this->em->getRepository(Chapter::class);
        $chapter = $chapterRepository->find($this->requestStack->getCurrentRequest()->get('chapterId'));

        $question = TextField::new('question', $this->translator->trans('question.configureFields.question', [], 'messages', $this->getUser()->getLocale()));
        $random = BooleanField::new('random', $this->translator->trans('question.configureFields.random', [], 'messages', $this->getUser()->getLocale()));
        $imageFile = VichImageField::new('imageFile', $this->translator->trans('question.configureFields.image_file', [], 'messages', $this->getUser()->getLocale()));
        if (Games::SECRET_WORD_TYPE == $chapter->getType()->getId()) {
            $answers = CollectionField::new('answers', $this->translator->trans('question.configureFields.answers', [], 'messages', $this->getUser()->getLocale()))->setEntryType('App\Form\Type\Admin\AnswerType')->addJsFiles('build/answerType.js');
        } else {
            $answers = CollectionField::new('answers', $this->translator->trans('question.configureFields.answers', [], 'messages', $this->getUser()->getLocale()))
                ->setEntryType('App\Form\Type\Admin\AnswerType')->addJsFiles('build/checkboxTypeQuestion.js');
        }

        return [
            $question, $random, $imageFile, $answers,
        ];
    }

    public function createEntity(string $entityFqc)
    {
        $question = new Question();

        $chapterRepository = $this->em->getRepository(Chapter::class);
        $chapter = $chapterRepository->find($this->requestStack->getCurrentRequest()->get('chapterId'));

        $question->setChapter($chapter);

        return $question;
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if ($this->requestStack->getCurrentRequest()->get('chapterId')) {
            $responseParameters->set('chapter', $this->em->getRepository(Chapter::class)->find($this->requestStack->getCurrentRequest()->get('chapterId')));
        }

        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName')) {
            $questionRepository = $this->em->getRepository(Question::class);
            $entity = $this->context->getContext()->getEntity();
            $question = $questionRepository->find($entity->getPrimaryKeyValue());

            $answerRepository = $this->em->getRepository(Answer::class);
            $answers = $answerRepository->findBy([
                'question' => $question,
            ]);

            $adminUrlGenerator = $this->get(AdminUrlGenerator::class);
            $referrer = $adminUrlGenerator
                ->unsetAll()
                ->setController(QuestionCrudController::class)
                ->setAction('detail')
                ->setEntityId($question->getId())
                ->generateUrl();

            $responseParameters->set('question', $question);
            $responseParameters->set('answers', $answers);
            $responseParameters->set('referrer', $referrer);
            $responseParameters->set('table_template', Answer::LIST_TEMPLATE);
        }

        return $responseParameters;
    }

    /**
     * @Route("/admin/save-question", name="save-question",methods={"POST"})
     *
     * @return Response
     */
    public function saveQuestion(Request $request)
    {
        try {
            $chapterId = $request->get('chapter');
            $question = $request->get('question');
            $random = $request->get('random');
            $answers = json_decode($request->get('answers'), true);
            $image = $request->files->get('image');
            $time = $request->get('time');

            $chapterRepository = $this->em->getRepository(Chapter::class);
            $chapter = $chapterRepository->find($chapterId);

            $ramdomValue = 'true' == $random ? true : false;

            $questionEntity = new Question();
            $questionEntity->setChapter($chapter);
            $questionEntity->setQuestion($question);
            $questionEntity->setRandom((bool) $ramdomValue);
            $questionEntity->setTime($time);

            $this->addFeedback($questionEntity, $request);

            if (null != $image) {
                $questionEntity->setImage('');
                $questionEntity->setImageFile($image);
            }

            $this->em->persist($questionEntity);

            foreach ($answers as $answer) {
                if ('' != $answer['answer']) {
                    $answerEntity = new Answer();
                    $answerEntity->setQuestion($questionEntity);
                    $answerEntity->setAnswer($answer['answer']);
                    $answerEntity->setCorrect($answer['correct']);
                    $this->em->persist($answerEntity);
                }
            }

            $this->em->flush();

            $response = [
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => [
                    'message' => 'Question saved successfully',
                    'route' => $this->urlChapter($chapterId),
                ],
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Ha ocurrido un error al guardar la pregunta: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }

    private function addFeedback(Question $question, $request)
    {
        $feedback = json_decode($request->get('feedback'), true);

        if (isset($feedback)) {
            $isFeedBack = 'true' == $feedback['isFeedback'] ? 1 : 0;
            $feedBackPositive = $feedback['correct'];
            $feedBackNegative = $feedback['incorrect'];

            $question->setIsFeedback((bool) $isFeedBack);
            $question->setFeedbackPositive($feedBackPositive);
            $question->setFeedbackNegative($feedBackNegative);

            return $question;
        }
    }

    /**
     * @Route("/admin/update-question", name="update-question-chapter",methods={"POST"})
     *
     * @return Response
     */
    public function updateQuestion(Request $request)
    {
        try {
            $chapterId = $request->get('chapter');
            $idQuestion = $request->get('idQuestion');
            $question = $request->get('question');
            $random = $request->get('random');
            $answers = json_decode($request->get('answers'), true);
            $image = $request->files->get('image');
            $answersDelete = json_decode($request->get('answersDelete'), true);
            $time = $request->get('time');

            $ramdomValue = 'true' == $random ? true : false;

            $questionEntity = $this->em->getRepository(Question::class)->find($idQuestion);
            $questionEntity->setQuestion($question);
            $questionEntity->setRandom($ramdomValue);
            $questionEntity->setTime($time);

            $this->addFeedback($questionEntity, $request);

            if (null != $image) {
                if (!\is_null($questionEntity->getImage())) {
                    $this->deleteFile($this->getParameter('app.question_uploads_path'), $questionEntity->getImage());
                }
                $questionEntity->setImage('');
                $questionEntity->setImageFile($image);
            }

            $this->em->persist($questionEntity);

            foreach ($answers as $answer) {
                if ('' != $answer['answer']) {
                    $answerEntity = $this->em->getRepository(Answer::class)->findOneBy(['id' => $answer['id'], 'question' => $idQuestion]);
                    if (!$answerEntity) {
                        $answerEntity = new Answer();
                        $answerEntity->setQuestion($questionEntity);
                        $answerEntity->setAnswer($answer['answer']);
                        $answerEntity->setCorrect($answer['correct']);
                    } else {
                        $answerEntity->setAnswer($answer['answer']);
                        $answerEntity->setCorrect($answer['correct']);
                    }

                    $this->em->persist($answerEntity);
                }
            }

            if (count($answersDelete) > 0) {
                foreach ($answersDelete as $answerDelete) {
                    $answerEntity = $this->em->getRepository(Answer::class)->findOneBy(['id' => $answerDelete['id'], 'question' => $idQuestion]);

                    $this->em->remove($answerEntity);
                }
            }

            $this->em->flush();

            $response = [
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => [
                    'message' => 'Question saved successfully',
                    'route' => $this->urlChapter($chapterId),
                ],
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Ha ocurrido un error al guardar la pregunta: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }

    private function urlChapter($chapterId)
    {
        $adminUrlGenerator = $this->get(AdminUrlGenerator::class);

        return $adminUrlGenerator
            ->unsetAll()
            ->setController(ChapterCrudController::class)
            ->setAction('edit')
            ->setEntityId($chapterId)
            ->generateUrl();
    }

    /**
     * @Route("/admin/list-questions/{id}", name="update-question",methods={"GET"})
     *
     * @return Response
     */
    public function getQuestionsByChapter(Chapter $chapter)
    {
        try {
            $questions = $chapter->getQuestions();
            $questionsArray = [];
            foreach ($questions as $question) {
                $questionsArray[] = [
                    'id' => $question->getId(),
                    'question' => $question->getQuestion(),
                    'random' => $question->getRandom(),
                    'image' => $question->getImage(),
                    'answers' => $question->getAnswers(),
                    'validated' => $question->getValidateQuestion(),
                    'chapter' => [
                        'id' => $question->getChapter()->getId(),
                        'title' => $question->getChapter()->getTitle(),
                        'type' => $question->getChapter()->getType()->getId(),
                    ],
                    'feedback' => [
                        'isFeedback' => $question->isIsFeedback() ?? false,
                        'correct' => $question->getFeedbackPositive() ?? '',
                        'incorrect' => $question->getFeedbackNegative() ?? '',
                    ],
                    'time' => $question->getQuestionTime(),
                    'maxQuestion' => $chapter->getMaxQuestion(),
                ];
            }

            $response = [
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => $questionsArray,
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Error to get question: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response, ['groups' => ['questions', 'results']]);
    }

    /**
     * @Route("/admin/delete-question", name="delete-question",methods={"POST"})
     *
     * @return Response
     */
    public function deleteQuestion(Request $request)
    {
        try {
            $requestData = json_decode($request->getContent(), true);
            $idQuestion = $requestData['idQuestion'];
            $idChapter = $requestData['idChapter'];
            $questionEntity = $this->em->getRepository(Question::class)->find($idQuestion);
            $this->em->remove($questionEntity);
            $this->em->flush();

            $response = [
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => [
                    'message' => 'Question deleted successfully',
                    'route' => $this->urlChapter($idChapter),
                ],
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'Error to delete question: {' . $e->getMessage() . '}',
            ];
        }

        return $this->sendResponse($response);
    }
}
