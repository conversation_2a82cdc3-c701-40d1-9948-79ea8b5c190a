<?php

namespace App\Controller\Admin;

use App\Service\SettingsService;
use KMS\FroalaEditorBundle\Service\MediaManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Routing\Annotation\Route;

class FroalaUploadController extends AbstractController
{
    private MediaManager $mediaManager;
    private string $rootDir;
    private SettingsService $settings;

    public function __construct(MediaManager $mediaManager, KernelInterface $kernel, SettingsService $settings)
    {
        $this->mediaManager = $mediaManager;
        $this->rootDir = $kernel->getProjectDir();
        $this->settings = $settings;
    }

    /**
     * @Route("/admin/froala/upload_image")
     * @param Request $request
     * @return Response
     */
    public function uploadImage(Request $request): Response {
        $folder = $this->settings->get('kms_froala_editor.imageUploadFolder');
        $path = $this->settings->get('kms_froala_editor.imageUploadPath');
        return $this->mediaManager->uploadImage($request->files, $this->rootDir, '/public', $request->getBasePath(), $folder, "$path/");
    }

    /**
     * @Route("/admin/froala/delete_image")
     * @param Request $request
     * @return Response
     */
    public function deleteImage(Request $request): Response {
        $imageSrc = urldecode($request->request->get('src'));
        $folder = $this->settings->get('kms_froala_editor.imageUploadFolder');
        $this->mediaManager->deleteImage($imageSrc, $this->rootDir, '/public', $folder);
        return new Response();
    }

    /**
     * @Route("/admin/froala/load_images")
     * @param Request $request
     * @return Response
     */
    public function loadImages(Request $request): Response {
        $folder = $this->settings->get('kms_froala_editor.imageUploadFolder');
        $path = $this->settings->get('kms_froala_editor.imageUploadPath');
        return $this->mediaManager->loadImages($this->rootDir, '/public', $request->getBasePath(), $folder, "$path/");
    }

    /**
     * @Route("/admin/froala/upload_file")
     * @param Request $request
     * @return Response
     */
    public function uploadFile(Request $request): Response {
        $folder = $this->settings->get('kms_froala_editor.fileUploadFolder');
        $path = $this->settings->get('kms_froala_editor.fileUploadPath');
        return $this->mediaManager->uploadFile($request->files, $this->rootDir, '/public', $request->getBasePath(), $folder, "$path/");
    }

    /**
     * @Route("/admin/froala/upload_video")
     * @param Request $request
     * @return Response
     */
    public function uploadVideo(Request $request): Response {
        $folder = $this->settings->get('kms_froala_editor.fileUploadFolder');
        $path = $this->settings->get('kms_froala_editor.fileUploadPath');
        return $this->mediaManager->uploadVideo($request->files, $this->rootDir, '/public', $request->getBasePath(), $folder, "$path/");
    }
}
