FROM debian:12-slim

RUN apt-get update && apt-get install -y \
      bash

# Install Apache
RUN apt-get update && apt-get install -y \
      apache2 \
      ssl-cert \
      vim \
      wget \
      openssl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Enable Apache modules
RUN a2enmod ssl headers rewrite proxy proxy_http proxy_fcgi

# Create a configuration file for OpenSSL with SANs
RUN echo "[req]" > /tmp/openssl.cnf && \
    echo "distinguished_name=req_distinguished_name" >> /tmp/openssl.cnf && \
    echo "[req_distinguished_name]" >> /tmp/openssl.cnf && \
    echo "[v3_req]" >> /tmp/openssl.cnf && \
    echo "subjectAltName=DNS:localhost,DNS:md-backend.local" >> /tmp/openssl.cnf \
    && make-ssl-cert generate-default-snakeoil --force-overwrite \
    && openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout /etc/ssl/private/apache-selfsigned.key \
        -out /etc/ssl/private/apache-selfsigned.crt \
        -subj "/C=US/ST=Denial/L=Springfield/O=Dis/CN=localhost" \
        -extensions v3_req -config /tmp/openssl.cnf \
    && rm /tmp/openssl.cnf \
    && chown -R www-data:www-data /etc/ssl/private/apache-selfsigned.key /etc/ssl/private/apache-selfsigned.crt \
    && chmod 600 /etc/ssl/private/apache-selfsigned.key /etc/ssl/private/apache-selfsigned.crt \
    && a2ensite default-ssl

# Start Apache
CMD ["apachectl", "-D", "FOREGROUND"]
